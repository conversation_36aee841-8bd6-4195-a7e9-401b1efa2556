Rails.application.routes.draw do
  # Legal pages
  get "terms" => "pages#terms"
  get "privacy" => "pages#privacy"
  
  # Waitlist page
  get "waitlist" => "pages#waitlist"
  
  # Support pages
  get "contact" => "contact#new", as: :new_contact
  post "contact" => "contact#create", as: :contact
  get "contact/success" => "contact#show", as: :contact_success
  get "faq" => "pages#faq"
  get "user-guide" => "pages#user_guide", as: :user_guide
  # Session routes
  get "sign-in" => "sessions#new", as: :new_session
  post "sign-in" => "sessions#create", as: :session
  delete "sign-out" => "sessions#destroy", as: :destroy_session
  resources :passwords, param: :token
  
  # Registration routes
  get "join" => "registrations#new", as: :new_registration
  get "join/artist" => "registrations#new_artist", as: :new_artist_registration
  get "join/client" => "registrations#new_client", as: :new_client_registration
  post "join" => "registrations#create", as: :registrations
  post "join/artist" => "registrations#create_artist", as: :artist_registrations
  post "join/client" => "registrations#create_client", as: :client_registrations
  get "join/:invite_code" => "registrations#new", as: :invite_registration
  
  # Artist profile routes - simplified
  resources :artists, param: :slug, only: [:show, :index, :edit, :update] do
    collection do
      get :load_more
    end
    member do
      get :portfolio
      get :tags
      get :inspiration
      get :following
      get :load_more_portfolio
      get :load_more_tags
      get :load_more_following
    end
    
    # Nested inspiration boards under artist profiles
    resources :inspiration_boards, path: 'inspiration/boards' do
      resources :inspiration_board_items, path: 'items', only: [:create, :update, :destroy]
    end
  end
  
  # Client profile routes  
  resources :clients, param: :slug, only: [:show, :index] do
    member do
      get :inspiration
      get :load_more_inspiration
    end
    
    
    # Nested inspiration boards under client profiles
    resources :inspiration_boards, path: 'inspiration/boards' do
      resources :inspiration_board_items, path: 'items', only: [:create, :update, :destroy]
    end
  end
  
  
  # Portfolio items management for artists
  resources :portfolio_items
  
  # Global inspiration boards browsing and user management
  get 'inspiration/browse' => 'inspiration_boards#browse_public', as: :browse_public_inspiration_boards
  get 'my/inspiration_boards' => 'inspiration_boards#index', as: :my_inspiration_boards
  post 'my/inspiration_boards' => 'inspiration_boards#create', as: :create_my_inspiration_board
  get 'my/inspiration_boards/new' => 'inspiration_boards#new', as: :new_my_inspiration_board
  get 'api/inspiration_boards/check_item' => 'inspiration_boards#check_item'
  resources :inspiration_board_items, only: [:create]

  
  # Admin routes
  namespace :admin do
    root "dashboard#index"
    get "dashboard" => "dashboard#index"
    
    resources :users do
      member do
        patch :approve
        patch :reject
      end
    end
    
    resources :specialties
    resource :settings, only: [:show, :edit, :update]
  end

  # Search
  get "search" => "search#index", as: :search

  # Pieces - portfolio items browsing
  get "pieces" => "pieces#index", as: :pieces
  get "pieces/load_more" => "pieces#load_more", as: :load_more_pieces

  # Home and discovery
  root "home#index"
  # get "discover" => "discover#index"

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
end
