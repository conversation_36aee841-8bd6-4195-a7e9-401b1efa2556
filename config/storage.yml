test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

staging:
  service: Disk
  root: <%= Rails.root.join("storage") %>

hetzner_staging:
  service: S3
  endpoint: "https://fsn1.your-objectstorage.com"
  access_key_id: <%= ENV["HETZNER_STAGING_ACCESS_KEY"] %>
  secret_access_key: <%= ENV["HETZNER_STAGING_SECRET_KEY"] %>
  region: fsn1
  bucket: yc-staging

hetzner:
  service: S3
  endpoint: "https://fsn1.your-objectstorage.com"
  access_key_id: <%= ENV["HETZNER_ACCESS_KEY"] %>
  secret_access_key: <%= ENV["HETZNER_SECRET_KEY"] %>
  region: fsn1
  bucket: glyph