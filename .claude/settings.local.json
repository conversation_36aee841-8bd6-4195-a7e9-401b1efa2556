{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(bundle install:*)", "Bash(rails generate:*)", "Bash(rails:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(bin/rails test:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(bin/rails generate migration:*)", "Bash(bin/rails:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(grep:*)", "Bash(find:*)", "<PERSON><PERSON>(exit)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "Bash(kill:*)", "<PERSON><PERSON>(mv:*)", "Bash(./bin/importmap:*)", "Bash(bundle exec rails runner:*)", "Bash(bundle exec rails server:*)", "Bash(bundle exec rails:*)", "<PERSON><PERSON>(true)", "Bash(ruby:*)", "WebFetch(domain:basecoatui.com)", "WebFetch(domain:basecoatui.com)", "Bash(rg:*)", "Bash(node:*)"], "deny": []}}