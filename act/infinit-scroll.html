<div data-controller="infinite-scroll" data-infinite-scroll-url-value="/posts/load_more" data-infinite-scroll-page-value="3" data-infinite-scroll-per-page-value="24">
      
      <div id="masonry-container" data-infinite-scroll-target="container" class="masonry-grid mb-8" data-masonry="" style="column-count: 4; column-gap: 1.5rem;">
        <!-- BEGIN app/views/posts/_post_cards.html.erb -->  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/12">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MywicHVyIjoiYmxvYl9pZCJ9fQ==--6c4231ddfd3e862d62802f41fd99e80547b1e2a6/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/female-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              sakuratanaka
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              2h ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Text Post -->
    <div class="p-4">
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Flash Friday Preview ⚡</p>

<p>This Friday I'm dropping some new flash designs! I've been working on a series of botanical pieces - think delicate florals with bold traditional elements. Perfect for those looking for something feminine but with that classic tattoo feel. Keep an eye on my story for the reveal!</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/11">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MywicHVyIjoiYmxvYl9pZCJ9fQ==--6c4231ddfd3e862d62802f41fd99e80547b1e2a6/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/female-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              sakuratanaka
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              4h ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="The client's reaction when they saw this finished piece was priceless." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MjYsInB1ciI6ImJsb2JfaWQifX0=--ff066909e5ce35af9a9b385d0eb54bb0a97bfc73/tn_photo-1561383621-d109918107aa.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>The client's reaction when they saw this finished piece was priceless.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/2">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MiwicHVyIjoiYmxvYl9pZCJ9fQ==--90af5a6213e7e03c3fa55bbaab2698e47bf4e085/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/male-3.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mikesmith
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              19h ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Abstract work allows for so much creative freedom and artistic expression." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MTgsInB1ciI6ImJsb2JfaWQifX0=--faedf795ebb50692f1b080fdc489e10264da8db4/tn_photo-1534367507873-d2d7e24c797f.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Abstract work allows for so much creative freedom and artistic expression.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/20">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NCwicHVyIjoiYmxvYl9pZCJ9fQ==--e84b15d4483130b347643217a39492963214058a/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              alexrivera
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              1d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Text Post -->
    <div class="p-4">
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Convention Season Thoughts 🎪</p>

<p>Just got back from an amazing convention weekend! Met so many talented artists and got to work on some incredible pieces. There's nothing quite like the energy of a tattoo convention - artists pushing each other to be better, clients getting dream pieces, and the whole community coming together. Already planning for the next one!</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/23">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NSwicHVyIjoiYmxvYl9pZCJ9fQ==--dfa7181c916df284dca275af27a1838ca5c32200/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mariagonzalez
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              1d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="The client's reaction when they saw this finished piece was priceless." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MzUsInB1ciI6ImJsb2JfaWQifX0=--e49147ab91dea0aec4f829485421ebb8e60b82cb/tn_photo-1627960630431-270d04164a22.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>The client's reaction when they saw this finished piece was priceless.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/13">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MywicHVyIjoiYmxvYl9pZCJ9fQ==--6c4231ddfd3e862d62802f41fd99e80547b1e2a6/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/female-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              sakuratanaka
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              1d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Text Post -->
    <div class="p-4">
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Flash Friday Preview ⚡</p>

<p>This Friday I'm dropping some new flash designs! I've been working on a series of botanical pieces - think delicate florals with bold traditional elements. Perfect for those looking for something feminine but with that classic tattoo feel. Keep an eye on my story for the reveal!</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/19">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NCwicHVyIjoiYmxvYl9pZCJ9fQ==--e84b15d4483130b347643217a39492963214058a/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              alexrivera
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              2d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Throwing it back to this piece from last month. Still one of my favorites!" loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MzIsInB1ciI6ImJsb2JfaWQifX0=--50dbacdbb837ac728e2ba946ae973480e3f3a345/tn_photo-1557286581-db6c124a6e2f.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Throwing it back to this piece from last month. Still one of my favorites!</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/18">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NCwicHVyIjoiYmxvYl9pZCJ9fQ==--e84b15d4483130b347643217a39492963214058a/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              alexrivera
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              2d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Work in progress shot - can't wait to finish this piece next session!" loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MzEsInB1ciI6ImJsb2JfaWQifX0=--7c8a35fbb4d67f0c54e746e7b16c7854df870126/tn_photo-1523264165578-20bfb5da52b5_1.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Work in progress shot - can't wait to finish this piece next session!</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/31">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NiwicHVyIjoiYmxvYl9pZCJ9fQ==--6adbdb8cf4dbca7600193c8d13166ffbfaccdb8f/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/male-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              davidchen
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              2d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Reference photos vs final result - love how this interpretation turned out." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NDEsInB1ciI6ImJsb2JfaWQifX0=--b1b30def497f80efe21ced3692da2a17b85dfc76/tn_photo-1704345911717-b9c422bf6ef0.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Reference photos vs final result - love how this interpretation turned out.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/10">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MywicHVyIjoiYmxvYl9pZCJ9fQ==--6c4231ddfd3e862d62802f41fd99e80547b1e2a6/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/female-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              sakuratanaka
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              2d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Work in progress shot - can't wait to finish this piece next session!" loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MjUsInB1ciI6ImJsb2JfaWQifX0=--83fa895837f75d8b4cf70e664f6b14c7925f0cb8/tn_photo-1513078094721-e7b6e0394a6a.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Work in progress shot - can't wait to finish this piece next session!</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/32">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NiwicHVyIjoiYmxvYl9pZCJ9fQ==--6adbdb8cf4dbca7600193c8d13166ffbfaccdb8f/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/male-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              davidchen
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              3d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Just finished this amazing session! The client was a trooper through the whole piece." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NDIsInB1ciI6ImJsb2JfaWQifX0=--fff7289e1b5757dd9f23883fe4d3ea65819e0e34/tn_photo-1568515045052-f9a854d70bfd.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Just finished this amazing session! The client was a trooper through the whole piece.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/4">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MiwicHVyIjoiYmxvYl9pZCJ9fQ==--90af5a6213e7e03c3fa55bbaab2698e47bf4e085/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/male-3.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mikesmith
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              3d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Touch-up session complete! Always happy to perfect the details." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MjAsInB1ciI6ImJsb2JfaWQifX0=--bb7b186b2e31fba0441425fbb765b153ac6ae1f6/tn_photo-1592402062271-11865c38baa1.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Touch-up session complete! Always happy to perfect the details.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/6">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MywicHVyIjoiYmxvYl9pZCJ9fQ==--6c4231ddfd3e862d62802f41fd99e80547b1e2a6/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/female-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              sakuratanaka
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              4d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Dotwork requires incredible patience but creates such unique texture and depth." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MjEsInB1ciI6ImJsb2JfaWQifX0=--f3e9d9b69c028f64d4b932548a4ae0801e5ad3ec/tn_photo-1575495407752-bfb6fb0518bf.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Dotwork requires incredible patience but creates such unique texture and depth.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/3">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MiwicHVyIjoiYmxvYl9pZCJ9fQ==--90af5a6213e7e03c3fa55bbaab2698e47bf4e085/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/male-3.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mikesmith
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              4d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Late night session but so worth it for this incredible piece." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MTksInB1ciI6ImJsb2JfaWQifX0=--1d1201d43faf526cbf8ff94f7b838882e8fb842e/tn_photo-1562962230-16e4623d36e6.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Late night session but so worth it for this incredible piece.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/25">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NSwicHVyIjoiYmxvYl9pZCJ9fQ==--dfa7181c916df284dca275af27a1838ca5c32200/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mariagonzalez
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              4d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Throwing it back to this piece from last month. Still one of my favorites!" loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MzcsInB1ciI6ImJsb2JfaWQifX0=--045e938df850c0b78ec4e89fceca3e14a237a105/tn_photo-1617196556242-d0de7c06a13e.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Throwing it back to this piece from last month. Still one of my favorites!</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/5">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MiwicHVyIjoiYmxvYl9pZCJ9fQ==--90af5a6213e7e03c3fa55bbaab2698e47bf4e085/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/male-3.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mikesmith
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              5d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Text Post -->
    <div class="p-4">
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Artist Spotlight: Traditional Tattooing</p>

<p>There's something magical about traditional tattooing that never gets old. The bold lines, the limited color palette, the timeless imagery - every piece tells a story that's been told for generations. I've been diving deep into the history of American traditional lately and it's incredible how much technique and artistry goes into what might look 'simple' to the untrained eye.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/24">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NSwicHVyIjoiYmxvYl9pZCJ9fQ==--dfa7181c916df284dca275af27a1838ca5c32200/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mariagonzalez
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              5d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Healing shots are always satisfying to see. This piece aged beautifully over the past month." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MzYsInB1ciI6ImJsb2JfaWQifX0=--501798983ef2b3cfacc567f46b59bac03064b8d5/tn_photo-1597722582304-9d1a7f3eb990.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Healing shots are always satisfying to see. This piece aged beautifully over the past month.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/7">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MywicHVyIjoiYmxvYl9pZCJ9fQ==--6c4231ddfd3e862d62802f41fd99e80547b1e2a6/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/female-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              sakuratanaka
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              6d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="This cover-up transformation exceeded all expectations - new life from old ink." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MjIsInB1ciI6ImJsb2JfaWQifX0=--45040d664fd04f74722155dd451b463a2435e16d/tn_photo-1522687533888-1078974f88ec.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>This cover-up transformation exceeded all expectations - new life from old ink.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/9">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MywicHVyIjoiYmxvYl9pZCJ9fQ==--6c4231ddfd3e862d62802f41fd99e80547b1e2a6/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/female-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              sakuratanaka
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              6d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Reference photos vs final result - love how this interpretation turned out." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MjQsInB1ciI6ImJsb2JfaWQifX0=--2e490a95a4afa77e15db046aebb61bebef68d475/tn_photo-1594091360183-4793f2dac6e6.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Reference photos vs final result - love how this interpretation turned out.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/21">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NSwicHVyIjoiYmxvYl9pZCJ9fQ==--dfa7181c916df284dca275af27a1838ca5c32200/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mariagonzalez
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              7d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="Custom work like this is why I love being a tattoo artist. Pure collaboration." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MzMsInB1ciI6ImJsb2JfaWQifX0=--03595ab548a7b9f7ab62a7553417291eb254bcfe/tn_photo-1605647533135-51b5906087d0.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Custom work like this is why I love being a tattoo artist. Pure collaboration.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/26">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NSwicHVyIjoiYmxvYl9pZCJ9fQ==--dfa7181c916df284dca275af27a1838ca5c32200/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mariagonzalez
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              8d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Text Post -->
    <div class="p-4">
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Studio Update 📍</p>

<p>Just wanted to give everyone a heads up that I'll be booking appointments for September starting next week! I've got some exciting flash designs in the works and can't wait to share them with you all. As always, feel free to reach out with any questions about the booking process.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/30">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NiwicHVyIjoiYmxvYl9pZCJ9fQ==--6adbdb8cf4dbca7600193c8d13166ffbfaccdb8f/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGVnIiwicmVzaXplX3RvX2ZpbGwiOlsyNCwyNF19LCJwdXIiOiJ2YXJpYXRpb24ifX0=--c5df3de7f754cb3c54c475bff36ec23505c1e2b1/male-1.jpeg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              davidchen
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              10d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="One of my favorite pieces from this year - the detail work really came together beautifully." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NDAsInB1ciI6ImJsb2JfaWQifX0=--216eb0328fcee41d1739e232a8f07c2b7f042468/tn_photo-1594091186701-13cd25dfda73.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>One of my favorite pieces from this year - the detail work really came together beautifully.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/22">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NSwicHVyIjoiYmxvYl9pZCJ9fQ==--dfa7181c916df284dca275af27a1838ca5c32200/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mariagonzalez
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              10d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Image Post -->
      <div class="relative">
        <img class="w-full object-cover" alt="One of my favorite pieces from this year - the detail work really came together beautifully." loading="lazy" src="http://localhost:3000/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsiZGF0YSI6MzQsInB1ciI6ImJsb2JfaWQifX0=--de144219860d800397842042a74a42455c9b8b66/tn_photo-1626215549618-f2f2aaff7d87.jpg">
      </div>

    <div class="p-4">
      <!-- Post Caption -->
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>One of my favorite pieces from this year - the detail work really came together beautifully.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
  <!-- BEGIN app/views/posts/_masonry_post_card.html.erb --><a class="block group" href="/posts/27">
  <div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300 mb-6 break-inside-avoid">
    <!-- Post Header -->
    <div class="px-4 pt-4 pb-2">
      <div class="flex items-center space-x-3">
        <!-- Author Avatar -->
        <div class="flex-shrink-0">
            <img class="w-6 h-6 rounded-full object-cover" src="http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsiZGF0YSI6NSwicHVyIjoiYmxvYl9pZCJ9fQ==--dfa7181c916df284dca275af27a1838ca5c32200/eyJfcmFpbHMiOnsiZGF0YSI6eyJmb3JtYXQiOiJqcGciLCJyZXNpemVfdG9fZmlsbCI6WzI0LDI0XX0sInB1ciI6InZhcmlhdGlvbiJ9fQ==--5808324706f6e0fa53e2dc3db263bd37bec41a82/female-2.jpg">
        </div>
        
        <!-- Author Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              mariagonzalez
            </p>
            <span class="text-gray-400">•</span>
            <span class="text-xs font-light text-gray-400 dark:text-gray-500">
              10d ago
            </span>
              <span class="inline-flex items-center" title="Verified Artist">
                <svg class="w-3 h-3 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </span>
          </div>
        </div>
      </div>

    </div>

  <!-- Post Content -->
    <!-- Text Post -->
    <div class="p-4">
        <div class="text-gray-900 dark:text-white text-sm mb-3">
          <p>Apprenticeship Memories 🎓</p>

<p>Thinking back to my apprenticeship days... man, those were tough but incredibly rewarding times. Cleaning tubes, setting up stations, watching every move my mentor made. To anyone starting their journey - soak up every bit of knowledge you can. The basics you learn now will serve you for your entire career.</p>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="p-3">
      <div class="flex items-center space-x-4">
        <!-- Comments Badge -->
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"></path>
          </svg>
          0
        </span>

        <!-- Save to Board Button -->

        <!-- Edit Button for Owner -->
      </div>
    </div>
  </div>
</a><!-- END app/views/posts/_masonry_post_card.html.erb -->
<!-- END app/views/posts/_post_cards.html.erb -->
      <!-- BEGIN app/views/layouts/application.html.erb -->

  
    <title>Glyph</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="csrf-param" content="authenticity_token">
<meta name="csrf-token" content="CbyRyhAGZQ3qIIEwh6VjXCUuWDgqsbUKdxGbU05ahqFTKWKCvSgncxXKTp4uKcu3TOVCT70ncT6osj57OJLmsw">
    

    


    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <link rel="stylesheet" href="/assets/actiontext-e646701d.css" data-turbo-track="reload">
<link rel="stylesheet" href="/assets/application-4feb7be9.css" data-turbo-track="reload">
<link rel="stylesheet" href="/assets/masonry-67583ea1.css" data-turbo-track="reload">
<link rel="stylesheet" href="/assets/tailwind-c2c2f21a.css" data-turbo-track="reload">
    
    
    <script type="importmap" data-turbo-track="reload">{
  "imports": {
    "application": "/assets/application-92a448e6.js",
    "@hotwired/turbo-rails": "/assets/turbo.min-3a2e143f.js",
    "@hotwired/stimulus": "/assets/stimulus.min-4b1e420e.js",
    "@hotwired/stimulus-loading": "/assets/stimulus-loading-1fc53fe7.js",
    "trix": "/assets/trix-4b540cb5.js",
    "@rails/actiontext": "/assets/actiontext.esm-f1c04d34.js",
    "controllers/application": "/assets/controllers/application-3affb389.js",
    "controllers/command_palette_controller": "/assets/controllers/command_palette_controller-b8182922.js",
    "controllers/hello_controller": "/assets/controllers/hello_controller-708796bd.js",
    "controllers": "/assets/controllers/index-ee64e1f1.js",
    "controllers/infinite_scroll_controller": "/assets/controllers/infinite_scroll_controller-3632a6a9.js",
    "controllers/navigation_controller": "/assets/controllers/navigation_controller-7a8d3df4.js",
    "controllers/save_to_board_controller": "/assets/controllers/save_to_board_controller-806217e3.js",
    "controllers/universal_sortable_controller": "/assets/controllers/universal_sortable_controller-0e8359a5.js",
    "custom/comment_collapse": "/assets/custom/comment_collapse-c0db430b.js",
    "custom/dark_mode": "/assets/custom/dark_mode-6a642271.js",
    "custom/inspiration_boards": "/assets/custom/inspiration_boards-fa1b9a73.js",
    "custom/masonry": "/assets/custom/masonry-1150f5fa.js",
    "custom/post_type_toggle": "/assets/custom/post_type_toggle-b2b2e5b5.js",
    "custom/tailwind_elements": "/assets/custom/tailwind_elements-534f81b1.js",
    "custom/threaded_comments": "/assets/custom/threaded_comments-0412df7b.js",
    "custom/voting": "/assets/custom/voting-bd1e8c78.js"
  }
}</script>
<link rel="modulepreload" href="/assets/application-92a448e6.js">
<link rel="modulepreload" href="/assets/turbo.min-3a2e143f.js">
<link rel="modulepreload" href="/assets/stimulus.min-4b1e420e.js">
<link rel="modulepreload" href="/assets/stimulus-loading-1fc53fe7.js">
<link rel="modulepreload" href="/assets/trix-4b540cb5.js">
<link rel="modulepreload" href="/assets/actiontext.esm-f1c04d34.js">
<link rel="modulepreload" href="/assets/controllers/application-3affb389.js">
<link rel="modulepreload" href="/assets/controllers/command_palette_controller-b8182922.js">
<link rel="modulepreload" href="/assets/controllers/hello_controller-708796bd.js">
<link rel="modulepreload" href="/assets/controllers/index-ee64e1f1.js">
<link rel="modulepreload" href="/assets/controllers/infinite_scroll_controller-3632a6a9.js">
<link rel="modulepreload" href="/assets/controllers/navigation_controller-7a8d3df4.js">
<link rel="modulepreload" href="/assets/controllers/save_to_board_controller-806217e3.js">
<link rel="modulepreload" href="/assets/controllers/universal_sortable_controller-0e8359a5.js">
<link rel="modulepreload" href="/assets/custom/comment_collapse-c0db430b.js">
<link rel="modulepreload" href="/assets/custom/dark_mode-6a642271.js">
<link rel="modulepreload" href="/assets/custom/inspiration_boards-fa1b9a73.js">
<link rel="modulepreload" href="/assets/custom/masonry-1150f5fa.js">
<link rel="modulepreload" href="/assets/custom/post_type_toggle-b2b2e5b5.js">
<link rel="modulepreload" href="/assets/custom/tailwind_elements-534f81b1.js">
<link rel="modulepreload" href="/assets/custom/threaded_comments-0412df7b.js">
<link rel="modulepreload" href="/assets/custom/voting-bd1e8c78.js">
<script type="module">import "application"</script>
    
    <!-- Dark mode initialization -->
    <script>
      // Initialize dark mode based on system preference or stored preference
      if (localStorage.getItem('tattoo-marketplace-theme') === 'dark' || (!localStorage.getItem('tattoo-marketplace-theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    </script>
    <meta name="sentry-trace" content="8571a74f1f214c4c87cfd7e87803570a-78ee68bd156f4f0f">
<meta name="baggage" content="sentry-trace_id=8571a74f1f214c4c87cfd7e87803570a,sentry-environment=development,sentry-public_key=585776ce6a89cc69e8549f6c80893830">
  

  
    <!-- Sidebar Navigation -->
    <!-- BEGIN app/views/shared/_sidebar_navigation.html.erb --><!-- Sidebar -->
<div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col transition-transform duration-300 ease-in-out" id="sidebar">
  <!-- Sidebar component -->
  <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-black border-r border-gray-300 dark:border-gray-600 px-6 pb-4">
    <div class="flex h-16 shrink-0 items-center justify-between">
      <a class="flex items-center" href="/">
        <!-- Glyph Logo -->
        <div class="h-8 w-8 bg-black dark:bg-white rounded-md flex items-center justify-center">
          <span class="text-xl font-bold text-white dark:text-black">G</span>
        </div>
        <span class="ml-3 text-xl font-medium text-black dark:text-white">Glyph</span>
</a>      
    </div>
    <nav class="flex flex-1 flex-col">
      <ul role="list" class="flex flex-1 flex-col gap-y-7">
        <li>
          <ul role="list" class="-mx-2 space-y-1">
            <!-- Home Link -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
                </svg>
                Home
</a>            </li>
            
            <!-- Artists -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/artists">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
                </svg>
                Artists
</a>            </li>

            <!-- Posts -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/posts">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></path>
                </svg>
                Posts
</a>            </li>

            <!-- Forums -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/threads">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"></path>
                </svg>
                Forums
</a>            </li>

            <!-- Inspiration -->

            <!-- Search -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/search">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
                </svg>
                Search
</a>            </li>

          </ul>
        </li>
        
        <!-- Authenticated User Links Section -->
        
        <!-- User Profile Section -->
          <!-- Sign in/Join buttons for non-authenticated users -->
          <li class="mt-auto">
            <div class="space-y-3">
              <a class="block w-full text-center text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white hover:underline transition-colors py-2" href="/sign-in">Sign in</a>
              <a class="block w-full px-4 py-3 text-center bg-black dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 rounded-md transition-colors font-medium" href="/join">Join</a>
            </div>
          </li>
      </ul>
    </nav>
  </div>
</div>

<!-- Mobile menu overlay -->
<div class="lg:hidden fixed inset-0 flex z-50 transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-menu">
  <!-- Off-canvas menu -->
  <div class="fixed inset-0 flex z-40">
    <div class="fixed inset-0 bg-black/50" id="mobile-backdrop"></div>
    
    <div class="relative flex-1 flex flex-col max-w-xs w-full pt-5 pb-4 bg-white dark:bg-black">
      <div class="absolute top-0 right-0 -mr-12 pt-2">
        <button type="button" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" id="mobile-close-btn">
          <span class="sr-only">Close sidebar</span>
          <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="flex-shrink-0 flex items-center px-4">
        <a class="flex items-center" href="/">
          <div class="h-8 w-8 bg-black dark:bg-white rounded-md flex items-center justify-center">
            <span class="text-xl font-bold text-white dark:text-black">G</span>
          </div>
          <span class="ml-3 text-xl font-medium text-black dark:text-white">Glyph</span>
</a>      </div>
      
      <div class="mt-5 flex-1 h-0 overflow-y-auto">
        <nav class="px-2 space-y-1">
          <!-- Home Link -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
            </svg>
            Home
</a>          
          <!-- Artists -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/artists">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
            Artists
</a>
          <!-- Posts -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/posts">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></path>
            </svg>
            Posts
</a>
          <!-- Forums -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/threads">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"></path>
            </svg>
            Forums
</a>
          <!-- Inspiration -->

          <!-- Search -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/search">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
            </svg>
            Search
</a>
          <!-- Divider for authenticated user links -->

          <!-- Theme Toggle -->
          <button type="button" class="w-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md text-left" data-dark-mode-toggle="">
            <!-- Moon icon (shown when light mode is active) -->
            <svg data-dark-icon="" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: block;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"></path>
            </svg>
            <!-- Sun icon (shown when dark mode is active) -->
            <svg data-light-icon="" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: none;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"></path>
            </svg>
            Theme
          </button>
        </nav>
      </div>
    </div>
  </div>
</div><!-- END app/views/shared/_sidebar_navigation.html.erb -->

    <!-- Top Navigation -->
    <!-- BEGIN app/views/shared/_top_navigation.html.erb --><!-- Top Navigation Bar -->
<div class="lg:pl-72 transition-all duration-300 ease-in-out" id="top-nav">
  <div class="sticky top-0 z-40 bg-white dark:bg-black border-b border-gray-300 dark:border-gray-600 flex items-center justify-between h-12 px-4">
    
    <!-- Left Side: Sidebar Toggle (Mobile + Desktop) -->
    <div class="flex items-center">
      <!-- Mobile sidebar toggle -->
      <button type="button" class="lg:hidden -m-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" id="mobile-menu-toggle">
        <span class="sr-only">Open sidebar</span>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
        </svg>
      </button>
      
      <!-- Desktop sidebar toggle -->
      <button type="button" class="hidden lg:flex -m-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" id="desktop-sidebar-toggle" title="Toggle Sidebar">
        <span class="sr-only">Toggle sidebar</span>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5"></path>
        </svg>
      </button>
    </div>

    <!-- Right Side: Command Palette + Theme Toggle -->
    <div class="flex items-center gap-x-4">
      
      <!-- Command Palette Shortcut -->
      <button type="button" onclick="document.querySelector('[data-controller*=\" command-palette\"]="" dialog').showmodal();="" document.queryselector('[data-controller*="\&quot;command-palette\&quot;]" input').focus()"="" class="flex items-center gap-x-2 rounded-md border border-gray-200 dark:border-gray-700 px-2 py-1 text-xs text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-900 hover:text-gray-700 dark:hover:text-gray-300 transition-colors" title="Open command palette (⌘K)">
        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
        </svg>
        <span>⌘K</span>
      </button>

      <!-- Theme Toggle -->
      <button type="button" class="-m-2 p-2.5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" data-dark-mode-toggle="" title="Toggle theme">
        <span class="sr-only">Toggle theme</span>
        <!-- Moon icon (shown when light mode is active) -->
        <svg data-dark-icon="" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: block;">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"></path>
        </svg>
        <!-- Sun icon (shown when dark mode is active) -->
        <svg data-light-icon="" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: none;">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"></path>
        </svg>
      </button>
      
    </div>
  </div>
</div><!-- END app/views/shared/_top_navigation.html.erb -->

    <!-- Main Content -->
    <main class="lg:pl-72 flex-1 transition-all duration-300 ease-in-out" id="main-content">
      <div class="px-4 sm:px-6 lg:px-8 py-8">
        <!-- BEGIN app/views/sessions/new.html.erb --><div class="min-h-screen bg-white dark:bg-black flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
      Sign in to your account
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
      Welcome back to Tattoo Marketplace
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="border border-gray-300 dark:border-gray-600 py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
      <!-- Flash Messages -->


      <form class="form space-y-6" action="/sign-in" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="SVUdoNnWysEXx89Lbxm6IE2GRzltsr16zyWglydvFBmpYP9ndG7CdjY9T21mBesHkCcvTLc8PDFm10B6PfMfEA" autocomplete="off">
        <div class="grid gap-3">
          <label class="block text-xs font-medium text-gray-900 dark:text-white" for="login">Email or Username</label>
          <input required="required" autofocus="autofocus" autocomplete="username" placeholder="Enter your email or username" class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" type="text" name="login" id="login">
        <div data-lastpass-icon-root="" style="position: relative !important; height: 0px !important; width: 0px !important; float: left !important;"></div></div>

        <div class="grid gap-3">
          <label class="block text-xs font-medium text-gray-900 dark:text-white" for="password">Password</label>
          <input required="required" autocomplete="current-password" placeholder="Enter your password" maxlength="72" class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" size="72" type="password" name="password" id="password">
        <div data-lastpass-icon-root="" style="position: relative !important; height: 0px !important; width: 0px !important; float: left !important;"></div></div>

        <div class="flex items-center justify-between">
          <div class="text-sm">
            <a class="font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" href="/passwords/new">Forgot your password?</a>
          </div>
        </div>

        <div>
          <input type="submit" name="commit" value="Sign in" class="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400" data-disable-with="Sign in">
        </div>

        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Don't have an account?
            <a class="font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" href="/join">Join now</a>
          </p>
        </div>
</form>    </div>
  </div>
</div>
<!-- END app/views/sessions/new.html.erb -->
      </div>
    </main>
    
    <!-- Footer -->
    <footer class="lg:pl-72 bg-white dark:bg-black border-t border-gray-300 dark:border-gray-600 mt-auto transition-all duration-300 ease-in-out" id="footer">
      <div class="px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            © 2025 Glyph. All rights reserved.
          </p>
          <div class="mt-4 sm:mt-0 flex space-x-6">
            <a class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" href="/terms">Terms of Service</a>
            <a class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" href="/privacy">Privacy Policy</a>
          </div>
        </div>
      </div>
    </footer>
    

    <!-- Command Palette -->
    <!-- BEGIN app/views/shared/_command_palette.html.erb --><!-- Command Palette -->
<div data-controller="command-palette" class="relative z-50">
  <dialog data-command-palette-target="dialog" class="backdrop:bg-transparent fixed inset-0 z-50 overflow-y-auto">
    <div class="fixed inset-0 bg-black/25 transition-opacity dark:bg-black/50"></div>
    
    <div tabindex="0" class="fixed inset-0 w-screen overflow-y-auto p-4 focus:outline-none sm:p-6 md:p-20">
      <div class="mx-auto block max-w-xl transform overflow-hidden rounded-xl bg-white shadow-2xl outline-1 outline-black/5 transition-all dark:bg-black dark:-outline-offset-1 dark:outline-white/10">
        <div>
          <div class="grid grid-cols-1 border-b border-gray-300 dark:border-gray-600">
            <input type="text" autofocus="" placeholder="Search for actions, pages, or features..." data-command-palette-target="input" data-action="input-&gt;command-palette#search keydown-&gt;command-palette#handleListKeyDown" class="col-start-1 row-start-1 h-12 w-full pr-4 pl-11 text-base text-black outline-hidden placeholder:text-gray-400 sm:text-sm dark:bg-black dark:text-white dark:placeholder:text-gray-500">
            <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="pointer-events-none col-start-1 row-start-1 ml-4 size-5 self-center text-gray-400 dark:text-gray-400">
              <path d="M9 3.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11ZM2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Z" clip-rule="evenodd" fill-rule="evenodd"></path>
            </svg>
          </div>

          <div data-command-palette-target="list" class="block max-h-72 scroll-py-2 overflow-y-auto py-2 text-sm text-black dark:text-white" hidden="">
            <!-- Actions will be dynamically inserted here -->
          </div>

          <div data-command-palette-target="noResults" class="block p-4 text-center text-sm text-gray-600 dark:text-gray-400" hidden="">
            <div class="flex flex-col items-center justify-center py-6">
              <svg class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
              </svg>
              <p class="text-black dark:text-white font-medium">No results found</p>
              <p class="text-gray-600 dark:text-gray-400 text-xs mt-1">Try searching with different keywords</p>
            </div>
          </div>

          <!-- Help text at bottom -->
          <div class="border-t border-gray-300 dark:border-gray-600 px-4 py-2 text-xs text-gray-600 dark:text-gray-400">
            <div class="flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <span>Press <kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">↑↓</kbd> to navigate</span>
                <span><kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">↵</kbd> to select</span>
                <span><kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">esc</kbd> twice to close</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </dialog>
</div><!-- END app/views/shared/_command_palette.html.erb -->

    <!-- Save to Board Modal -->
  

<!-- END app/views/layouts/application.html.erb --><!-- BEGIN app/views/layouts/application.html.erb -->

  
    <title>Glyph</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="csrf-param" content="authenticity_token">
<meta name="csrf-token" content="vZaJfDsB7w4Nm1OquHhEO0oZPkxEApBSmep-1xKWG0DnA3o0li-tcPJxnAQR9OzQI9IkO9OUVGZGSdv_ZF57Ug">
    

    


    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <link rel="stylesheet" href="/assets/actiontext-e646701d.css" data-turbo-track="reload">
<link rel="stylesheet" href="/assets/application-4feb7be9.css" data-turbo-track="reload">
<link rel="stylesheet" href="/assets/masonry-67583ea1.css" data-turbo-track="reload">
<link rel="stylesheet" href="/assets/tailwind-c2c2f21a.css" data-turbo-track="reload">
    
    
    <script type="importmap" data-turbo-track="reload">{
  "imports": {
    "application": "/assets/application-92a448e6.js",
    "@hotwired/turbo-rails": "/assets/turbo.min-3a2e143f.js",
    "@hotwired/stimulus": "/assets/stimulus.min-4b1e420e.js",
    "@hotwired/stimulus-loading": "/assets/stimulus-loading-1fc53fe7.js",
    "trix": "/assets/trix-4b540cb5.js",
    "@rails/actiontext": "/assets/actiontext.esm-f1c04d34.js",
    "controllers/application": "/assets/controllers/application-3affb389.js",
    "controllers/command_palette_controller": "/assets/controllers/command_palette_controller-b8182922.js",
    "controllers/hello_controller": "/assets/controllers/hello_controller-708796bd.js",
    "controllers": "/assets/controllers/index-ee64e1f1.js",
    "controllers/infinite_scroll_controller": "/assets/controllers/infinite_scroll_controller-3632a6a9.js",
    "controllers/navigation_controller": "/assets/controllers/navigation_controller-7a8d3df4.js",
    "controllers/save_to_board_controller": "/assets/controllers/save_to_board_controller-806217e3.js",
    "controllers/universal_sortable_controller": "/assets/controllers/universal_sortable_controller-0e8359a5.js",
    "custom/comment_collapse": "/assets/custom/comment_collapse-c0db430b.js",
    "custom/dark_mode": "/assets/custom/dark_mode-6a642271.js",
    "custom/inspiration_boards": "/assets/custom/inspiration_boards-fa1b9a73.js",
    "custom/masonry": "/assets/custom/masonry-1150f5fa.js",
    "custom/post_type_toggle": "/assets/custom/post_type_toggle-b2b2e5b5.js",
    "custom/tailwind_elements": "/assets/custom/tailwind_elements-534f81b1.js",
    "custom/threaded_comments": "/assets/custom/threaded_comments-0412df7b.js",
    "custom/voting": "/assets/custom/voting-bd1e8c78.js"
  }
}</script>
<link rel="modulepreload" href="/assets/application-92a448e6.js">
<link rel="modulepreload" href="/assets/turbo.min-3a2e143f.js">
<link rel="modulepreload" href="/assets/stimulus.min-4b1e420e.js">
<link rel="modulepreload" href="/assets/stimulus-loading-1fc53fe7.js">
<link rel="modulepreload" href="/assets/trix-4b540cb5.js">
<link rel="modulepreload" href="/assets/actiontext.esm-f1c04d34.js">
<link rel="modulepreload" href="/assets/controllers/application-3affb389.js">
<link rel="modulepreload" href="/assets/controllers/command_palette_controller-b8182922.js">
<link rel="modulepreload" href="/assets/controllers/hello_controller-708796bd.js">
<link rel="modulepreload" href="/assets/controllers/index-ee64e1f1.js">
<link rel="modulepreload" href="/assets/controllers/infinite_scroll_controller-3632a6a9.js">
<link rel="modulepreload" href="/assets/controllers/navigation_controller-7a8d3df4.js">
<link rel="modulepreload" href="/assets/controllers/save_to_board_controller-806217e3.js">
<link rel="modulepreload" href="/assets/controllers/universal_sortable_controller-0e8359a5.js">
<link rel="modulepreload" href="/assets/custom/comment_collapse-c0db430b.js">
<link rel="modulepreload" href="/assets/custom/dark_mode-6a642271.js">
<link rel="modulepreload" href="/assets/custom/inspiration_boards-fa1b9a73.js">
<link rel="modulepreload" href="/assets/custom/masonry-1150f5fa.js">
<link rel="modulepreload" href="/assets/custom/post_type_toggle-b2b2e5b5.js">
<link rel="modulepreload" href="/assets/custom/tailwind_elements-534f81b1.js">
<link rel="modulepreload" href="/assets/custom/threaded_comments-0412df7b.js">
<link rel="modulepreload" href="/assets/custom/voting-bd1e8c78.js">
<script type="module">import "application"</script>
    
    <!-- Dark mode initialization -->
    <script>
      // Initialize dark mode based on system preference or stored preference
      if (localStorage.getItem('tattoo-marketplace-theme') === 'dark' || (!localStorage.getItem('tattoo-marketplace-theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    </script>
    <meta name="sentry-trace" content="76bce845f54844048ba3012c6e260940-679f0208ab334fdb">
<meta name="baggage" content="sentry-trace_id=76bce845f54844048ba3012c6e260940,sentry-environment=development,sentry-public_key=585776ce6a89cc69e8549f6c80893830">
  

  
    <!-- Sidebar Navigation -->
    <!-- BEGIN app/views/shared/_sidebar_navigation.html.erb --><!-- Sidebar -->
<div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col transition-transform duration-300 ease-in-out" id="sidebar">
  <!-- Sidebar component -->
  <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-black border-r border-gray-300 dark:border-gray-600 px-6 pb-4">
    <div class="flex h-16 shrink-0 items-center justify-between">
      <a class="flex items-center" href="/">
        <!-- Glyph Logo -->
        <div class="h-8 w-8 bg-black dark:bg-white rounded-md flex items-center justify-center">
          <span class="text-xl font-bold text-white dark:text-black">G</span>
        </div>
        <span class="ml-3 text-xl font-medium text-black dark:text-white">Glyph</span>
</a>      
    </div>
    <nav class="flex flex-1 flex-col">
      <ul role="list" class="flex flex-1 flex-col gap-y-7">
        <li>
          <ul role="list" class="-mx-2 space-y-1">
            <!-- Home Link -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
                </svg>
                Home
</a>            </li>
            
            <!-- Artists -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/artists">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
                </svg>
                Artists
</a>            </li>

            <!-- Posts -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/posts">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></path>
                </svg>
                Posts
</a>            </li>

            <!-- Forums -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/threads">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"></path>
                </svg>
                Forums
</a>            </li>

            <!-- Inspiration -->

            <!-- Search -->
            <li>
              <a class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900 font-medium group flex gap-x-3 rounded-md p-2 text-sm leading-6" href="/search">
                <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
                </svg>
                Search
</a>            </li>

          </ul>
        </li>
        
        <!-- Authenticated User Links Section -->
        
        <!-- User Profile Section -->
          <!-- Sign in/Join buttons for non-authenticated users -->
          <li class="mt-auto">
            <div class="space-y-3">
              <a class="block w-full text-center text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white hover:underline transition-colors py-2" href="/sign-in">Sign in</a>
              <a class="block w-full px-4 py-3 text-center bg-black dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 rounded-md transition-colors font-medium" href="/join">Join</a>
            </div>
          </li>
      </ul>
    </nav>
  </div>
</div>

<!-- Mobile menu overlay -->
<div class="lg:hidden fixed inset-0 flex z-50 transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-menu">
  <!-- Off-canvas menu -->
  <div class="fixed inset-0 flex z-40">
    <div class="fixed inset-0 bg-black/50" id="mobile-backdrop"></div>
    
    <div class="relative flex-1 flex flex-col max-w-xs w-full pt-5 pb-4 bg-white dark:bg-black">
      <div class="absolute top-0 right-0 -mr-12 pt-2">
        <button type="button" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" id="mobile-close-btn">
          <span class="sr-only">Close sidebar</span>
          <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="flex-shrink-0 flex items-center px-4">
        <a class="flex items-center" href="/">
          <div class="h-8 w-8 bg-black dark:bg-white rounded-md flex items-center justify-center">
            <span class="text-xl font-bold text-white dark:text-black">G</span>
          </div>
          <span class="ml-3 text-xl font-medium text-black dark:text-white">Glyph</span>
</a>      </div>
      
      <div class="mt-5 flex-1 h-0 overflow-y-auto">
        <nav class="px-2 space-y-1">
          <!-- Home Link -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
            </svg>
            Home
</a>          
          <!-- Artists -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/artists">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
            Artists
</a>
          <!-- Posts -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/posts">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></path>
            </svg>
            Posts
</a>
          <!-- Forums -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/threads">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"></path>
            </svg>
            Forums
</a>
          <!-- Inspiration -->

          <!-- Search -->
          <a class="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" href="/search">
            <svg class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
            </svg>
            Search
</a>
          <!-- Divider for authenticated user links -->

          <!-- Theme Toggle -->
          <button type="button" class="w-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white group flex items-center px-2 py-2 text-base font-medium rounded-md text-left" data-dark-mode-toggle="">
            <!-- Moon icon (shown when light mode is active) -->
            <svg data-dark-icon="" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: block;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"></path>
            </svg>
            <!-- Sun icon (shown when dark mode is active) -->
            <svg data-light-icon="" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: none;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"></path>
            </svg>
            Theme
          </button>
        </nav>
      </div>
    </div>
  </div>
</div><!-- END app/views/shared/_sidebar_navigation.html.erb -->

    <!-- Top Navigation -->
    <!-- BEGIN app/views/shared/_top_navigation.html.erb --><!-- Top Navigation Bar -->
<div class="lg:pl-72 transition-all duration-300 ease-in-out" id="top-nav">
  <div class="sticky top-0 z-40 bg-white dark:bg-black border-b border-gray-300 dark:border-gray-600 flex items-center justify-between h-12 px-4">
    
    <!-- Left Side: Sidebar Toggle (Mobile + Desktop) -->
    <div class="flex items-center">
      <!-- Mobile sidebar toggle -->
      <button type="button" class="lg:hidden -m-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" id="mobile-menu-toggle">
        <span class="sr-only">Open sidebar</span>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
        </svg>
      </button>
      
      <!-- Desktop sidebar toggle -->
      <button type="button" class="hidden lg:flex -m-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" id="desktop-sidebar-toggle" title="Toggle Sidebar">
        <span class="sr-only">Toggle sidebar</span>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5"></path>
        </svg>
      </button>
    </div>

    <!-- Right Side: Command Palette + Theme Toggle -->
    <div class="flex items-center gap-x-4">
      
      <!-- Command Palette Shortcut -->
      <button type="button" onclick="document.querySelector('[data-controller*=\" command-palette\"]="" dialog').showmodal();="" document.queryselector('[data-controller*="\&quot;command-palette\&quot;]" input').focus()"="" class="flex items-center gap-x-2 rounded-md border border-gray-200 dark:border-gray-700 px-2 py-1 text-xs text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-900 hover:text-gray-700 dark:hover:text-gray-300 transition-colors" title="Open command palette (⌘K)">
        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
        </svg>
        <span>⌘K</span>
      </button>

      <!-- Theme Toggle -->
      <button type="button" class="-m-2 p-2.5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" data-dark-mode-toggle="" title="Toggle theme">
        <span class="sr-only">Toggle theme</span>
        <!-- Moon icon (shown when light mode is active) -->
        <svg data-dark-icon="" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: block;">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"></path>
        </svg>
        <!-- Sun icon (shown when dark mode is active) -->
        <svg data-light-icon="" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: none;">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"></path>
        </svg>
      </button>
      
    </div>
  </div>
</div><!-- END app/views/shared/_top_navigation.html.erb -->

    <!-- Main Content -->
    <main class="lg:pl-72 flex-1 transition-all duration-300 ease-in-out" id="main-content">
      <div class="px-4 sm:px-6 lg:px-8 py-8">
        <!-- BEGIN app/views/sessions/new.html.erb --><div class="min-h-screen bg-white dark:bg-black flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
      Sign in to your account
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
      Welcome back to Tattoo Marketplace
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="border border-gray-300 dark:border-gray-600 py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
      <!-- Flash Messages -->


      <form class="form space-y-6" action="/sign-in" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="0HDtvMLJDxbSVxQGWbJ7qDn6MF8FF-WucQtdGbTJlK8wRQ97b3EHofOtlCBQriqP5FtYKt-ZZOXY-b30rlWfpg" autocomplete="off">
        <div class="grid gap-3">
          <label class="block text-xs font-medium text-gray-900 dark:text-white" for="login">Email or Username</label>
          <input required="required" autofocus="autofocus" autocomplete="username" placeholder="Enter your email or username" class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" type="text" name="login" id="login">
        <div data-lastpass-icon-root="" style="position: relative !important; height: 0px !important; width: 0px !important; float: left !important;"></div></div>

        <div class="grid gap-3">
          <label class="block text-xs font-medium text-gray-900 dark:text-white" for="password">Password</label>
          <input required="required" autocomplete="current-password" placeholder="Enter your password" maxlength="72" class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" size="72" type="password" name="password" id="password">
        <div data-lastpass-icon-root="" style="position: relative !important; height: 0px !important; width: 0px !important; float: left !important;"></div></div>

        <div class="flex items-center justify-between">
          <div class="text-sm">
            <a class="font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" href="/passwords/new">Forgot your password?</a>
          </div>
        </div>

        <div>
          <input type="submit" name="commit" value="Sign in" class="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400" data-disable-with="Sign in">
        </div>

        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Don't have an account?
            <a class="font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" href="/join">Join now</a>
          </p>
        </div>
</form>    </div>
  </div>
</div>
<!-- END app/views/sessions/new.html.erb -->
      </div>
    </main>
    
    <!-- Footer -->
    <footer class="lg:pl-72 bg-white dark:bg-black border-t border-gray-300 dark:border-gray-600 mt-auto transition-all duration-300 ease-in-out" id="footer">
      <div class="px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            © 2025 Glyph. All rights reserved.
          </p>
          <div class="mt-4 sm:mt-0 flex space-x-6">
            <a class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" href="/terms">Terms of Service</a>
            <a class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" href="/privacy">Privacy Policy</a>
          </div>
        </div>
      </div>
    </footer>
    

    <!-- Command Palette -->
    <!-- BEGIN app/views/shared/_command_palette.html.erb --><!-- Command Palette -->
<div data-controller="command-palette" class="relative z-50">
  <dialog data-command-palette-target="dialog" class="backdrop:bg-transparent fixed inset-0 z-50 overflow-y-auto">
    <div class="fixed inset-0 bg-black/25 transition-opacity dark:bg-black/50"></div>
    
    <div tabindex="0" class="fixed inset-0 w-screen overflow-y-auto p-4 focus:outline-none sm:p-6 md:p-20">
      <div class="mx-auto block max-w-xl transform overflow-hidden rounded-xl bg-white shadow-2xl outline-1 outline-black/5 transition-all dark:bg-black dark:-outline-offset-1 dark:outline-white/10">
        <div>
          <div class="grid grid-cols-1 border-b border-gray-300 dark:border-gray-600">
            <input type="text" autofocus="" placeholder="Search for actions, pages, or features..." data-command-palette-target="input" data-action="input-&gt;command-palette#search keydown-&gt;command-palette#handleListKeyDown" class="col-start-1 row-start-1 h-12 w-full pr-4 pl-11 text-base text-black outline-hidden placeholder:text-gray-400 sm:text-sm dark:bg-black dark:text-white dark:placeholder:text-gray-500">
            <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="pointer-events-none col-start-1 row-start-1 ml-4 size-5 self-center text-gray-400 dark:text-gray-400">
              <path d="M9 3.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11ZM2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Z" clip-rule="evenodd" fill-rule="evenodd"></path>
            </svg>
          </div>

          <div data-command-palette-target="list" class="block max-h-72 scroll-py-2 overflow-y-auto py-2 text-sm text-black dark:text-white" hidden="">
            <!-- Actions will be dynamically inserted here -->
          </div>

          <div data-command-palette-target="noResults" class="block p-4 text-center text-sm text-gray-600 dark:text-gray-400" hidden="">
            <div class="flex flex-col items-center justify-center py-6">
              <svg class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"></path>
              </svg>
              <p class="text-black dark:text-white font-medium">No results found</p>
              <p class="text-gray-600 dark:text-gray-400 text-xs mt-1">Try searching with different keywords</p>
            </div>
          </div>

          <!-- Help text at bottom -->
          <div class="border-t border-gray-300 dark:border-gray-600 px-4 py-2 text-xs text-gray-600 dark:text-gray-400">
            <div class="flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <span>Press <kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">↑↓</kbd> to navigate</span>
                <span><kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">↵</kbd> to select</span>
                <span><kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">esc</kbd> twice to close</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </dialog>
</div><!-- END app/views/shared/_command_palette.html.erb -->

    <!-- Save to Board Modal -->
  

<!-- END app/views/layouts/application.html.erb --></div>

      <!-- Loading Spinner -->
      <div data-infinite-scroll-target="loading" class="hidden">
        <!-- BEGIN app/views/shared/_loading_spinner.html.erb --><div class="flex justify-center items-center py-8">
  <div class="inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400">
    <svg class="animate-spin -ml-1 mr-3 h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    <span class="text-sm font-medium">Loading more...</span>
  </div>
</div><!-- END app/views/shared/_loading_spinner.html.erb -->
      </div>
    </div>