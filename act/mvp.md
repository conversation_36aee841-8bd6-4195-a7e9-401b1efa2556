# Tattoo Marketplace Implementation Plan

## Tech Stack
- Rails 8, S<PERSON>ite, Tailwind CSS
- Active Storage with MiniMagick (dev) / Vips (production)
- Rails 8 authentication generator
- Solid Queue for background image processing
- FriendlyId gem for URL slugs
- No Stimulus initially - pure Rails forms with page refreshes

## Phase 1: Foundation & Core Models (Week 1-2)

### Database Schema
**Core Tables:**
- `users` - email, password, role (artist/client), username, admin (boolean), approved (boolean)
- `settings` - approval_required (boolean), other site-wide settings
- `artist_profiles` - name, slug (for URL), biography, location (text field), contact_email, instagram_url, website_url, booking_link, messages_enabled (boolean)
- `client_profiles` - basic profile info
- `follows` - following system
- `posts` - single or multiple images with one caption (artists only)
- `portfolio_items` - single image + optional caption, position (integer)
- `inspiration_boards` - named collections (e.g., "leg tattoo")
- `inspiration_items` - saved posts in boards
- `specialties` - title, description, optional image attachment
- `artist_specialties` - join table
- `conversations` & `messages` - messaging system
- `blocks` - user blocking functionality

### Key Tasks:
1. Set up Rails 8 with authentication generator
2. Create User model with role-based system and approval flag
3. Implement profile models with different fields per role
4. Set up Active Storage with proper image processing
5. Create base controllers with role-based access control
6. Admin flag and approval system

## Phase 2: User Registration & Profiles (Week 3-4)

### Features:
- Single comprehensive signup page (not multi-step)
- Toggle between artist/client on the same form
- All profile fields on one page
- Artist fields: name, biography, location, specialties, social links, contact email
- Automatic URL slug generation from artist name
- Client fields: basic profile info
- Profile photo uploads
- Admin approval required for accounts (toggleable)
- Special artist signup route that bypasses approval

### Key Tasks:
1. Single-page registration form with role toggle
2. Conditional form fields based on selected role
3. FriendlyId setup with slug generation
4. Slug customization field for artists
5. Specialty selection from predefined list
6. Optional social links (only show if filled)
7. Contact email separate from account email
8. Image upload validation
9. Approval workflow (check settings table)
10. Special route (e.g., `/artist-invite-xyz123`) that auto-approves

## Phase 3: Portfolio & Posts System (Week 5-6)

### Features:
- **Portfolio Items**: Single image + optional caption
- **Posts**: Single or multiple images + single caption
- Gallery view for multi-photo posts
- Artists can create both portfolio items and posts
- Likes and comments on posts
- Background image processing for variants

### Key Tasks:
1. Portfolio item CRUD for artists
2. Position field for portfolio ordering
3. Post creation with single or multiple images
4. Gallery display for multi-image posts
5. Asynchronous job for image variants (thumbnail, medium, large)
6. Like and comment functionality
7. Portfolio reordering interface (position-based)

## Phase 4: Inspiration Boards (Week 7-8)

### Features:
- Both artists and clients can save to inspiration
- Named boards (e.g., "leg tattoo", "geometric designs")
- Save posts to specific boards
- Private/public board settings
- Board management interface

### Key Tasks:
1. Inspiration board CRUD
2. Save posts to boards functionality
3. Board privacy settings
4. Display boards on profiles
5. Browse user's public boards
6. Move items between boards

## Phase 5: Discovery & Search (Week 9-10)

### Features:
- Home feed (chronological posts from followed artists)
- General artist search (single search bar)
- Search across: artist name, location, bio, specialty keywords
- Artist directory/browse page
- Save functionality for posts

### Search Implementation:
- Single search bar (no separate filters initially)
- Searches artist name, location, biography, and specialty titles
- Location is simple text field (no geolocation yet)
- Results show artist cards with name, location, specialties, profile photo
- Pagination for results

### Key Tasks:
1. General search across multiple fields
2. Artist directory with search
3. Search results pagination
4. Artist card previews
6. Follow/unfollow functionality

## Phase 6: Messaging System (Week 11-12)

### Features:
- Direct messaging between users
- Artists can disable messages entirely
- Send images in messages
- Block users from messaging
- Standard form submission (no real-time)

### Key Tasks:
1. Message enable/disable toggle for artists
2. Conversation list and inbox
3. Block list implementation
4. Send images in messages
5. Unread message indicators
6. Message notifications via email

## Phase 7: Admin Dashboard (Week 13-14)

### Features:
- User approval system (toggleable on/off)
- Site-wide settings management
- View all users (filterable by role, approval status)
- Approve/reject new accounts
- Content moderation
- Basic site statistics
- User management (edit, delete)
- Specialty management (CRUD operations)

### Key Tasks:
1. Admin authentication and authorization
2. Settings page with approval_required toggle
3. User list with approval actions
4. Filter users by approval status
5. Content flagging system
6. Hide/show posts moderation
7. Site statistics (counts, growth)
8. Specialty management interface (add/edit/delete with image uploads)

## Phase 8: Polish & Launch Prep (Week 15-16)

### Features:
- Mobile responsiveness
- Email notifications setup
- Terms of service and privacy
- Performance optimization
- Final UI polish

### Key Tasks:
1. Responsive design testing
2. Email templates (welcome, messages, etc.)
3. Legal pages (terms, privacy)
4. Image lazy loading
5. Final bug fixes and testing

## Phase 9: JavaScript & Interactivity (Week 17-18)

### Features:
- AJAX interactions for better UX
- Real-time features
- Advanced UI interactions
- Performance enhancements
- Progressive web app features

### Key Tasks:

#### Core AJAX Functionality:
1. **Login Modal for Unauthenticated Users**
   - Modal popup when unauthenticated users try to like/comment/follow
   - Quick login/signup without page refresh
   - Return to original action after authentication

2. **AJAX Like/Unlike System**
   - Instant visual feedback on like button clicks
   - Update like counts without page refresh
   - Heart animation on like/unlike

3. **AJAX Comment System**
   - Inline comment creation and editing
   - Real-time comment posting
   - Edit comments without page navigation

4. **AJAX Follow/Unfollow**
   - Instant follow button state changes
   - Update follower counts immediately
   - Follow/unfollow from any page (artist cards, profiles, etc.)

#### Advanced Features:
5. **Infinite Scroll**
   - Home feed infinite loading
   - Artist directory pagination
   - Search results continuous loading
   - Portfolio and post galleries

6. **Live Search & Autocomplete**
   - Real-time artist search suggestions
   - Search as you type functionality
   - Highlight matching terms
   - Recent searches memory

7. **Enhanced Image Handling**
   - Image preview before upload
   - Progressive image loading
   - Lazy loading for performance
   - Lightbox/modal image viewing
   - Image galleries with navigation

8. **Interactive UI Elements**
   - Drag and drop portfolio reordering
   - Auto-save for long forms (drafts)
   - Keyboard shortcuts for power users
   - Toast notification system

#### Real-time Features:
9. **Real-time Messaging**
   - WebSocket/ActionCable integration
   - Live message delivery
   - Typing indicators
   - Online status indicators

10. **Push Notifications**
    - Browser notifications for messages
    - New follower notifications
    - Comment and like notifications

#### Performance & PWA:
11. **Progressive Web App**
    - Service worker for offline functionality
    - App installation prompts
    - Background sync for actions
    - Cached content for offline viewing

12. **Performance Optimizations**
    - Image optimization and compression
    - Code splitting and lazy loading
    - Database query optimization
    - CDN integration for assets

### Implementation Notes:
- Use Stimulus.js for JavaScript organization
- Implement Turbo for AJAX functionality
- ActionCable for real-time features
- Intersection Observer for infinite scroll
- Service Workers for PWA features
- IndexedDB for offline data storage

### Browser Compatibility:
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Progressive enhancement approach
- Graceful fallbacks for older browsers
- Mobile-first responsive design

## Technical Implementation Details

### Image Processing:
```ruby
# config/application.rb
config.active_storage.variant_processor = Rails.env.production? ? :vips : :mini_magick

# After upload, process variants asynchronously
class ImageProcessingJob < ApplicationJob
  def perform(blob)
    blob.variant(resize_to_limit: [150, 150]).processed # thumbnail
    blob.variant(resize_to_limit: [600, 600]).processed # medium
    blob.variant(resize_to_limit: [1200, 1200]).processed # large
  end
end
```

### Seed Data (db/seeds.rb):
```ruby
# Create specialties with descriptions
specialties = [
  { title: "Traditional", description: "Bold lines, limited color palette, classic imagery" },
  { title: "Japanese", description: "Traditional Japanese motifs and techniques" },
  { title: "Realism", description: "Photorealistic portraits and scenes" },
  { title: "Watercolor", description: "Soft, flowing designs mimicking watercolor paintings" },
  { title: "Tribal", description: "Bold black patterns inspired by indigenous cultures" },
  { title: "New School", description: "Vibrant colors, exaggerated features, cartoon-like" },
  { title: "Neo Traditional", description: "Modern take on traditional with more detail and color" },
  { title: "Blackwork", description: "Solid black ink designs and patterns" },
  { title: "Dotwork", description: "Images created entirely from dots" },
  { title: "Geometric", description: "Precise mathematical patterns and shapes" },
  { title: "Portrait", description: "Realistic human or animal faces" },
  { title: "Biomechanical", description: "Fusion of organic and mechanical elements" },
  { title: "Surrealism", description: "Dream-like, abstract imagery" },
  { title: "Minimalist", description: "Simple, clean designs with minimal elements" }
]

# Create admin user, sample artists, clients, posts, etc.
```

### Specialty Management:
- Each specialty has title, description, and optional image
- Admin can upload showcase images for each style
- Used in artist profiles and search filters

### URL Structure & FriendlyId:
```ruby
# Artist portfolio URL: /artists/artistname
# Using FriendlyId with slug_candidates
class ArtistProfile < ApplicationRecord
  extend FriendlyId
  friendly_id :slug_candidates, use: :slugged
  
  def slug_candidates
    [
      :name,
      [:name, -> { Time.current.to_i }]  # name-unixTimestamp if taken
    ]
  end
end
```

### Artist Profile Page Structure:
Artist profiles have three separate pages (not tabs, but separate URLs):

1. **Overview Page** (`/artists/artistname`)
   - Main profile with bio, location, social links
   - **Specialties section**: Display all selected tattoo styles (artists can select multiple)
   - Top right: "Message" button (only if messages_enabled == true)
   - Follow/Unfollow button (shows "Following" if already following, "Follow" if not)
   - Contact email and booking link (only if provided)

2. **Portfolio Page** (`/artists/artistname/portfolio`)
   - Grid display of portfolio items
   - Single images with optional captions
   - Ordered by position field

3. **Posts Page** (`/artists/artistname/posts`)
   - Chronological feed of their posts
   - Single or multiple images with captions
   - Like and comment functionality

### Profile Display Logic:
- Only show contact_email if filled
- Only show social links if provided
- Show booking link only if available == true
- Hide message button if messages_enabled == false
- Follow button only shows for logged-in clients

### Registration Flow:
1. Single page with role selector at top
2. Form dynamically shows/hides fields based on role
3. Artists see: biography, **multiple specialty selection** (checkboxes), location fields
4. Clients see: minimal profile fields
5. Submit creates user as "unapproved" (if approval_required setting is on)
6. Admin must approve before full access (unless approval_required is off)
7. Special artist route (e.g., `/join/artist-early-access-2025`) bypasses approval

### Specialty System:
- Artists can select multiple tattoo styles/specialties during registration
- 14 predefined specialties available (Traditional, Japanese, Realism, etc.)
- Displayed prominently on artist overview pages
- Used in search functionality to find artists by style
- Admin can manage specialties via admin dashboard

### Special Artist Route:
- Create a unique, hard-to-guess URL for artist signups
- Artists who sign up via this route are auto-approved
- Same form but with hidden field indicating special route
- Useful for inviting specific artists during closed beta

### Key Simplifications:
- No Stimulus.js initially
- No real-time features
- Simple text field for location
- No waitlist functionality
- Basic blocking (no reporting)
- Standard Rails forms throughout