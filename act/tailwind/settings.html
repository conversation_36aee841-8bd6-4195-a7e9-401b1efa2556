<!-- Include this script tag or install `@tailwindplus/elements` via npm: -->
<!-- <script src="https://cdn.jsdelivr.net/npm/@tailwindplus/elements@1" type="module"></script> -->
<header class="absolute inset-x-0 top-0 z-50 flex h-16 border-b border-gray-900/10">
  <div class="mx-auto flex w-full max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8">
    <div class="flex flex-1 items-center gap-x-6">
      <button type="button" command="show-modal" commandfor="mobile-menu" class="-m-3 p-3 md:hidden">
        <span class="sr-only">Open main menu</span>
        <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5 text-gray-900">
          <path
            d="M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75ZM2 10a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 10Zm0 5.25a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z"
            clip-rule="evenodd" fill-rule="evenodd" />
        </svg>
      </button>
      <img src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600" alt="Your Company"
        class="h-8 w-auto" />
    </div>
    <nav class="hidden md:flex md:gap-x-11 md:text-sm/6 md:font-semibold md:text-gray-700">
      <a href="#">Home</a>
      <a href="#">Invoices</a>
      <a href="#">Clients</a>
      <a href="#">Expenses</a>
    </nav>
    <div class="flex flex-1 items-center justify-end gap-x-8">
      <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500">
        <span class="sr-only">View notifications</span>
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
          aria-hidden="true" class="size-6">
          <path
            d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
            stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </button>
      <a href="#" class="-m-1.5 p-1.5">
        <span class="sr-only">Your profile</span>
        <img
          src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
          alt="" class="size-8 rounded-full bg-gray-800" />
      </a>
    </div>
  </div>
  <el-dialog>
    <dialog id="mobile-menu" class="backdrop:bg-transparent lg:hidden">
      <div tabindex="0" class="fixed inset-0 focus:outline-none">
        <el-dialog-panel
          class="fixed inset-y-0 left-0 z-50 w-full overflow-y-auto bg-white px-4 pb-6 sm:max-w-sm sm:px-6 sm:ring-1 sm:ring-gray-900/10">
          <div class="-ml-0.5 flex h-16 items-center gap-x-6">
            <button type="button" command="close" commandfor="mobile-menu" class="-m-2.5 p-2.5 text-gray-700">
              <span class="sr-only">Close menu</span>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
                aria-hidden="true" class="size-6">
                <path d="M6 18 18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </button>
            <div class="-ml-0.5">
              <a href="#" class="-m-1.5 block p-1.5">
                <span class="sr-only">Your Company</span>
                <img src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600" alt=""
                  class="h-8 w-auto" />
              </a>
            </div>
          </div>
          <div class="mt-2 space-y-2">
            <a href="#"
              class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Home</a>
            <a href="#"
              class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Invoices</a>
            <a href="#"
              class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Clients</a>
            <a href="#"
              class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Expenses</a>
          </div>
        </el-dialog-panel>
      </div>
    </dialog>
  </el-dialog>
</header>

<div class="mx-auto max-w-7xl pt-16 lg:flex lg:gap-x-16 lg:px-8">
  <h1 class="sr-only">General Settings</h1>

  <aside
    class="flex overflow-x-auto border-b border-gray-900/5 py-4 lg:block lg:w-64 lg:flex-none lg:border-0 lg:py-20">
    <nav class="flex-none px-4 sm:px-6 lg:px-0">
      <ul role="list" class="flex gap-x-3 gap-y-1 whitespace-nowrap lg:flex-col">
        <li>
          <!-- Current: "bg-gray-50 text-indigo-600", Default: "text-gray-700 hover:text-indigo-600 hover:bg-gray-50" -->
          <a href="#"
            class="group flex gap-x-3 rounded-md bg-gray-50 py-2 pr-3 pl-2 text-sm/6 font-semibold text-indigo-600">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
              aria-hidden="true" class="size-6 shrink-0 text-indigo-600">
              <path
                d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            General
          </a>
        </li>
        <li>
          <a href="#"
            class="group flex gap-x-3 rounded-md py-2 pr-3 pl-2 text-sm/6 font-semibold text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
              aria-hidden="true" class="size-6 shrink-0 text-gray-400 group-hover:text-indigo-600">
              <path
                d="M7.864 4.243A7.5 7.5 0 0 1 19.5 10.5c0 2.92-.556 5.709-1.568 8.268M5.742 6.364A7.465 7.465 0 0 0 4.5 10.5a7.464 7.464 0 0 1-1.15 3.993m1.989 3.559A11.209 11.209 0 0 0 8.25 10.5a3.75 3.75 0 1 1 7.5 0c0 .527-.021 1.049-.064 1.565M12 10.5a14.94 14.94 0 0 1-3.6 9.75m6.633-4.596a18.666 18.666 0 0 1-2.485 5.33"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            Security
          </a>
        </li>
        <li>
          <a href="#"
            class="group flex gap-x-3 rounded-md py-2 pr-3 pl-2 text-sm/6 font-semibold text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
              aria-hidden="true" class="size-6 shrink-0 text-gray-400 group-hover:text-indigo-600">
              <path
                d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            Notifications
          </a>
        </li>
        <li>
          <a href="#"
            class="group flex gap-x-3 rounded-md py-2 pr-3 pl-2 text-sm/6 font-semibold text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
              aria-hidden="true" class="size-6 shrink-0 text-gray-400 group-hover:text-indigo-600">
              <path d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            Plan
          </a>
        </li>
        <li>
          <a href="#"
            class="group flex gap-x-3 rounded-md py-2 pr-3 pl-2 text-sm/6 font-semibold text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
              aria-hidden="true" class="size-6 shrink-0 text-gray-400 group-hover:text-indigo-600">
              <path
                d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            Billing
          </a>
        </li>
        <li>
          <a href="#"
            class="group flex gap-x-3 rounded-md py-2 pr-3 pl-2 text-sm/6 font-semibold text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
              aria-hidden="true" class="size-6 shrink-0 text-gray-400 group-hover:text-indigo-600">
              <path
                d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            Team members
          </a>
        </li>
      </ul>
    </nav>
  </aside>

  <main class="px-4 py-16 sm:px-6 lg:flex-auto lg:px-0 lg:py-20">
    <div class="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
      <div>
        <h2 class="text-base/7 font-semibold text-gray-900">Profile</h2>
        <p class="mt-1 text-sm/6 text-gray-500">This information will be displayed publicly so be careful what you
          share.</p>

        <dl class="mt-6 divide-y divide-gray-100 border-t border-gray-200 text-sm/6">
          <div class="py-6 sm:flex">
            <dt class="font-medium text-gray-900 sm:w-64 sm:flex-none sm:pr-6">Full name</dt>
            <dd class="mt-1 flex justify-between gap-x-6 sm:mt-0 sm:flex-auto">
              <div class="text-gray-900">Tom Cook</div>
              <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
            </dd>
          </div>
          <div class="py-6 sm:flex">
            <dt class="font-medium text-gray-900 sm:w-64 sm:flex-none sm:pr-6">Email address</dt>
            <dd class="mt-1 flex justify-between gap-x-6 sm:mt-0 sm:flex-auto">
              <div class="text-gray-900"><EMAIL></div>
              <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
            </dd>
          </div>
          <div class="py-6 sm:flex">
            <dt class="font-medium text-gray-900 sm:w-64 sm:flex-none sm:pr-6">Title</dt>
            <dd class="mt-1 flex justify-between gap-x-6 sm:mt-0 sm:flex-auto">
              <div class="text-gray-900">Human Resources Manager</div>
              <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
            </dd>
          </div>
        </dl>
      </div>

      <div>
        <h2 class="text-base/7 font-semibold text-gray-900">Bank accounts</h2>
        <p class="mt-1 text-sm/6 text-gray-500">Connect bank accounts to your account.</p>

        <ul role="list" class="mt-6 divide-y divide-gray-100 border-t border-gray-200 text-sm/6">
          <li class="flex justify-between gap-x-6 py-6">
            <div class="font-medium text-gray-900">TD Canada Trust</div>
            <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
          </li>
          <li class="flex justify-between gap-x-6 py-6">
            <div class="font-medium text-gray-900">Royal Bank of Canada</div>
            <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
          </li>
        </ul>

        <div class="flex border-t border-gray-100 pt-6">
          <button type="button" class="text-sm/6 font-semibold text-indigo-600 hover:text-indigo-500"><span
              aria-hidden="true">+</span> Add another bank</button>
        </div>
      </div>

      <div>
        <h2 class="text-base/7 font-semibold text-gray-900">Integrations</h2>
        <p class="mt-1 text-sm/6 text-gray-500">Connect applications to your account.</p>

        <ul role="list" class="mt-6 divide-y divide-gray-100 border-t border-gray-200 text-sm/6">
          <li class="flex justify-between gap-x-6 py-6">
            <div class="font-medium text-gray-900">QuickBooks</div>
            <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
          </li>
        </ul>

        <div class="flex border-t border-gray-100 pt-6">
          <button type="button" class="text-sm/6 font-semibold text-indigo-600 hover:text-indigo-500"><span
              aria-hidden="true">+</span> Add another application</button>
        </div>
      </div>

      <div>
        <h2 class="text-base/7 font-semibold text-gray-900">Language and dates</h2>
        <p class="mt-1 text-sm/6 text-gray-500">Choose what language and date format to use throughout your account.</p>

        <dl class="mt-6 divide-y divide-gray-100 border-t border-gray-200 text-sm/6">
          <div class="py-6 sm:flex">
            <dt class="font-medium text-gray-900 sm:w-64 sm:flex-none sm:pr-6">Language</dt>
            <dd class="mt-1 flex justify-between gap-x-6 sm:mt-0 sm:flex-auto">
              <div class="text-gray-900">English</div>
              <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
            </dd>
          </div>
          <div class="py-6 sm:flex">
            <dt class="font-medium text-gray-900 sm:w-64 sm:flex-none sm:pr-6">Date format</dt>
            <dd class="mt-1 flex justify-between gap-x-6 sm:mt-0 sm:flex-auto">
              <div class="text-gray-900">DD-MM-YYYY</div>
              <button type="button" class="font-semibold text-indigo-600 hover:text-indigo-500">Update</button>
            </dd>
          </div>
          <div class="flex pt-6">
            <dt class="font-medium text-gray-900 sm:w-64 sm:flex-none sm:pr-6">Automatic timezone</dt>
            <dd class="flex flex-auto items-center justify-end">
              <div
                class="group relative inline-flex w-8 shrink-0 rounded-full bg-gray-200 p-px inset-ring inset-ring-gray-900/5 outline-offset-2 outline-indigo-600 transition-colors duration-200 ease-in-out has-checked:bg-indigo-600 has-focus-visible:outline-2 dark:bg-white/5 dark:inset-ring-white/10 dark:outline-indigo-500 dark:has-checked:bg-indigo-500">
                <span
                  class="size-4 rounded-full bg-white shadow-xs ring-1 ring-gray-900/5 transition-transform duration-200 ease-in-out group-has-checked:translate-x-3.5"></span>
                <input type="checkbox" name="automatic-timezone" checked aria-label="Automatic timezone"
                  class="absolute inset-0 appearance-none focus:outline-hidden" />
              </div>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </main>
</div>