<!-- Include this script tag or install `@tailwindplus/elements` via npm: -->
<!-- <script src="https://cdn.jsdelivr.net/npm/@tailwindplus/elements@1" type="module"></script> -->
<button command="show-modal" commandfor="dialog" class="rounded-md bg-white/80 px-2.5 py-1.5 text-sm font-semibold text-gray-900 hover:bg-white/90 dark:bg-gray-800/80 dark:text-white dark:hover:bg-gray-800/90">Open command palette</button>

<el-dialog>
  <dialog id="dialog" class="backdrop:bg-transparent">
    <el-dialog-backdrop class="fixed inset-0 bg-gray-500/25 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in dark:bg-gray-900/50"></el-dialog-backdrop>

    <div tabindex="0" class="fixed inset-0 w-screen overflow-y-auto p-4 focus:outline-none sm:p-6 md:p-20">
      <el-dialog-panel class="mx-auto block max-w-xl transform overflow-hidden rounded-xl bg-white shadow-2xl outline-1 outline-black/5 transition-all data-closed:scale-95 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in dark:bg-gray-900 dark:-outline-offset-1 dark:outline-white/10">
        <el-command-palette>
          <div class="grid grid-cols-1 border-b border-gray-100 dark:border-white/10">
            <input type="text" autofocus placeholder="Search..." class="col-start-1 row-start-1 h-12 w-full pr-4 pl-11 text-base text-gray-900 outline-hidden placeholder:text-gray-400 sm:text-sm dark:bg-gray-900 dark:text-white dark:placeholder:text-gray-500" />
            <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="pointer-events-none col-start-1 row-start-1 ml-4 size-5 self-center text-gray-400 dark:text-gray-500">
              <path d="M9 3.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11ZM2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Z" clip-rule="evenodd" fill-rule="evenodd" />
            </svg>
          </div>

          <el-command-list hidden class="block max-h-72 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 dark:text-gray-200">
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Leslie Alexander</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Michael Foster</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Dries Vincent</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Lindsay Walton</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Courtney Henry</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Tom Cook</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Whitney Francis</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Leonard Krasner</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Floyd Miles</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Emily Selman</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Kristin Watson</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Emma Dorsey</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Alicia Bell</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Jenny Wilson</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Anna Roberts</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Benjamin Russel</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Jeffrey Webb</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Kathryn Murphy</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Lawrence Hunter</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Yvette Armstrong</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Angela Fisher</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Blake Reid</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Hector Gibbons</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Fabricio Mendes</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Jillian Steward</a>
            <a href="#" hidden class="block cursor-default px-4 py-2 select-none focus:outline-hidden aria-selected:bg-indigo-600 aria-selected:text-white dark:aria-selected:bg-indigo-500">Chelsea Hagon</a>
          </el-command-list>

          <el-no-results hidden class="block p-4 text-sm text-gray-500 dark:text-gray-400">No people found.</el-no-results>
        </el-command-palette>
      </el-dialog-panel>
    </div>
  </dialog>
</el-dialog>
