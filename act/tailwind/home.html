<!-- Include this script tag or install `@tailwindplus/elements` via npm: -->
<!-- <script src="https://cdn.jsdelivr.net/npm/@tailwindplus/elements@1" type="module"></script> -->
<header class="absolute inset-x-0 top-0 z-50 flex h-16 border-b border-gray-900/10">
  <div class="mx-auto flex w-full max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8">
    <div class="flex flex-1 items-center gap-x-6">
      <button type="button" command="show-modal" commandfor="mobile-menu" class="-m-3 p-3 md:hidden">
        <span class="sr-only">Open main menu</span>
        <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5 text-gray-900">
          <path d="M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75ZM2 10a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 10Zm0 5.25a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" fill-rule="evenodd" />
        </svg>
      </button>
      <img src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600" alt="Your Company" class="h-8 w-auto" />
    </div>
    <nav class="hidden md:flex md:gap-x-11 md:text-sm/6 md:font-semibold md:text-gray-700">
      <a href="#">Home</a>
      <a href="#">Invoices</a>
      <a href="#">Clients</a>
      <a href="#">Expenses</a>
    </nav>
    <div class="flex flex-1 items-center justify-end gap-x-8">
      <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500">
        <span class="sr-only">View notifications</span>
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon" aria-hidden="true" class="size-6">
          <path d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </button>
      <a href="#" class="-m-1.5 p-1.5">
        <span class="sr-only">Your profile</span>
        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="" class="size-8 rounded-full bg-gray-800" />
      </a>
    </div>
  </div>
  <el-dialog>
    <dialog id="mobile-menu" class="backdrop:bg-transparent lg:hidden">
      <div tabindex="0" class="fixed inset-0 focus:outline-none">
        <el-dialog-panel class="fixed inset-y-0 left-0 z-50 w-full overflow-y-auto bg-white px-4 pb-6 sm:max-w-sm sm:px-6 sm:ring-1 sm:ring-gray-900/10">
          <div class="-ml-0.5 flex h-16 items-center gap-x-6">
            <button type="button" command="close" commandfor="mobile-menu" class="-m-2.5 p-2.5 text-gray-700">
              <span class="sr-only">Close menu</span>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon" aria-hidden="true" class="size-6">
                <path d="M6 18 18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </button>
            <div class="-ml-0.5">
              <a href="#" class="-m-1.5 block p-1.5">
                <span class="sr-only">Your Company</span>
                <img src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600" alt="" class="h-8 w-auto" />
              </a>
            </div>
          </div>
          <div class="mt-2 space-y-2">
            <a href="#" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Home</a>
            <a href="#" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Invoices</a>
            <a href="#" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Clients</a>
            <a href="#" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50">Expenses</a>
          </div>
        </el-dialog-panel>
      </div>
    </dialog>
  </el-dialog>
</header>

<main>
  <div class="relative isolate overflow-hidden pt-16">
    <!-- Secondary navigation -->
    <header class="pt-6 pb-4 sm:pb-6">
      <div class="mx-auto flex max-w-7xl flex-wrap items-center gap-6 px-4 sm:flex-nowrap sm:px-6 lg:px-8">
        <h1 class="text-base/7 font-semibold text-gray-900">Cashflow</h1>
        <div class="order-last flex w-full gap-x-8 text-sm/6 font-semibold sm:order-0 sm:w-auto sm:border-l sm:border-gray-200 sm:pl-6 sm:text-sm/7">
          <a href="#" class="text-indigo-600">Last 7 days</a>
          <a href="#" class="text-gray-700">Last 30 days</a>
          <a href="#" class="text-gray-700">All-time</a>
        </div>
        <a href="#" class="ml-auto flex items-center gap-x-1 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
          <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="-ml-1.5 size-5">
            <path d="M10.75 6.75a.75.75 0 0 0-1.5 0v2.5h-2.5a.75.75 0 0 0 0 1.5h2.5v2.5a.75.75 0 0 0 1.5 0v-2.5h2.5a.75.75 0 0 0 0-1.5h-2.5v-2.5Z" />
          </svg>
          New invoice
        </a>
      </div>
    </header>

    <!-- Stats -->
    <div class="border-b border-b-gray-900/10 lg:border-t lg:border-t-gray-900/5">
      <dl class="mx-auto grid max-w-7xl grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 lg:px-2 xl:px-0">
        <div class="flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8">
          <dt class="text-sm/6 font-medium text-gray-500">Revenue</dt>
          <dd class="text-xs font-medium text-gray-700">+4.75%</dd>
          <dd class="w-full flex-none text-3xl/10 font-medium tracking-tight text-gray-900">$405,091.00</dd>
        </div>
        <div class="flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 border-t border-gray-900/5 px-4 py-10 sm:border-l sm:px-6 lg:border-t-0 xl:px-8">
          <dt class="text-sm/6 font-medium text-gray-500">Overdue invoices</dt>
          <dd class="text-xs font-medium text-rose-600">+54.02%</dd>
          <dd class="w-full flex-none text-3xl/10 font-medium tracking-tight text-gray-900">$12,787.00</dd>
        </div>
        <div class="flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 lg:border-l xl:px-8">
          <dt class="text-sm/6 font-medium text-gray-500">Outstanding invoices</dt>
          <dd class="text-xs font-medium text-gray-700">-1.39%</dd>
          <dd class="w-full flex-none text-3xl/10 font-medium tracking-tight text-gray-900">$245,988.00</dd>
        </div>
        <div class="flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 border-t border-gray-900/5 px-4 py-10 sm:border-l sm:px-6 lg:border-t-0 xl:px-8">
          <dt class="text-sm/6 font-medium text-gray-500">Expenses</dt>
          <dd class="text-xs font-medium text-rose-600">+10.18%</dd>
          <dd class="w-full flex-none text-3xl/10 font-medium tracking-tight text-gray-900">$30,156.00</dd>
        </div>
      </dl>
    </div>

    <div aria-hidden="true" class="absolute top-full left-0 -z-10 mt-96 origin-top-left translate-y-40 -rotate-90 transform-gpu opacity-20 blur-3xl sm:left-1/2 sm:-mt-10 sm:-ml-96 sm:translate-y-0 sm:rotate-0 sm:transform-gpu sm:opacity-50">
      <div style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)" class="aspect-1154/678 w-288.5 bg-linear-to-br from-[#FF80B5] to-[#9089FC]"></div>
    </div>
  </div>

  <div class="space-y-16 py-16 xl:space-y-20">
    <!-- Recent activity table -->
    <div>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <h2 class="mx-auto max-w-2xl text-base font-semibold text-gray-900 lg:mx-0 lg:max-w-none">Recent activity</h2>
      </div>
      <div class="mt-6 overflow-hidden border-t border-gray-100">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:mx-0 lg:max-w-none">
            <table class="w-full text-left">
              <thead class="sr-only">
                <tr>
                  <th>Amount</th>
                  <th class="hidden sm:table-cell">Client</th>
                  <th>More details</th>
                </tr>
              </thead>
              <tbody>
                <tr class="text-sm/6 text-gray-900">
                  <th scope="colgroup" colspan="3" class="relative isolate py-2 font-semibold">
                    <time datetime="2023-03-22">Today</time>
                    <div class="absolute inset-y-0 right-full -z-10 w-screen border-b border-gray-200 bg-gray-50"></div>
                    <div class="absolute inset-y-0 left-0 -z-10 w-screen border-b border-gray-200 bg-gray-50"></div>
                  </th>
                </tr>
                <tr>
                  <td class="relative py-5 pr-6">
                    <div class="flex gap-x-6">
                      <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="hidden h-6 w-5 flex-none text-gray-400 sm:block">
                        <path d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm-.75-4.75a.75.75 0 0 0 1.5 0V8.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0L6.2 9.74a.75.75 0 1 0 1.1 1.02l1.95-2.1v4.59Z" clip-rule="evenodd" fill-rule="evenodd" />
                      </svg>
                      <div class="flex-auto">
                        <div class="flex items-start gap-x-3">
                          <div class="text-sm/6 font-medium text-gray-900">$7,600.00 USD</div>
                          <div class="rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-green-600/20 ring-inset">Paid</div>
                        </div>
                        <div class="mt-1 text-xs/5 text-gray-500">$500.00 tax</div>
                      </div>
                    </div>
                    <div class="absolute right-full bottom-0 h-px w-screen bg-gray-100"></div>
                    <div class="absolute bottom-0 left-0 h-px w-screen bg-gray-100"></div>
                  </td>
                  <td class="hidden py-5 pr-6 sm:table-cell">
                    <div class="text-sm/6 text-gray-900">Reform</div>
                    <div class="mt-1 text-xs/5 text-gray-500">Website redesign</div>
                  </td>
                  <td class="py-5 text-right">
                    <div class="flex justify-end">
                      <a href="#" class="text-sm/6 font-medium text-indigo-600 hover:text-indigo-500">View<span class="hidden sm:inline"> transaction</span><span class="sr-only">, invoice #00012, Reform</span></a>
                    </div>
                    <div class="mt-1 text-xs/5 text-gray-500">Invoice <span class="text-gray-900">#00012</span></div>
                  </td>
                </tr>
                <tr>
                  <td class="relative py-5 pr-6">
                    <div class="flex gap-x-6">
                      <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="hidden h-6 w-5 flex-none text-gray-400 sm:block">
                        <path d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm.75-11.25a.75.75 0 0 0-1.5 0v4.59L7.3 9.24a.75.75 0 0 0-1.1 1.02l3.25 3.5a.75.75 0 0 0 1.1 0l3.25-3.5a.75.75 0 1 0-1.1-1.02l-1.95 2.1V6.75Z" clip-rule="evenodd" fill-rule="evenodd" />
                      </svg>
                      <div class="flex-auto">
                        <div class="flex items-start gap-x-3">
                          <div class="text-sm/6 font-medium text-gray-900">$10,000.00 USD</div>
                          <div class="rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-gray-500/10 ring-inset">Withdraw</div>
                        </div>
                      </div>
                    </div>
                    <div class="absolute right-full bottom-0 h-px w-screen bg-gray-100"></div>
                    <div class="absolute bottom-0 left-0 h-px w-screen bg-gray-100"></div>
                  </td>
                  <td class="hidden py-5 pr-6 sm:table-cell">
                    <div class="text-sm/6 text-gray-900">Tom Cook</div>
                    <div class="mt-1 text-xs/5 text-gray-500">Salary</div>
                  </td>
                  <td class="py-5 text-right">
                    <div class="flex justify-end">
                      <a href="#" class="text-sm/6 font-medium text-indigo-600 hover:text-indigo-500">View<span class="hidden sm:inline"> transaction</span><span class="sr-only">, invoice #00011, Tom Cook</span></a>
                    </div>
                    <div class="mt-1 text-xs/5 text-gray-500">Invoice <span class="text-gray-900">#00011</span></div>
                  </td>
                </tr>
                <tr>
                  <td class="relative py-5 pr-6">
                    <div class="flex gap-x-6">
                      <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="hidden h-6 w-5 flex-none text-gray-400 sm:block">
                        <path d="M15.312 11.424a5.5 5.5 0 0 1-9.201 2.466l-.312-.311h2.433a.75.75 0 0 0 0-1.5H3.989a.75.75 0 0 0-.75.75v4.242a.75.75 0 0 0 1.5 0v-2.43l.31.31a7 7 0 0 0 11.712-*********** 0 0 0-1.449-.39Zm1.23-3.723a.75.75 0 0 0 .219-.53V2.929a.75.75 0 0 0-1.5 0V5.36l-.31-.31A7 7 0 0 0 3.239 8.188a.75.75 0 1 0 1.448.389A5.5 5.5 0 0 1 13.89 6.11l.311.31h-2.432a.75.75 0 0 0 0 1.5h4.243a.75.75 0 0 0 .53-.219Z" clip-rule="evenodd" fill-rule="evenodd" />
                      </svg>
                      <div class="flex-auto">
                        <div class="flex items-start gap-x-3">
                          <div class="text-sm/6 font-medium text-gray-900">$2,000.00 USD</div>
                          <div class="rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">Overdue</div>
                        </div>
                        <div class="mt-1 text-xs/5 text-gray-500">$130.00 tax</div>
                      </div>
                    </div>
                    <div class="absolute right-full bottom-0 h-px w-screen bg-gray-100"></div>
                    <div class="absolute bottom-0 left-0 h-px w-screen bg-gray-100"></div>
                  </td>
                  <td class="hidden py-5 pr-6 sm:table-cell">
                    <div class="text-sm/6 text-gray-900">Tuple</div>
                    <div class="mt-1 text-xs/5 text-gray-500">Logo design</div>
                  </td>
                  <td class="py-5 text-right">
                    <div class="flex justify-end">
                      <a href="#" class="text-sm/6 font-medium text-indigo-600 hover:text-indigo-500">View<span class="hidden sm:inline"> transaction</span><span class="sr-only">, invoice #00009, Tuple</span></a>
                    </div>
                    <div class="mt-1 text-xs/5 text-gray-500">Invoice <span class="text-gray-900">#00009</span></div>
                  </td>
                </tr>

                <tr class="text-sm/6 text-gray-900">
                  <th scope="colgroup" colspan="3" class="relative isolate py-2 font-semibold">
                    <time datetime="2023-03-21">Yesterday</time>
                    <div class="absolute inset-y-0 right-full -z-10 w-screen border-b border-gray-200 bg-gray-50"></div>
                    <div class="absolute inset-y-0 left-0 -z-10 w-screen border-b border-gray-200 bg-gray-50"></div>
                  </th>
                </tr>
                <tr>
                  <td class="relative py-5 pr-6">
                    <div class="flex gap-x-6">
                      <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="hidden h-6 w-5 flex-none text-gray-400 sm:block">
                        <path d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm-.75-4.75a.75.75 0 0 0 1.5 0V8.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0L6.2 9.74a.75.75 0 1 0 1.1 1.02l1.95-2.1v4.59Z" clip-rule="evenodd" fill-rule="evenodd" />
                      </svg>
                      <div class="flex-auto">
                        <div class="flex items-start gap-x-3">
                          <div class="text-sm/6 font-medium text-gray-900">$14,000.00 USD</div>
                          <div class="rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-green-600/20 ring-inset">Paid</div>
                        </div>
                        <div class="mt-1 text-xs/5 text-gray-500">$900.00 tax</div>
                      </div>
                    </div>
                    <div class="absolute right-full bottom-0 h-px w-screen bg-gray-100"></div>
                    <div class="absolute bottom-0 left-0 h-px w-screen bg-gray-100"></div>
                  </td>
                  <td class="hidden py-5 pr-6 sm:table-cell">
                    <div class="text-sm/6 text-gray-900">SavvyCal</div>
                    <div class="mt-1 text-xs/5 text-gray-500">Website redesign</div>
                  </td>
                  <td class="py-5 text-right">
                    <div class="flex justify-end">
                      <a href="#" class="text-sm/6 font-medium text-indigo-600 hover:text-indigo-500">View<span class="hidden sm:inline"> transaction</span><span class="sr-only">, invoice #00010, SavvyCal</span></a>
                    </div>
                    <div class="mt-1 text-xs/5 text-gray-500">Invoice <span class="text-gray-900">#00010</span></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent client list-->
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:mx-0 lg:max-w-none">
        <div class="flex items-center justify-between">
          <h2 class="text-base/7 font-semibold text-gray-900">Recent clients</h2>
          <a href="#" class="text-sm/6 font-semibold text-indigo-600 hover:text-indigo-500">View all<span class="sr-only">, clients</span></a>
        </div>
        <ul role="list" class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 lg:grid-cols-3 xl:gap-x-8">
          <li class="overflow-hidden rounded-xl border border-gray-200">
            <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
              <img src="https://tailwindcss.com/plus-assets/img/logos/48x48/tuple.svg" alt="Tuple" class="size-12 flex-none rounded-lg bg-white object-cover ring-1 ring-gray-900/10" />
              <div class="text-sm/6 font-medium text-gray-900">Tuple</div>
              <el-dropdown class="relative ml-auto">
                <button class="relative block text-gray-400 hover:text-gray-500">
                  <span class="absolute -inset-2.5"></span>
                  <span class="sr-only">Open options</span>
                  <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                    <path d="M3 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM8.5 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM15.5 8.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Z" />
                  </svg>
                </button>
                <el-menu anchor="bottom end" popover class="w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 transition transition-discrete [--anchor-gap:--spacing(0.5)] focus:outline-hidden data-closed:scale-95 data-closed:transform data-closed:opacity-0 data-enter:duration-100 data-enter:ease-out data-leave:duration-75 data-leave:ease-in">
                  <a href="#" class="block px-3 py-1 text-sm/6 text-gray-900 focus:bg-gray-50 focus:outline-hidden">View<span class="sr-only">, Tuple</span></a>
                  <a href="#" class="block px-3 py-1 text-sm/6 text-gray-900 focus:bg-gray-50 focus:outline-hidden">Edit<span class="sr-only">, Tuple</span></a>
                </el-menu>
              </el-dropdown>
            </div>
            <dl class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm/6">
              <div class="flex justify-between gap-x-4 py-3">
                <dt class="text-gray-500">Last invoice</dt>
                <dd class="text-gray-700"><time datetime="2022-12-13">December 13, 2022</time></dd>
              </div>
              <div class="flex justify-between gap-x-4 py-3">
                <dt class="text-gray-500">Amount</dt>
                <dd class="flex items-start gap-x-2">
                  <div class="font-medium text-gray-900">$2,000.00</div>
                  <div class="rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">Overdue</div>
                </dd>
              </div>
            </dl>
          </li>
          <li class="overflow-hidden rounded-xl border border-gray-200">
            <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
              <img src="https://tailwindcss.com/plus-assets/img/logos/48x48/savvycal.svg" alt="SavvyCal" class="size-12 flex-none rounded-lg bg-white object-cover ring-1 ring-gray-900/10" />
              <div class="text-sm/6 font-medium text-gray-900">SavvyCal</div>
              <el-dropdown class="relative ml-auto">
                <button class="relative block text-gray-400 hover:text-gray-500">
                  <span class="absolute -inset-2.5"></span>
                  <span class="sr-only">Open options</span>
                  <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                    <path d="M3 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM8.5 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM15.5 8.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Z" />
                  </svg>
                </button>
                <el-menu anchor="bottom end" popover class="w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 transition transition-discrete [--anchor-gap:--spacing(0.5)] focus:outline-hidden data-closed:scale-95 data-closed:transform data-closed:opacity-0 data-enter:duration-100 data-enter:ease-out data-leave:duration-75 data-leave:ease-in">
                  <a href="#" class="block px-3 py-1 text-sm/6 text-gray-900 focus:bg-gray-50 focus:outline-hidden">View<span class="sr-only">, SavvyCal</span></a>
                  <a href="#" class="block px-3 py-1 text-sm/6 text-gray-900 focus:bg-gray-50 focus:outline-hidden">Edit<span class="sr-only">, SavvyCal</span></a>
                </el-menu>
              </el-dropdown>
            </div>
            <dl class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm/6">
              <div class="flex justify-between gap-x-4 py-3">
                <dt class="text-gray-500">Last invoice</dt>
                <dd class="text-gray-700"><time datetime="2023-01-22">January 22, 2023</time></dd>
              </div>
              <div class="flex justify-between gap-x-4 py-3">
                <dt class="text-gray-500">Amount</dt>
                <dd class="flex items-start gap-x-2">
                  <div class="font-medium text-gray-900">$14,000.00</div>
                  <div class="rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-green-600/20 ring-inset">Paid</div>
                </dd>
              </div>
            </dl>
          </li>
          <li class="overflow-hidden rounded-xl border border-gray-200">
            <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
              <img src="https://tailwindcss.com/plus-assets/img/logos/48x48/reform.svg" alt="Reform" class="size-12 flex-none rounded-lg bg-white object-cover ring-1 ring-gray-900/10" />
              <div class="text-sm/6 font-medium text-gray-900">Reform</div>
              <el-dropdown class="relative ml-auto">
                <button class="relative block text-gray-400 hover:text-gray-500">
                  <span class="absolute -inset-2.5"></span>
                  <span class="sr-only">Open options</span>
                  <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                    <path d="M3 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM8.5 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM15.5 8.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Z" />
                  </svg>
                </button>
                <el-menu anchor="bottom end" popover class="w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 transition transition-discrete [--anchor-gap:--spacing(0.5)] focus:outline-hidden data-closed:scale-95 data-closed:transform data-closed:opacity-0 data-enter:duration-100 data-enter:ease-out data-leave:duration-75 data-leave:ease-in">
                  <a href="#" class="block px-3 py-1 text-sm/6 text-gray-900 focus:bg-gray-50 focus:outline-hidden">View<span class="sr-only">, Reform</span></a>
                  <a href="#" class="block px-3 py-1 text-sm/6 text-gray-900 focus:bg-gray-50 focus:outline-hidden">Edit<span class="sr-only">, Reform</span></a>
                </el-menu>
              </el-dropdown>
            </div>
            <dl class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm/6">
              <div class="flex justify-between gap-x-4 py-3">
                <dt class="text-gray-500">Last invoice</dt>
                <dd class="text-gray-700"><time datetime="2023-01-23">January 23, 2023</time></dd>
              </div>
              <div class="flex justify-between gap-x-4 py-3">
                <dt class="text-gray-500">Amount</dt>
                <dd class="flex items-start gap-x-2">
                  <div class="font-medium text-gray-900">$7,600.00</div>
                  <div class="rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-green-600/20 ring-inset">Paid</div>
                </dd>
              </div>
            </dl>
          </li>
        </ul>
      </div>
    </div>
  </div>
</main>
