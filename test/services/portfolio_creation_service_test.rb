require "test_helper"

class PortfolioCreationServiceTest < ActiveSupport::TestCase
  def setup
    @artist_profile = create(:artist_profile)
    @post = create(:post, artist_profile: @artist_profile, caption: "Test caption")
  end

  test "create_from_post should create portfolio item from image post" do
    assert_difference 'PortfolioItem.count', 1 do
      portfolio_item = PortfolioCreationService.create_from_post(@post, @artist_profile)
      
      assert portfolio_item.persisted?
      assert_equal @artist_profile, portfolio_item.artist_profile
      assert_equal "Test caption", portfolio_item.caption
      assert portfolio_item.image.attached?
      assert_equal 0, portfolio_item.position
    end
  end

  test "create_from_post should not create portfolio item from text post" do
    text_post = Post.create!(
      artist_profile: @artist_profile,
      post_type: :text,
      body: "Text content"
    )
    
    assert_no_difference 'PortfolioItem.count' do
      result = PortfolioCreationService.create_from_post(text_post, @artist_profile)
      assert_nil result
    end
  end

  test "create_from_post should not create portfolio item from post without image" do
    post_without_image = Post.new(artist_profile: @artist_profile)
    post_without_image.save(validate: false)
    
    assert_no_difference 'PortfolioItem.count' do
      result = PortfolioCreationService.create_from_post(post_without_image, @artist_profile)
      assert_nil result
    end
  end

  test "create_from_post should set correct position based on existing items" do
    existing_item1 = create(:portfolio_item, artist_profile: @artist_profile, position: 0)
    existing_item2 = create(:portfolio_item, artist_profile: @artist_profile, position: 2)
    
    portfolio_item = PortfolioCreationService.create_from_post(@post, @artist_profile)
    
    assert_equal 3, portfolio_item.position
  end

  test "create_direct should create portfolio item with provided attributes" do
    image = fixture_file_upload('test_image.jpg', 'image/jpeg')
    
    assert_difference 'PortfolioItem.count', 1 do
      portfolio_item = PortfolioCreationService.create_direct(
        @artist_profile,
        image,
        caption: "Direct caption"
      )
      
      assert portfolio_item.persisted?
      assert_equal @artist_profile, portfolio_item.artist_profile
      assert_equal "Direct caption", portfolio_item.caption
      assert portfolio_item.image.attached?
      assert_equal 0, portfolio_item.position
    end
  end

  test "create_direct should work without caption" do
    image = fixture_file_upload('test_image.jpg', 'image/jpeg')
    
    portfolio_item = PortfolioCreationService.create_direct(@artist_profile, image)
    
    assert portfolio_item.persisted?
    assert_nil portfolio_item.caption
  end

  test "create_direct should work without image" do
    portfolio_item = PortfolioCreationService.create_direct(
      @artist_profile,
      nil,
      caption: "No image caption"
    )
    
    # Should create but be invalid due to missing image
    assert portfolio_item.persisted? == false
    assert_not portfolio_item.valid?
  end

  private

  def fixture_file_upload(filename, content_type)
    file_path = Rails.root.join('test', 'fixtures', 'files', filename)
    Rack::Test::UploadedFile.new(file_path, content_type)
  end
end