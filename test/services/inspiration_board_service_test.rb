require "test_helper"

class InspirationBoardServiceTest < ActiveSupport::TestCase
  def setup
    @user = create(:user, :approved)
    @board = create(:inspiration_board, user: @user)
    @post = create(:post)
    @artist_profile = create(:artist_profile)
    @portfolio_item = create(:portfolio_item, artist_profile: @artist_profile)
  end

  test "add_post_to_board should create inspiration board item from post" do
    assert_difference 'InspirationBoardItem.count', 1 do
      item = InspirationBoardService.add_post_to_board(@board, @post, notes: "Great design")
      
      assert item.persisted?
      assert_equal @board, item.inspiration_board
      assert_equal @post, item.post
      assert_nil item.portfolio_item
      assert_equal "Great design", item.notes
    end
  end

  test "add_post_to_board should work without notes" do
    item = InspirationBoardService.add_post_to_board(@board, @post)
    
    assert item.persisted?
    assert_nil item.notes
  end

  test "add_post_to_board should not create duplicate entries" do
    # Create initial item
    InspirationBoardService.add_post_to_board(@board, @post)
    
    assert_no_difference 'InspirationBoardItem.count' do
      result = InspirationBoardService.add_post_to_board(@board, @post)
      assert_nil result
    end
  end

  test "add_portfolio_item_to_board should create inspiration board item from portfolio item" do
    assert_difference 'InspirationBoardItem.count', 1 do
      item = InspirationBoardService.add_portfolio_item_to_board(@board, @portfolio_item, notes: "Love this style")
      
      assert item.persisted?
      assert_equal @board, item.inspiration_board
      assert_equal @portfolio_item, item.portfolio_item
      assert_nil item.post
      assert_equal "Love this style", item.notes
    end
  end

  test "add_portfolio_item_to_board should work without notes" do
    item = InspirationBoardService.add_portfolio_item_to_board(@board, @portfolio_item)
    
    assert item.persisted?
    assert_nil item.notes
  end

  test "add_portfolio_item_to_board should not create duplicate entries" do
    # Create initial item
    InspirationBoardService.add_portfolio_item_to_board(@board, @portfolio_item)
    
    assert_no_difference 'InspirationBoardItem.count' do
      result = InspirationBoardService.add_portfolio_item_to_board(@board, @portfolio_item)
      assert_nil result
    end
  end

  test "update_notes should update existing item notes" do
    item = InspirationBoardService.add_post_to_board(@board, @post, notes: "Original notes")
    
    InspirationBoardService.update_notes(item, "Updated notes")
    
    item.reload
    assert_equal "Updated notes", item.notes
  end

  test "update_notes should allow clearing notes" do
    item = InspirationBoardService.add_post_to_board(@board, @post, notes: "Some notes")
    
    InspirationBoardService.update_notes(item, nil)
    
    item.reload
    assert_nil item.notes
  end

  test "update_notes should allow blank notes" do
    item = InspirationBoardService.add_post_to_board(@board, @post, notes: "Some notes")
    
    InspirationBoardService.update_notes(item, "")
    
    item.reload
    assert_equal "", item.notes
  end
end