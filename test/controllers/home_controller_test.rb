require "test_helper"

class HomeControllerTest < ActionDispatch::IntegrationTest
  def setup
    @client_user = create(:user, :client, :approved)
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @post = create(:post, artist_profile: @artist_profile)
  end

  test "should get index without authentication" do
    get root_path
    assert_response :success
    assert_select 'h1', 'Find Your Perfect Tattoo Artist'
  end

  test "should show general posts for unauthenticated users" do
    get root_path
    assert_response :success
    assert assigns(:posts)
    assert_not assigns(:showing_feed)
  end

  test "should show general posts for artists" do
    sign_in_as(@artist_user)
    get root_path
    assert_response :success
    assert assigns(:posts)
    assert_not assigns(:showing_feed)
  end

  test "should show feed for approved clients" do
    sign_in_as(@client_user)
    get root_path
    assert_response :success
    assert assigns(:posts)
    assert assigns(:showing_feed), "Expected @showing_feed to be true for approved client user"
  end

  test "should show followed artists posts in feed" do
    # Client follows the artist
    follow = create(:follow, client_profile: @client_user.client_profile, artist_profile: @artist_profile)
    
    sign_in_as(@client_user)
    get root_path
    assert_response :success
    
    # Should show posts from followed artists
    assert_includes assigns(:posts), @post
  end

  test "should show recent posts when client follows no one" do
    sign_in_as(@client_user)
    get root_path
    assert_response :success
    
    # Should show recent posts since not following anyone
    assert_includes assigns(:posts), @post
  end

  test "should show personalized feed layout for clients" do
    sign_in_as(@client_user)
    get root_path
    assert_response :success
    assert_select 'h2', 'Your Feed'
  end

  test "should show quick actions for non-feed users" do
    get root_path
    assert_response :success
    assert_select 'h3', 'Browse Artists'
    assert_select 'h3', 'Get Inspired'
  end

  test "should limit followed artists posts to 20" do
    # Create many posts from followed artist
    create_list(:post, 25, artist_profile: @artist_profile)
    create(:follow, client_profile: @client_user.client_profile, artist_profile: @artist_profile)
    
    sign_in_as(@client_user)
    get root_path
    assert_response :success
    
    # Should limit to 20 posts
    assert_operator assigns(:posts).count, :<=, 20
  end

  test "should limit general posts to 10" do
    create_list(:post, 15, artist_profile: @artist_profile)
    
    get root_path
    assert_response :success
    
    # Should limit to 10 posts for general feed
    assert_operator assigns(:posts).count, :<=, 10
  end
end
