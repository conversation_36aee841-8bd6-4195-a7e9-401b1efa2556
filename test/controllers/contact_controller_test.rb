require "test_helper"

class ContactControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = create(:user, :client, :approved)
    @valid_params = {
      name: "<PERSON>",
      email: "<EMAIL>",
      subject: "Question about services",
      message: "I have a question about your tattoo services."
    }
  end

  test "should get new" do
    get new_contact_path
    assert_response :success
    assert_select "form"
    assert_select "input[name='name']"
    assert_select "input[name='email']"
    assert_select "input[name='subject']"
    assert_select "textarea[name='message']"
    assert_select "input[type='submit']"
  end

  test "authenticated user can access contact form" do
    sign_in_as(@user)
    get new_contact_path
    assert_response :success
  end

  test "unauthenticated user can access contact form" do
    get new_contact_path
    assert_response :success
  end

  test "should create contact message with valid params" do
    assert_emails 1 do
      post contact_path, params: @valid_params
    end
    
    assert_redirected_to contact_path
    assert_equal "Thank you for your message! We'll get back to you soon.", flash[:notice]
  end

  test "should send email with correct parameters" do
    perform_enqueued_jobs do
      post contact_path, params: @valid_params
    end
    
    # Verify email was sent with correct parameters
    assert_enqueued_email_with ContactMailer, :new_message, args: [
      @valid_params[:name],
      @valid_params[:email], 
      @valid_params[:subject],
      @valid_params[:message]
    ]
  end

  test "should not create message without name" do
    invalid_params = @valid_params.merge(name: "")
    
    assert_no_emails do
      post contact_path, params: invalid_params
    end
    
    assert_response :success
    assert_template :new
    assert_equal "Please fill in all required fields.", flash[:alert]
  end

  test "should not create message without email" do
    invalid_params = @valid_params.merge(email: "")
    
    assert_no_emails do
      post contact_path, params: invalid_params
    end
    
    assert_response :success
    assert_template :new
    assert_equal "Please fill in all required fields.", flash[:alert]
  end

  test "should not create message without message content" do
    invalid_params = @valid_params.merge(message: "")
    
    assert_no_emails do
      post contact_path, params: invalid_params
    end
    
    assert_response :success
    assert_template :new
    assert_equal "Please fill in all required fields.", flash[:alert]
  end

  test "should create message without subject" do
    # Subject is optional based on the controller logic
    params_without_subject = @valid_params.merge(subject: "")
    
    assert_emails 1 do
      post contact_path, params: params_without_subject
    end
    
    assert_redirected_to contact_path
    assert_equal "Thank you for your message! We'll get back to you soon.", flash[:notice]
  end

  test "should handle nil parameters gracefully" do
    assert_no_emails do
      post contact_path, params: { name: nil, email: nil, message: nil }
    end
    
    assert_response :success
    assert_template :new
    assert_equal "Please fill in all required fields.", flash[:alert]
  end

  test "should handle whitespace-only parameters" do
    whitespace_params = {
      name: "   ",
      email: "   ",
      message: "   "
    }
    
    assert_no_emails do
      post contact_path, params: whitespace_params
    end
    
    assert_response :success
    assert_template :new
    assert_equal "Please fill in all required fields.", flash[:alert]
  end

  test "should get show (success page)" do
    get contact_success_path
    assert_response :success
  end

  test "success page accessible without authentication" do
    get contact_success_path
    assert_response :success
  end

  test "contact form preserves entered data on validation error" do
    invalid_params = @valid_params.merge(name: "")
    
    post contact_path, params: invalid_params
    
    assert_response :success
    assert_template :new
    
    # Check that valid data is preserved
    assert_select "input[name='email'][value='#{@valid_params[:email]}']"
    assert_select "input[name='subject'][value='#{@valid_params[:subject]}']"
    assert_select "textarea[name='message']", text: @valid_params[:message]
  end

  test "should handle email delivery failures gracefully" do
    # Mock email delivery failure
    ContactMailer.stub :new_message, -> (*args) { raise StandardError.new("Email delivery failed") } do
      assert_raises(StandardError) do
        post contact_path, params: @valid_params
      end
    end
  end

  test "should sanitize input parameters" do
    malicious_params = {
      name: "<script>alert('xss')</script>John",
      email: "<EMAIL>",
      subject: "<img src=x onerror=alert('xss')>",
      message: "Normal message content"
    }
    
    assert_emails 1 do
      post contact_path, params: malicious_params
    end
    
    # The email should be sent but with the original content
    # (Rails doesn't automatically sanitize params, but email rendering should handle it)
    assert_redirected_to contact_path
  end

  test "should accept long messages" do
    long_message = "a" * 5000 # Very long message
    long_params = @valid_params.merge(message: long_message)
    
    assert_emails 1 do
      post contact_path, params: long_params
    end
    
    assert_redirected_to contact_path
    assert_equal "Thank you for your message! We'll get back to you soon.", flash[:notice]
  end

  test "should handle special characters in all fields" do
    special_char_params = {
      name: "José María González",
      email: "josé@example.com",
      subject: "Información sobre tatuajes",
      message: "¿Cuánto cuesta un tatuaje de 10cm²?"
    }
    
    assert_emails 1 do
      post contact_path, params: special_char_params
    end
    
    assert_redirected_to contact_path
  end

  test "contact form works for both authenticated and unauthenticated users" do
    # Test unauthenticated
    assert_emails 1 do
      post contact_path, params: @valid_params
    end
    assert_redirected_to contact_path
    
    # Test authenticated
    sign_in_as(@user)
    assert_emails 1 do
      post contact_path, params: @valid_params.merge(name: "Authenticated User")
    end
    assert_redirected_to contact_path
  end

  private

  def sign_in_as(user)
    post session_path, params: { email_address: user.email_address, password: "password123" }
  end
end