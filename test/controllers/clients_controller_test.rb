require "test_helper"

class ClientsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @client_user = create(:user, :client, :approved)
    @client_profile = @client_user.client_profile
    @other_client = create(:user, :client, :approved)
    @tattoo_item = create(:tattoo_item, client_profile: @client_profile, artist_name: "Test Artist")
  end

  test "should get show" do
    get client_path(@client_profile)
    assert_response :success
  end

  test "should get index" do
    get clients_path
    assert_response :success
  end

  test "should get tattoos without authentication" do
    get tattoos_client_path(@client_profile)
    assert_response :success
    assert_select "h2", "Tattoo Collection"
  end

  test "should get tattoos for authenticated users" do
    sign_in_as(@other_client)
    get tattoos_client_path(@client_profile)
    assert_response :success
    assert_select "h2", "Tattoo Collection"
  end

  test "should show add tattoo button for owner" do
    sign_in_as(@client_user)
    get tattoos_client_path(@client_profile)
    assert_response :success
    assert_select "a[href=?]", new_client_tattoo_item_path(@client_profile), text: "Add Tattoo"
  end

  test "should not show add tattoo button for non-owner" do
    sign_in_as(@other_client)
    get tattoos_client_path(@client_profile)
    assert_response :success
    assert_select "a[href=?]", new_client_tattoo_item_path(@client_profile), count: 0
  end

  test "should not show add tattoo button for unauthenticated users" do
    get tattoos_client_path(@client_profile)
    assert_response :success
    assert_select "a[href=?]", new_client_tattoo_item_path(@client_profile), count: 0
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end
