require "test_helper"

class ArtistsSearchTest < ActionDispatch::IntegrationTest
  def setup
    @traditional_specialty = create(:specialty, :traditional)
    @japanese_specialty = create(:specialty, :japanese)
    
    # Create approved artists
    @artist1 = create(:user, :artist, :approved)
    @artist_profile1 = @artist1.artist_profile
    @artist_profile1.update!(
      name: "<PERSON>",
      location: "New York, NY", 
      biography: "Specializing in traditional American tattoos with over 10 years experience"
    )
    create(:artist_specialty, artist_profile: @artist_profile1, specialty: @traditional_specialty)
    
    @artist2 = create(:user, :artist, :approved)
    @artist_profile2 = @artist2.artist_profile
    @artist_profile2.update!(
      name: "<PERSON>",
      location: "Los Angeles, CA",
      biography: "Creating beautiful Japanese-inspired artwork and sleeve designs"
    )
    create(:artist_specialty, artist_profile: @artist_profile2, specialty: @japanese_specialty)
    
    @artist3 = create(:user, :artist, :approved)
    @artist_profile3 = @artist3.artist_profile
    @artist_profile3.update!(
      name: "<PERSON>",
      location: "San Francisco, CA",
      biography: "Modern tattoo artist focusing on geometric and minimalist designs"
    )
    
    # Create unapproved artist (should not appear in search)
    @unapproved_artist = create(:user, :artist, approved: false)
  end

  test "should display all approved artists when no search query" do
    get artists_path
    assert_response :success
    
    assert_select 'h1', 'Discover Artists'
    assert_select '.grid .bg-white', count: 3 # Should show 3 approved artists
    assert_select 'h3', text: @artist_profile1.name
    assert_select 'h3', text: @artist_profile2.name
    assert_select 'h3', text: @artist_profile3.name
  end

  test "should search by artist name" do
    get artists_path, params: { search: "John" }
    assert_response :success
    
    assert_includes assigns(:artist_profiles), @artist_profile1
    assert_not_includes assigns(:artist_profiles), @artist_profile2
    assert_not_includes assigns(:artist_profiles), @artist_profile3
    
    assert_select 'h3', text: @artist_profile1.name
    assert_select 'h3', text: @artist_profile2.name, count: 0
  end

  test "should search by location" do
    get artists_path, params: { search: "Los Angeles" }
    assert_response :success
    
    assert_includes assigns(:artist_profiles), @artist_profile2
    assert_not_includes assigns(:artist_profiles), @artist_profile1
    assert_not_includes assigns(:artist_profiles), @artist_profile3
  end

  test "should search by biography content" do
    get artists_path, params: { search: "geometric" }
    assert_response :success
    
    assert_includes assigns(:artist_profiles), @artist_profile3
    assert_not_includes assigns(:artist_profiles), @artist_profile1
    assert_not_includes assigns(:artist_profiles), @artist_profile2
  end

  test "should search by specialty title" do
    get artists_path, params: { search: "Traditional" }
    assert_response :success
    
    assert_includes assigns(:artist_profiles), @artist_profile1
    assert_not_includes assigns(:artist_profiles), @artist_profile2
    assert_not_includes assigns(:artist_profiles), @artist_profile3
  end

  test "should search case insensitively" do
    get artists_path, params: { search: "JAPANESE" }
    assert_response :success
    
    assert_includes assigns(:artist_profiles), @artist_profile2
  end

  test "should search with partial matches" do
    get artists_path, params: { search: "Tat" }
    assert_response :success
    
    # Should match "John Tattoo Smith"
    assert_includes assigns(:artist_profiles), @artist_profile1
  end

  test "should return empty results for non-matching search" do
    get artists_path, params: { search: "nonexistent" }
    assert_response :success
    
    assert_empty assigns(:artist_profiles)
    assert_select '.text-center h3', text: 'No artists found'
  end

  test "should not include unapproved artists in search results" do
    get artists_path, params: { search: @unapproved_artist.artist_profile.name }
    assert_response :success
    
    assert_not_includes assigns(:artist_profiles), @unapproved_artist.artist_profile
  end

  test "should display search query in results" do
    search_term = "Traditional"
    get artists_path, params: { search: search_term }
    assert_response :success
    
    assert_select 'p', text: /Found.*matching "#{search_term}"/
    assert_equal search_term, assigns(:search_query)
  end

  test "should show clear search link when searching" do
    get artists_path, params: { search: "test" }
    assert_response :success
    
    assert_select 'a[href=?]', artists_path, text: 'Clear Search'
  end

  test "should display total artist count" do
    get artists_path
    assert_response :success
    
    assert_equal 3, assigns(:total_artists)
    assert_select 'p', text: /Showing.*of.*3.*artists/
  end

  test "should limit search results to 50" do
    # This test verifies the limit is in place, though we can't easily test with 50+ records in test
    get artists_path, params: { search: "a" } # Search that might match many
    assert_response :success
    
    assert_operator assigns(:artist_profiles).count, :<=, 50
  end

  test "should show specialties in artist cards" do
    get artists_path
    assert_response :success
    
    assert_select '.bg-indigo-100', text: @traditional_specialty.title
    assert_select '.bg-indigo-100', text: @japanese_specialty.title
  end

  test "should show follow buttons for authenticated clients" do
    client_user = create(:user, :client, :approved)
    sign_in_as(client_user)
    
    get artists_path
    assert_response :success
    
    assert_select 'button', text: 'Follow', count: 3
  end

  test "should not show follow buttons for unauthenticated users" do
    get artists_path
    assert_response :success
    
    assert_select 'button', text: 'Follow', count: 0
  end

  test "should not show follow buttons for artists" do
    sign_in_as(@artist1)
    
    get artists_path
    assert_response :success
    
    assert_select 'button', text: 'Follow', count: 0
  end

  test "should show following status for followed artists" do
    client_user = create(:user, :client, :approved)
    create(:follow, client_profile: client_user.client_profile, artist_profile: @artist_profile1)
    sign_in_as(client_user)
    
    get artists_path
    assert_response :success
    
    assert_select 'button', text: 'Following', count: 1
    assert_select 'button', text: 'Follow', count: 2
  end

  test "should display artist stats" do
    # Create some posts and followers
    create_list(:post, 3, artist_profile: @artist_profile1)
    client = create(:user, :client, :approved)
    create(:follow, client_profile: client.client_profile, artist_profile: @artist_profile1)
    
    get artists_path
    assert_response :success
    
    assert_select '.text-xs', text: '3 posts'
    assert_select '.text-xs', text: '1 follower'
  end

  test "should show available status" do
    @artist_profile1.update!(available: true)
    @artist_profile2.update!(available: false)
    
    get artists_path
    assert_response :success
    
    assert_select '.bg-green-100', text: 'Available', count: 2 # artist1 and artist3 (default true)
  end
end