require "test_helper"

class InspirationBoardItemsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = create(:user, :approved)
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @post = create(:post, artist_profile: @artist_profile)
    @board = create(:inspiration_board, user: @user)
    @other_board = create(:inspiration_board, user: @user)
    @item = create(:inspiration_board_item, inspiration_board: @board, post: @post)
  end

  test "should redirect to login when not authenticated" do
    post inspiration_board_items_path, params: {
      post_id: @post.id,
      inspiration_board_id: @board.id
    }
    assert_redirected_to new_session_path
  end

  test "should redirect unapproved users" do
    unapproved_user = create(:user, approved: false)
    sign_in_as(unapproved_user)
    
    post inspiration_board_items_path, params: {
      post_id: @post.id,
      inspiration_board_id: @board.id
    }
    
    assert_redirected_to root_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should create inspiration board item" do
    sign_in_as(@user)
    new_post = create(:post, artist_profile: @artist_profile)
    
    assert_difference '@board.inspiration_board_items.count', 1 do
      post inspiration_board_items_path, params: {
        post_id: new_post.id,
        inspiration_board_id: @board.id,
        notes: "Love this design!"
      }
    end
    
    assert_redirected_to new_post
    assert_equal 'Post saved to inspiration board.', flash[:notice]
    
    created_item = @board.inspiration_board_items.last
    assert_equal new_post, created_item.post
    assert_equal "Love this design!", created_item.notes
  end

  test "should not create duplicate items in same board" do
    sign_in_as(@user)
    
    # Try to add same post to same board again
    assert_no_difference '@board.inspiration_board_items.count' do
      post inspiration_board_items_path, params: {
        post_id: @post.id,
        inspiration_board_id: @board.id
      }
    end
    
    assert_redirected_to @post
    assert_includes flash[:alert], "already in this inspiration board"
  end

  test "should allow same post in different boards" do
    sign_in_as(@user)
    
    # Same post in different board should work
    assert_difference '@other_board.inspiration_board_items.count', 1 do
      post inspiration_board_items_path, params: {
        post_id: @post.id,
        inspiration_board_id: @other_board.id
      }
    end
    
    assert_redirected_to @post
    assert_equal 'Post saved to inspiration board.', flash[:notice]
  end

  test "should not save to other user's board" do
    other_user = create(:user, :approved)
    other_board = create(:inspiration_board, user: other_user)
    sign_in_as(@user)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      post inspiration_board_items_path, params: {
        post_id: @post.id,
        inspiration_board_id: other_board.id
      }
    end
  end

  test "should update notes on own item" do
    sign_in_as(@user)
    
    patch inspiration_board_item_path(@item), params: {
      inspiration_board_item: {
        notes: "Updated notes"
      }
    }
    
    assert_redirected_to @board
    assert_equal 'Notes updated.', flash[:notice]
    
    @item.reload
    assert_equal "Updated notes", @item.notes
  end

  test "should not update other user's item" do
    other_user = create(:user, :approved)
    sign_in_as(other_user)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      patch inspiration_board_item_path(@item), params: {
        inspiration_board_item: {
          notes: "Hacked notes"
        }
      }
    end
  end

  test "should destroy own item" do
    sign_in_as(@user)
    
    assert_difference '@board.inspiration_board_items.count', -1 do
      delete inspiration_board_item_path(@item)
    end
    
    assert_redirected_to @board
    assert_equal 'Post removed from inspiration board.', flash[:notice]
  end

  test "should not destroy other user's item" do
    other_user = create(:user, :approved)
    sign_in_as(other_user)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      delete inspiration_board_item_path(@item)
    end
  end

  test "should move item between own boards" do
    sign_in_as(@user)
    
    patch move_inspiration_board_item_path(@item), params: {
      target_board_id: @other_board.id
    }
    
    assert_redirected_to @other_board
    assert_equal 'Post moved to different board.', flash[:notice]
    
    @item.reload
    assert_equal @other_board, @item.inspiration_board
  end

  test "should not move item to other user's board" do
    other_user = create(:user, :approved)
    other_board = create(:inspiration_board, user: other_user)
    sign_in_as(@user)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      patch move_inspiration_board_item_path(@item), params: {
        target_board_id: other_board.id
      }
    end
  end

  test "should handle moving to same board gracefully" do
    sign_in_as(@user)
    
    patch move_inspiration_board_item_path(@item), params: {
      target_board_id: @board.id
    }
    
    # Should still work, just no actual change
    assert_redirected_to @board
    assert_equal 'Post moved to different board.', flash[:notice]
    
    @item.reload
    assert_equal @board, @item.inspiration_board
  end

  test "should not duplicate when moving to board that already has the post" do
    # Add same post to target board first
    create(:inspiration_board_item, inspiration_board: @other_board, post: @post)
    sign_in_as(@user)
    
    # This should fail due to uniqueness constraint
    assert_raises(ActiveRecord::RecordInvalid) do
      patch move_inspiration_board_item_path(@item), params: {
        target_board_id: @other_board.id
      }
    end
  end

  test "should create item without notes" do
    sign_in_as(@user)
    new_post = create(:post, artist_profile: @artist_profile)
    
    assert_difference '@board.inspiration_board_items.count', 1 do
      post inspiration_board_items_path, params: {
        post_id: new_post.id,
        inspiration_board_id: @board.id
        # No notes parameter
      }
    end
    
    created_item = @board.inspiration_board_items.last
    assert_nil created_item.notes
  end
end