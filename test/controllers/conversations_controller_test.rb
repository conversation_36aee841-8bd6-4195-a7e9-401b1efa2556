require "test_helper"

class ConversationsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user1 = create(:user, :client, :approved)
    @user2 = create(:user, :artist, :approved)
    @conversation = create(:conversation, user1: @user1, user2: @user2)
    @message = create(:message, conversation: @conversation, sender: @user2, content: "Hello!")
  end

  test "should redirect to login when not authenticated" do
    get conversations_path
    assert_response :redirect
  end

  test "should get index when authenticated" do
    sign_in_as(@user1)
    get conversations_path
    assert_response :success
    assert_select "h1", "Messages"
  end

  test "should show user's conversations in index" do
    sign_in_as(@user1)
    get conversations_path
    assert_response :success
    assert assigns(:conversations).include?(@conversation)
  end

  test "should show unread count in index" do
    create(:message, conversation: @conversation, sender: @user2, read: false)
    sign_in_as(@user1)
    get conversations_path
    assert_response :success
    assert assigns(:unread_count) > 0
  end

  test "should not show other users' conversations" do
    user3 = create(:user, :client, :approved)
    sign_in_as(user3)
    get conversations_path
    assert_response :success
    assert_not assigns(:conversations).include?(@conversation)
  end

  test "should get show when authenticated and participant" do
    sign_in_as(@user1)
    get conversation_path(@conversation)
    assert_response :success
  end

  test "should mark messages as read when viewing conversation" do
    unread_message = create(:message, conversation: @conversation, sender: @user2, read: false)
    
    sign_in_as(@user1)
    get conversation_path(@conversation)
    assert_response :success
    
    unread_message.reload
    assert unread_message.read?
  end

  test "should not mark own messages as read" do
    own_message = create(:message, conversation: @conversation, sender: @user1, read: false)
    
    sign_in_as(@user1)
    get conversation_path(@conversation)
    assert_response :success
    
    own_message.reload
    assert_not own_message.read?
  end

  test "should redirect when trying to access conversation not participant of" do
    user3 = create(:user, :client, :approved)
    sign_in_as(user3)
    get conversation_path(@conversation)
    assert_response :redirect
    assert_redirected_to conversations_path
  end

  test "should redirect when other user is blocked" do
    create(:block, blocker: @user1, blocked: @user2)
    sign_in_as(@user1)
    get conversation_path(@conversation)
    assert_response :redirect
    assert_redirected_to conversations_path
  end

  test "should redirect when blocked by other user" do
    create(:block, blocker: @user2, blocked: @user1)
    sign_in_as(@user1)
    get conversation_path(@conversation)
    assert_response :redirect
    assert_redirected_to conversations_path
  end

  test "should redirect when artist has messages disabled" do
    @user2.artist_profile.update!(messages_enabled: false)
    sign_in_as(@user1)
    get conversation_path(@conversation)
    assert_response :redirect
    assert_redirected_to conversations_path
  end

  test "should show conversation with message form" do
    sign_in_as(@user1)
    get conversation_path(@conversation)
    assert_response :success
    assert_select "form[action=?]", conversation_messages_path(@conversation)
    assert assigns(:message)
    assert assigns(:other_user)
    assert assigns(:messages)
  end
end
