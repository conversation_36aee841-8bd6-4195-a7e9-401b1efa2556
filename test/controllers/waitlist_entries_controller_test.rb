require "test_helper"

class WaitlistEntriesControllerTest < ActionDispatch::IntegrationTest
  include ActiveJob::<PERSON><PERSON><PERSON><PERSON>
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @artist_profile.update!(waitlist_enabled: true)
    
    @client_user = create(:user, :client, :approved)
    @other_artist = create(:user, :artist, :approved)
    
    @waitlist_entry = create(:waitlist_entry, artist_profile: @artist_profile)
  end

  # GET /artists/:artist_slug/waitlist_entries/new
  test "should get new when waitlist is enabled" do
    get new_artist_waitlist_entry_path(@artist_profile)
    assert_response :success
    assert_select "h1", text: /Join.*Waitlist/
    assert_select "form"
  end

  test "should redirect when waitlist is disabled" do
    @artist_profile.update!(waitlist_enabled: false)
    
    get new_artist_waitlist_entry_path(@artist_profile)
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Waitlist is not enabled for this artist.", flash[:alert]
  end

  test "should get new without authentication" do
    # Test that guests can access the form
    get new_artist_waitlist_entry_path(@artist_profile)
    assert_response :success
  end

  # POST /artists/:artist_slug/waitlist_entries
  test "should create waitlist entry with valid params" do
    assert_difference "WaitlistEntry.count", 1 do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Test User",
          email: "<EMAIL>",
          phone_number: "555-1234",
          message: "I'd love a tattoo!"
        }
      }
    end
    
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Successfully joined the waitlist!", flash[:notice]
    
    entry = WaitlistEntry.last
    assert_equal "Test User", entry.name
    assert_equal "<EMAIL>", entry.email
    assert_equal @artist_profile, entry.artist_profile
    assert entry.pending?
  end

  test "should not create waitlist entry with invalid params" do
    assert_no_difference "WaitlistEntry.count" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "",
          email: "invalid-email"
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select ".text-red-800", /problems with your submission/
  end

  test "should not create duplicate email for same artist" do
    assert_no_difference "WaitlistEntry.count" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Different Name",
          email: @waitlist_entry.email
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should create entry with same email for different artist" do
    other_artist = create(:artist_profile, waitlist_enabled: true)
    
    assert_difference "WaitlistEntry.count", 1 do
      post artist_waitlist_entries_path(other_artist), params: {
        waitlist_entry: {
          name: "Same Email User",
          email: @waitlist_entry.email
        }
      }
    end
    
    assert_redirected_to artist_path(other_artist)
  end

  test "should not create entry when waitlist disabled" do
    @artist_profile.update!(waitlist_enabled: false)
    
    assert_no_difference "WaitlistEntry.count" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Test User",
          email: "<EMAIL>"
        }
      }
    end
    
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Waitlist is not enabled for this artist.", flash[:alert]
  end

  # GET /artists/:artist_slug/waitlist_entries/:id
  test "should show waitlist entry to artist owner" do
    sign_in_as(@artist_user)
    
    get artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    assert_response :success
    assert_select "h1", text: "Waitlist Entry"
    assert_select "h2", text: @waitlist_entry.name
  end

  test "should not show waitlist entry to non-owner" do
    sign_in_as(@client_user)
    
    get artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Access denied.", flash[:alert]
  end

  test "should not show waitlist entry to other artist" do
    sign_in_as(@other_artist)
    
    get artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Access denied.", flash[:alert]
  end

  test "should not show waitlist entry without authentication" do
    get artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    assert_redirected_to new_session_path
  end

  # PATCH /artists/:artist_slug/waitlist_entries/:id/mark_as_contacted
  test "should mark entry as contacted by artist owner" do
    sign_in_as(@artist_user)
    assert @waitlist_entry.pending?
    
    patch mark_as_contacted_artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    
    assert_redirected_to waitlist_artist_path(@artist_profile)
    assert_equal "Marked as contacted.", flash[:notice]
    
    @waitlist_entry.reload
    assert @waitlist_entry.contacted?
  end

  test "should not mark as contacted by non-owner" do
    sign_in_as(@client_user)
    
    patch mark_as_contacted_artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Access denied.", flash[:alert]
    
    @waitlist_entry.reload
    assert @waitlist_entry.pending?
  end

  # DELETE /artists/:artist_slug/waitlist_entries/:id
  test "should destroy waitlist entry by artist owner" do
    sign_in_as(@artist_user)
    
    assert_difference "WaitlistEntry.count", -1 do
      delete artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    end
    
    assert_redirected_to waitlist_artist_path(@artist_profile)
    assert_equal "Waitlist entry removed.", flash[:notice]
  end

  test "should not destroy entry by non-owner" do
    sign_in_as(@client_user)
    
    assert_no_difference "WaitlistEntry.count" do
      delete artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    end
    
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Access denied.", flash[:alert]
  end

  test "should not destroy entry without authentication" do
    assert_no_difference "WaitlistEntry.count" do
      delete artist_waitlist_entry_path(@artist_profile, @waitlist_entry)
    end
    
    assert_redirected_to new_session_path
  end

  # Email notification tests
  test "should send email notification when entry is created" do
    assert_enqueued_jobs 1, only: ActionMailer::MailDeliveryJob do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Test User",
          email: "<EMAIL>"
        }
      }
    end
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end