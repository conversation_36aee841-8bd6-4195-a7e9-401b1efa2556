require "test_helper"

class ApplicationControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = create(:user, :client, :approved)
    @unapproved_user = create(:user, :client, approved: false)
  end

  test "authenticated user can access protected resources" do
    sign_in_as(@user)
    get artists_path
    assert_response :success
  end

  test "unauthenticated user is redirected to login" do
    get artists_path
    assert_redirected_to new_session_path
  end

  test "return_to_after_authenticating is set for protected resources" do
    get artists_path
    assert_equal artists_url, session[:return_to_after_authenticating]
  end

  test "authenticated helper returns true for authenticated users" do
    sign_in_as(@user)
    get root_path
    assert_response :success
    # We can't directly test the helper method in controller tests,
    # but we can verify authentication worked by checking protected resources
  end

  test "current_user is available for authenticated users" do
    sign_in_as(@user)
    get root_path
    assert_response :success
    # Indirect test - if authentication works, current_user is working
  end

  test "session persists across requests" do
    sign_in_as(@user)
    
    # First request
    get root_path
    assert_response :success
    
    # Second request should still be authenticated
    get artists_path
    assert_response :success
  end

  test "invalid session cookie is handled gracefully" do
    # Set an invalid session cookie
    cookies.signed[:session_id] = "invalid_session_id"
    
    get artists_path
    assert_redirected_to new_session_path
  end

  test "browser version requirement allows modern browsers" do
    # Modern browser user agent
    get root_path, headers: { 
      "User-Agent" => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    assert_response :success
  end

  test "authentication concern is included properly" do
    assert ApplicationController.included_modules.include?(Authentication)
  end

  test "session cleanup on termination" do
    sign_in_as(@user)
    session_id = Current.session.id
    
    delete session_path
    assert_redirected_to new_session_path
    
    # Verify session was destroyed in database
    assert_raises(ActiveRecord::RecordNotFound) do
      Session.find(session_id)
    end
  end

  test "multiple sessions for same user are allowed" do
    # First session
    post session_path, params: { email_address: @user.email_address, password: "password123" }
    first_session_id = Current.session.id
    
    # Reset current session
    Current.session = nil
    cookies.delete(:session_id)
    
    # Second session (simulating different browser/device)
    post session_path, params: { email_address: @user.email_address, password: "password123" }
    second_session_id = Current.session.id
    
    assert_not_equal first_session_id, second_session_id
    assert Session.exists?(first_session_id)
    assert Session.exists?(second_session_id)
  end

  private

  def sign_in_as(user)
    post session_path, params: { email_address: user.email_address, password: "password123" }
  end
end