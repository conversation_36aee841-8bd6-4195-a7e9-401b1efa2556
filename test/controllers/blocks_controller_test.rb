require "test_helper"

class BlocksControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user1 = create(:user, :client, :approved)
    @user2 = create(:user, :artist, :approved)
    @block = create(:block, blocker: @user1, blocked: @user2)
  end

  test "should redirect to login when not authenticated" do
    post blocks_path, params: { blocked_id: @user2.id }
    assert_response :redirect
  end

  test "should create block when authenticated" do
    user3 = create(:user, :client, :approved)
    sign_in_as(@user1)
    
    assert_difference 'Block.count', 1 do
      post blocks_path, params: { blocked_id: user3.id }
    end
    
    assert_response :redirect
    assert Block.exists?(blocker: @user1, blocked: user3)
  end

  test "should not allow user to block themselves" do
    sign_in_as(@user1)
    
    assert_no_difference 'Block.count' do
      post blocks_path, params: { blocked_id: @user1.id }
    end
    
    assert_response :redirect
  end

  test "should not create duplicate block" do
    sign_in_as(@user1)
    
    # Try to block user2 again (already blocked in setup)
    assert_no_difference 'Block.count' do
      post blocks_path, params: { blocked_id: @user2.id }
    end
    
    assert_response :redirect
  end

  test "should destroy block when authenticated and owner" do
    sign_in_as(@user1)
    
    assert_difference 'Block.count', -1 do
      delete block_path(@block)
    end
    
    assert_response :redirect
    assert_not Block.exists?(id: @block.id)
  end

  test "should not destroy block when not owner" do
    user3 = create(:user, :client, :approved)
    sign_in_as(user3)
    
    assert_no_difference 'Block.count' do
      delete block_path(@block)
    end
    
    assert_response :redirect
  end

  test "should handle non-existent block destroy gracefully" do
    sign_in_as(@user1)
    
    non_existent_id = @block.id
    @block.destroy
    
    assert_no_difference 'Block.count' do
      delete block_path(non_existent_id)
    end
    
    assert_response :redirect
  end

  test "should redirect back after successful block" do
    user3 = create(:user, :client, :approved)
    sign_in_as(@user1)
    
    post blocks_path, params: { blocked_id: user3.id }
    assert_response :redirect
  end

  test "should redirect back after successful unblock" do
    sign_in_as(@user1)
    
    delete block_path(@block)
    assert_response :redirect
  end
end
