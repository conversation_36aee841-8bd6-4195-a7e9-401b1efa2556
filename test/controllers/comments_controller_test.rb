require "test_helper"

class CommentsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @post = create(:post, artist_profile: @artist_profile)
    @client_user = create(:user, :client, :approved)
    @comment = create(:comment, post: @post, user: @client_user)
    @unapproved_user = create(:user, :client, approved: false)
    @other_user = create(:user, :client, :approved)
  end

  test "should get index without authentication" do
    get post_comments_path(@post)
    assert_response :success
  end

  test "should show comment without authentication" do
    get post_comment_path(@post, @comment)
    assert_response :success
  end

  test "should redirect to login when not authenticated for new" do
    get new_post_comment_path(@post)
    assert_redirected_to new_session_path
  end

  test "should redirect when user is not approved" do
    sign_in_as(@unapproved_user)
    get new_post_comment_path(@post)
    assert_redirected_to root_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should get new when user is approved" do
    sign_in_as(@client_user)
    get new_post_comment_path(@post)
    assert_response :success
    assert_select 'form'
  end

  test "should create comment with valid params" do
    sign_in_as(@client_user)
    
    assert_difference '@post.comments.count', 1 do
      post post_comments_path(@post), params: {
        comment: {
          content: "Great tattoo work!"
        }
      }
    end
    
    comment = @post.comments.last
    assert_equal @client_user, comment.user
    assert_equal "Great tattoo work!", comment.content
    assert_redirected_to @post
    assert_equal 'Comment was successfully created.', flash[:notice]
  end

  test "should not create comment with invalid params" do
    sign_in_as(@client_user)
    
    assert_no_difference '@post.comments.count' do
      post post_comments_path(@post), params: {
        comment: {
          content: ""  # Invalid - content is required
        }
      }
    end
    
    assert_redirected_to @post
    assert_equal 'Unable to create comment.', flash[:alert]
  end

  test "should get edit for own comment" do
    sign_in_as(@client_user)
    get edit_post_comment_path(@post, @comment)
    assert_response :success
    assert_select 'form'
  end

  test "should not get edit for other user's comment" do
    sign_in_as(@other_user)
    get edit_post_comment_path(@post, @comment)
    assert_redirected_to @post
    assert_equal 'Access denied.', flash[:alert]
  end

  test "admin should get edit for any comment" do
    admin_user = create(:user, :client, :approved, admin: true)
    sign_in_as(admin_user)
    get edit_post_comment_path(@post, @comment)
    assert_response :success
  end

  test "should update own comment" do
    sign_in_as(@client_user)
    
    patch post_comment_path(@post, @comment), params: {
      comment: {
        content: "Updated comment content"
      }
    }
    
    assert_redirected_to @post
    assert_equal 'Comment was successfully updated.', flash[:notice]
    
    @comment.reload
    assert_equal "Updated comment content", @comment.content
  end

  test "should not update comment with invalid params" do
    sign_in_as(@client_user)
    
    patch post_comment_path(@post, @comment), params: {
      comment: {
        content: ""  # Invalid
      }
    }
    
    assert_response :unprocessable_entity
  end

  test "should destroy own comment" do
    sign_in_as(@client_user)
    
    assert_difference '@post.comments.count', -1 do
      delete post_comment_path(@post, @comment)
    end
    
    assert_redirected_to @post
    assert_equal 'Comment was successfully deleted.', flash[:notice]
  end

  test "should not destroy other user's comment" do
    sign_in_as(@other_user)
    
    assert_no_difference '@post.comments.count' do
      delete post_comment_path(@post, @comment)
    end
    
    assert_redirected_to @post
    assert_equal 'Access denied.', flash[:alert]
  end

  test "admin should destroy any comment" do
    admin_user = create(:user, :client, :approved, admin: true)
    sign_in_as(admin_user)
    
    assert_difference '@post.comments.count', -1 do
      delete post_comment_path(@post, @comment)
    end
    
    assert_redirected_to @post
    assert_equal 'Comment was successfully deleted.', flash[:notice]
  end

  test "should handle turbo_stream requests for create" do
    sign_in_as(@client_user)
    
    post post_comments_path(@post), 
         params: { comment: { content: "Great work!" } },
         headers: { "Accept" => "text/vnd.turbo-stream.html" }
    
    assert_response :success
    assert_equal "text/vnd.turbo-stream.html", response.media_type
  end

  test "should handle turbo_stream requests for destroy" do
    sign_in_as(@client_user)
    
    delete post_comment_path(@post, @comment), 
           headers: { "Accept" => "text/vnd.turbo-stream.html" }
    
    assert_response :success
    assert_equal "text/vnd.turbo-stream.html", response.media_type
  end

  test "artists should be able to comment on posts" do
    sign_in_as(@artist_user)
    
    assert_difference '@post.comments.count', 1 do
      post post_comments_path(@post), params: {
        comment: { content: "Nice work!" }
      }
    end
  end
end