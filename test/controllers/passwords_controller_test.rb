require "test_helper"

class PasswordsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = create(:user, :client, :approved)
  end

  test "should get new" do
    get new_password_path
    assert_response :success
    assert_select "form"
    assert_select "input[name='email_address']"
    assert_select "input[type='submit']"
  end

  test "authenticated user can still access password reset" do
    sign_in_as(@user)
    get new_password_path
    assert_response :success
  end

  test "should send password reset email for existing user" do
    assert_enqueued_emails 1 do
      post passwords_path, params: { email_address: @user.email_address }
    end
    
    assert_redirected_to new_session_path
    assert_equal "Password reset instructions sent (if user with that email address exists).", flash[:notice]
  end

  test "should not reveal non-existent email addresses" do
    assert_no_enqueued_emails do
      post passwords_path, params: { email_address: "<EMAIL>" }
    end
    
    assert_redirected_to new_session_path
    assert_equal "Password reset instructions sent (if user with that email address exists).", flash[:notice]
  end

  test "should handle blank email address gracefully" do
    assert_no_enqueued_emails do
      post passwords_path, params: { email_address: "" }
    end
    
    assert_redirected_to new_session_path
    assert_equal "Password reset instructions sent (if user with that email address exists).", flash[:notice]
  end

  test "password reset email contains correct user" do
    perform_enqueued_jobs do
      post passwords_path, params: { email_address: @user.email_address }
    end
    
    # Verify the email was sent to the correct user
    assert_enqueued_email_with PasswordsMailer, :reset, args: [@user]
  end

  test "should get edit with valid token" do
    token = @user.signed_id(purpose: :password_reset, expires_in: 20.minutes)
    
    get edit_password_path(token)
    assert_response :success
    assert_select "form"
    assert_select "input[name='password']"
    assert_select "input[name='password_confirmation']"
  end

  test "should redirect with invalid token" do
    invalid_token = "invalid_token_string"
    
    get edit_password_path(invalid_token)
    assert_redirected_to new_password_path
    assert_equal "Password reset link is invalid or has expired.", flash[:alert]
  end

  test "should redirect with expired token" do
    # Create an expired token
    expired_token = @user.signed_id(purpose: :password_reset, expires_in: -1.minute)
    
    get edit_password_path(expired_token)
    assert_redirected_to new_password_path
    assert_equal "Password reset link is invalid or has expired.", flash[:alert]
  end

  test "should update password with valid token and matching passwords" do
    token = @user.signed_id(purpose: :password_reset, expires_in: 20.minutes)
    new_password = "newpassword123"
    
    patch password_path(token), params: { 
      password: new_password, 
      password_confirmation: new_password 
    }
    
    assert_redirected_to new_session_path
    assert_equal "Password has been reset.", flash[:notice]
    
    # Verify password was actually changed
    @user.reload
    assert @user.authenticate(new_password)
  end

  test "should not update password with mismatched confirmation" do
    token = @user.signed_id(purpose: :password_reset, expires_in: 20.minutes)
    
    patch password_path(token), params: { 
      password: "newpassword123", 
      password_confirmation: "differentpassword" 
    }
    
    assert_redirected_to edit_password_path(token)
    assert_equal "Passwords did not match.", flash[:alert]
    
    # Verify password was not changed
    @user.reload
    assert @user.authenticate("password123") # Original password should still work
  end

  test "should not update password with invalid token" do
    invalid_token = "invalid_token_string"
    
    patch password_path(invalid_token), params: { 
      password: "newpassword123", 
      password_confirmation: "newpassword123" 
    }
    
    assert_redirected_to new_password_path
    assert_equal "Password reset link is invalid or has expired.", flash[:alert]
  end

  test "should handle blank password gracefully" do
    token = @user.signed_id(purpose: :password_reset, expires_in: 20.minutes)
    
    patch password_path(token), params: { 
      password: "", 
      password_confirmation: "" 
    }
    
    assert_redirected_to edit_password_path(token)
    assert_equal "Passwords did not match.", flash[:alert]
  end

  test "password reset token is single use" do
    token = @user.signed_id(purpose: :password_reset, expires_in: 20.minutes)
    
    # First use - should work
    patch password_path(token), params: { 
      password: "newpassword123", 
      password_confirmation: "newpassword123" 
    }
    assert_redirected_to new_session_path
    
    # Second use - token should still be valid for the same user
    # (Rails signed_id tokens don't automatically expire after one use)
    get edit_password_path(token)
    assert_response :success
  end

  test "password reset works for different user types" do
    artist_user = create(:user, :artist, :approved)
    
    assert_enqueued_emails 1 do
      post passwords_path, params: { email_address: artist_user.email_address }
    end
    
    token = artist_user.signed_id(purpose: :password_reset, expires_in: 20.minutes)
    
    patch password_path(token), params: { 
      password: "artistnewpassword", 
      password_confirmation: "artistnewpassword" 
    }
    
    assert_redirected_to new_session_path
    assert_equal "Password has been reset.", flash[:notice]
  end

  test "case insensitive email lookup for password reset" do
    assert_enqueued_emails 1 do
      post passwords_path, params: { email_address: @user.email_address.upcase }
    end
    
    assert_redirected_to new_session_path
  end

  test "password update validates password requirements" do
    token = @user.signed_id(purpose: :password_reset, expires_in: 20.minutes)
    
    # Assuming there are password length requirements
    patch password_path(token), params: { 
      password: "123", # Too short
      password_confirmation: "123" 
    }
    
    # This might fail validation - depends on User model validations
    # If password validation fails, it should redirect back to edit
    if @user.class.validators_on(:password).any?
      assert_redirected_to edit_password_path(token)
    else
      assert_redirected_to new_session_path
    end
  end

  private

  def sign_in_as(user)
    post session_path, params: { email_address: user.email_address, password: "password123" }
  end
end