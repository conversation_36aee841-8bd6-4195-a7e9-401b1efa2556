require "test_helper"

class MessagesControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user1 = create(:user, :client, :approved)
    @user2 = create(:user, :artist, :approved)
    @conversation = create(:conversation, user1: @user1, user2: @user2)
    @message = create(:message, conversation: @conversation, sender: @user2)
  end

  test "should redirect to login when not authenticated for new" do
    get new_message_path(recipient_id: @user2.id)
    assert_response :redirect
  end

  test "should get new when authenticated with valid recipient" do
    sign_in_as(@user1)
    get new_message_path(recipient_id: @user2.id)
    assert_response :success
    assert assigns(:recipient)
    assert assigns(:message)
  end

  test "should redirect when trying to message blocked user" do
    create(:block, blocker: @user1, blocked: @user2)
    sign_in_as(@user1)
    get new_message_path(recipient_id: @user2.id)
    assert_response :redirect
    assert_redirected_to root_path
  end

  test "should redirect when trying to message user who blocked you" do
    create(:block, blocker: @user2, blocked: @user1)
    sign_in_as(@user1)
    get new_message_path(recipient_id: @user2.id)
    assert_response :redirect
    assert_redirected_to root_path
  end

  test "should redirect when trying to message artist with messages disabled" do
    @user2.artist_profile.update!(messages_enabled: false)
    sign_in_as(@user1)
    get new_message_path(recipient_id: @user2.id)
    assert_response :redirect
    assert_redirected_to root_path
  end

  test "should create new message and conversation" do
    user3 = create(:user, :client, :approved)
    sign_in_as(@user1)
    
    assert_difference ['Message.count', 'Conversation.count'], 1 do
      post messages_path, params: {
        message: {
          content: "Hello, new conversation!",
          recipient_id: user3.id
        }
      }
    end
    
    assert_response :redirect
    new_conversation = Conversation.between_users(@user1, user3)
    assert_redirected_to conversation_path(new_conversation)
  end

  test "should create message in existing conversation" do
    sign_in_as(@user1)
    
    assert_difference 'Message.count', 1 do
      assert_no_difference 'Conversation.count' do
        post messages_path, params: {
          message: {
            content: "Reply in existing conversation",
            recipient_id: @user2.id
          }
        }
      end
    end
    
    assert_response :redirect
    assert_redirected_to conversation_path(@conversation)
  end

  test "should create message via conversation" do
    sign_in_as(@user1)
    
    assert_difference 'Message.count', 1 do
      post conversation_messages_path(@conversation), params: {
        message: { content: "Message from conversation page" }
      }
    end
    
    assert_response :redirect
    assert_redirected_to conversation_path(@conversation)
  end

  test "should not create message with invalid content" do
    sign_in_as(@user1)
    
    assert_no_difference 'Message.count' do
      post conversation_messages_path(@conversation), params: {
        message: { content: "" }
      }
    end
    
    assert_response :unprocessable_entity
    assert assigns(:message).errors[:content].any?
  end

  test "should get show when participant" do
    sign_in_as(@user1)
    get message_path(@message)
    assert_response :success
    assert assigns(:message)
    assert assigns(:conversation)
    assert assigns(:other_user)
  end

  test "should redirect when not participant" do
    user3 = create(:user, :client, :approved)
    sign_in_as(user3)
    get message_path(@message)
    assert_response :redirect
    assert_redirected_to conversations_path
  end

  test "should mark message as read for recipient" do
    @message.update!(read: false)
    sign_in_as(@user1) # user1 is recipient
    
    patch mark_as_read_message_path(@message)
    assert_response :success
    
    @message.reload
    assert @message.read?
  end

  test "should not mark message as read for sender" do
    @message.update!(read: false)
    sign_in_as(@user2) # user2 is sender
    
    patch mark_as_read_message_path(@message)
    assert_response :forbidden
    
    @message.reload
    assert_not @message.read?
  end

  test "should handle message with image attachment" do
    sign_in_as(@user1)
    
    image = fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')
    
    assert_difference 'Message.count', 1 do
      post conversation_messages_path(@conversation), params: {
        message: { 
          content: "Message with image",
          image: image
        }
      }
    end
    
    message = Message.last
    assert message.image.attached?
    assert_response :redirect
  end
end
