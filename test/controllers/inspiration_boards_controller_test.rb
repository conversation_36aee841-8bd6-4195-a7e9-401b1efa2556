require "test_helper"

class InspirationBoardsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = create(:user, :approved)
    @other_user = create(:user, :approved)
    @board = create(:inspiration_board, user: @user)
    @public_board = create(:inspiration_board, :public, user: @other_user)
    @private_board = create(:inspiration_board, :private, user: @other_user)
  end

  test "should redirect to login when not authenticated" do
    get inspiration_boards_path
    assert_redirected_to new_session_path
  end

  test "should redirect unapproved users" do
    unapproved_user = create(:user, approved: false)
    sign_in_as(unapproved_user)
    get inspiration_boards_path
    assert_redirected_to root_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should get index when authenticated" do
    sign_in_as(@user)
    get inspiration_boards_path
    assert_response :success
    assert_select 'h1', 'My Inspiration Boards'
  end

  test "should show only user's own boards in index" do
    sign_in_as(@user)
    get inspiration_boards_path
    assert_response :success
    assert_select 'h3', text: @board.name
  end

  test "should get new board form" do
    sign_in_as(@user)
    get new_inspiration_board_path
    assert_response :success
    assert_select 'form'
    assert_select 'h1', 'Create Inspiration Board'
  end

  test "should create board with valid params" do
    sign_in_as(@user)
    
    assert_difference '@user.inspiration_boards.count', 1 do
      post inspiration_boards_path, params: {
        inspiration_board: {
          name: "Test Board",
          privacy: false
        }
      }
    end
    
    assert_redirected_to inspiration_board_path(InspirationBoard.last)
    assert_equal 'Inspiration board was successfully created.', flash[:notice]
    
    created_board = @user.inspiration_boards.last
    assert_equal "Test Board", created_board.name
    assert_not created_board.privacy
  end

  test "should not create board with invalid params" do
    sign_in_as(@user)
    
    assert_no_difference '@user.inspiration_boards.count' do
      post inspiration_boards_path, params: {
        inspiration_board: {
          name: "" # Invalid - blank name
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should show own board" do
    sign_in_as(@user)
    get inspiration_board_path(@board)
    assert_response :success
    assert_select 'h1', @board.name
  end

  test "should show public board from other user" do
    sign_in_as(@user)
    get inspiration_board_path(@public_board)
    assert_response :success
    assert_select 'h1', @public_board.name
  end

  test "should not show private board from other user" do
    sign_in_as(@user)
    get inspiration_board_path(@private_board)
    assert_redirected_to inspiration_boards_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should get edit for own board" do
    sign_in_as(@user)
    get edit_inspiration_board_path(@board)
    assert_response :success
    assert_select 'form'
    assert_select 'h1', 'Edit Inspiration Board'
  end

  test "should not get edit for other user's board" do
    sign_in_as(@user)
    get edit_inspiration_board_path(@private_board)
    assert_redirected_to inspiration_boards_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should update own board" do
    sign_in_as(@user)
    
    patch inspiration_board_path(@board), params: {
      inspiration_board: {
        name: "Updated Name",
        privacy: false
      }
    }
    
    assert_redirected_to inspiration_board_path(@board)
    assert_equal 'Inspiration board was successfully updated.', flash[:notice]
    
    @board.reload
    assert_equal "Updated Name", @board.name
    assert_not @board.privacy
  end

  test "should not update board with invalid params" do
    sign_in_as(@user)
    
    patch inspiration_board_path(@board), params: {
      inspiration_board: {
        name: "" # Invalid
      }
    }
    
    assert_response :unprocessable_entity
  end

  test "should destroy own board" do
    sign_in_as(@user)
    
    assert_difference '@user.inspiration_boards.count', -1 do
      delete inspiration_board_path(@board)
    end
    
    assert_redirected_to inspiration_boards_path
    assert_equal 'Inspiration board was successfully deleted.', flash[:notice]
  end

  test "should not destroy other user's board" do
    sign_in_as(@user)
    
    assert_no_difference 'InspirationBoard.count' do
      delete inspiration_board_path(@private_board)
    end
    
    assert_redirected_to inspiration_boards_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should browse public boards" do
    # Create some public boards with items
    public_board_with_items = create(:inspiration_board, :public, :with_items, user: @other_user)
    
    sign_in_as(@user)
    get browse_public_inspiration_boards_path
    assert_response :success
    assert_select 'h1', 'Public Inspiration Boards'
  end

  test "should not show empty boards in public browse" do
    # The controller should only show boards with items
    empty_public_board = create(:inspiration_board, :public, user: @other_user)
    
    sign_in_as(@user)
    get browse_public_inspiration_boards_path
    assert_response :success
    # Should not show empty boards
  end

  test "should handle duplicate board names for same user" do
    sign_in_as(@user)
    
    # Try to create board with same name
    assert_no_difference '@user.inspiration_boards.count' do
      post inspiration_boards_path, params: {
        inspiration_board: {
          name: @board.name # Duplicate name
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should allow duplicate board names for different users" do
    sign_in_as(@user)
    
    # Should allow same name as other user's board
    assert_difference '@user.inspiration_boards.count', 1 do
      post inspiration_boards_path, params: {
        inspiration_board: {
          name: @public_board.name # Same name as other user's board
        }
      }
    end
    
    assert_redirected_to inspiration_board_path(InspirationBoard.last)
  end
end