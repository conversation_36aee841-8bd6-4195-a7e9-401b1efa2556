require "test_helper"

class ArtistsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
  end

  test "should get show" do
    get artist_path(@artist_profile.slug)
    assert_response :success
  end

  test "should get index" do
    get artists_path
    assert_response :success
  end

  test "should get portfolio" do
    get portfolio_artist_path(@artist_profile.slug)
    assert_response :success
  end

  test "should get posts" do
    get posts_artist_path(@artist_profile.slug)
    assert_response :success
  end

  test "should get tags" do
    get tags_artist_path(@artist_profile.slug)
    assert_response :success
    assert_select "h2", text: "Tagged Tattoos"
  end

  test "should display tagged tattoos on tags page" do
    client_profile = create(:client_profile)
    tattoo_item = create(:tattoo_item, client_profile: client_profile, artist_profile: @artist_profile)

    get tags_artist_path(@artist_profile.slug)
    assert_response :success
    assert_select ".grid .bg-white", count: 1
  end

  test "should show empty state when no tagged tattoos" do
    get tags_artist_path(@artist_profile.slug)
    assert_response :success
    assert_select "h3", text: "No tagged tattoos yet"
  end

  # Waitlist tests
  test "should get waitlist for artist owner" do
    sign_in_as(@artist_user)
    
    get waitlist_artist_path(@artist_profile)
    assert_response :success
    assert_select "h2", text: "Waitlist"
  end

  test "should not get waitlist for non-owner" do
    other_user = create(:user, :client, :approved)
    sign_in_as(other_user)
    
    get waitlist_artist_path(@artist_profile)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
  end

  test "should not get waitlist without authentication" do
    get waitlist_artist_path(@artist_profile)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
  end

  test "should get edit for artist owner" do
    sign_in_as(@artist_user)
    
    get edit_artist_path(@artist_profile)
    assert_response :success
    assert_select "input[name='artist_profile[waitlist_enabled]']"
  end

  test "should update artist profile with waitlist_enabled" do
    sign_in_as(@artist_user)
    
    patch artist_path(@artist_profile), params: {
      artist_profile: {
        name: @artist_profile.name,
        waitlist_enabled: true
      }
    }
    
    assert_redirected_to artist_path(@artist_profile)
    @artist_profile.reload
    assert @artist_profile.waitlist_enabled?
  end

  test "should show Join Waitlist button when waitlist enabled" do
    @artist_profile.update!(waitlist_enabled: true)
    
    get artist_path(@artist_profile)
    assert_response :success
    assert_select "a[href='#{new_artist_waitlist_entry_path(@artist_profile)}']", text: "Join Waitlist"
  end

  test "should not show Join Waitlist button when waitlist disabled" do
    @artist_profile.update!(waitlist_enabled: false)
    
    get artist_path(@artist_profile)
    assert_response :success
    assert_select "a[href='#{new_artist_waitlist_entry_path(@artist_profile)}']", count: 0
  end

  test "should show waitlist tab only to artist owner when enabled" do
    @artist_profile.update!(waitlist_enabled: true)
    
    # Artist owner should see the tab
    sign_in_as(@artist_user)
    get artist_path(@artist_profile)
    assert_select "a[href='#{waitlist_artist_path(@artist_profile)}']", text: /Waitlist/
    
    # Other users should not see the tab
    sign_out
    other_user = create(:user, :client, :approved)
    sign_in_as(other_user)
    get artist_path(@artist_profile)
    assert_select "a[href='#{waitlist_artist_path(@artist_profile)}']", count: 0
  end

  test "should not show waitlist tab when disabled" do
    @artist_profile.update!(waitlist_enabled: false)
    sign_in_as(@artist_user)
    
    get artist_path(@artist_profile)
    assert_select "a[href='#{waitlist_artist_path(@artist_profile)}']", count: 0
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end

  def sign_out
    delete session_url
  end
end
