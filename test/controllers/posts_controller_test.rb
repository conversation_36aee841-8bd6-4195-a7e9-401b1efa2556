require "test_helper"

class PostsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @post = create(:post, artist_profile: @artist_profile)
    @other_artist = create(:user, :artist, :approved)
    @client_user = create(:user, :client, :approved)
  end

  test "should get index without authentication" do
    get posts_path
    assert_response :success
  end

  test "should show published post without authentication" do
    get post_path(@post)
    assert_response :success
  end

  test "should not show unpublished post" do
    unpublished_post = create(:post, artist_profile: @artist_profile)
    unpublished_post.update_column(:published_at, nil)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      get post_path(unpublished_post)
    end
  end

  test "should redirect to root when not authenticated for new" do
    get new_post_path
    assert_redirected_to new_session_path
  end

  test "should redirect when user is not an artist" do
    sign_in_as(@client_user)
    get new_post_path
    assert_redirected_to root_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should redirect when artist is not approved" do
    unapproved_artist = create(:user, :artist, approved: false)
    sign_in_as(unapproved_artist)
    get new_post_path
    assert_redirected_to root_path
    assert_equal 'Access denied.', flash[:alert]
  end

  test "should get new when artist is approved" do
    sign_in_as(@artist_user)
    get new_post_path
    assert_response :success
    assert_select 'form'
  end

  test "should create post with valid params" do
    sign_in_as(@artist_user)
    
    assert_difference '@artist_profile.posts.count', 1 do
      post posts_path, params: {
        post: {
          caption: "Test caption",
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')
        }
      }
    end
    
    assert_redirected_to posts_path
    assert_equal 'Post was successfully created.', flash[:notice]
    
    created_post = @artist_profile.posts.last
    assert created_post.image.attached?
    assert_equal "Test caption", created_post.caption
    assert_not created_post.in_portfolio?
  end

  test "should create post in portfolio" do
    sign_in_as(@artist_user)
    
    assert_difference '@artist_profile.posts.count', 1 do
      post posts_path, params: {
        post: {
          caption: "Portfolio piece",
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
          in_portfolio: "1"
        }
      }
    end
    
    created_post = @artist_profile.posts.last
    assert created_post.in_portfolio?
  end

  test "should create post without caption" do
    sign_in_as(@artist_user)
    
    assert_difference '@artist_profile.posts.count', 1 do
      post posts_path, params: {
        post: {
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')
        }
      }
    end
    
    created_post = @artist_profile.posts.last
    assert created_post.image.attached?
    assert_nil created_post.caption
  end

  test "should not create post with invalid params" do
    sign_in_as(@artist_user)
    
    # Post without image
    assert_no_difference '@artist_profile.posts.count' do
      post posts_path, params: {
        post: {
          caption: "Caption but no image"
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should get edit for own post" do
    sign_in_as(@artist_user)
    get edit_post_path(@post)
    assert_response :success
    assert_select 'form'
  end

  test "should not get edit for other artist's post" do
    sign_in_as(@other_artist)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      get edit_post_path(@post)
    end
  end

  test "should update own post" do
    sign_in_as(@artist_user)
    
    patch post_path(@post), params: {
      post: {
        caption: "Updated caption"
      }
    }
    
    assert_redirected_to posts_path
    assert_equal 'Post was successfully updated.', flash[:notice]
    
    @post.reload
    assert_equal "Updated caption", @post.caption
  end

  test "should not update post with invalid params" do
    sign_in_as(@artist_user)
    
    # Try to make image post invalid by removing images
    patch post_path(@post), params: {
      post: {
        post_type: "image",
        images: []  # Invalid for image posts
      }
    }
    
    assert_response :unprocessable_entity
  end

  test "should destroy own post" do
    sign_in_as(@artist_user)
    
    assert_difference '@artist_profile.posts.count', -1 do
      delete post_path(@post)
    end
    
    assert_redirected_to posts_path
    assert_equal 'Post was successfully deleted.', flash[:notice]
  end

  test "should not destroy other artist's post" do
    sign_in_as(@other_artist)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      delete post_path(@post)
    end
  end

  test "should display posts in recent order on index" do
    old_post = create(:post, artist_profile: @artist_profile, published_at: 2.days.ago)
    new_post = create(:post, artist_profile: @artist_profile, published_at: 1.hour.ago)
    
    get posts_path
    assert_response :success
    
    # Check that posts are in correct order (newest first)
    assert_select '.post', count: 3  # Including the setup @post
  end

  # Text Post Tests
  test "should create text post with valid params" do
    sign_in_as(@artist_user)
    
    assert_difference '@artist_profile.posts.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "text",
          body: "This is a text post content"
        }
      }
    end
    
    assert_redirected_to posts_path
    assert_equal 'Post was successfully created.', flash[:notice]
    
    created_post = @artist_profile.posts.last
    assert created_post.text?
    assert_equal "This is a text post content", created_post.body
    assert_not created_post.image.attached?
    assert_nil created_post.caption
  end

  test "should not create text post without body" do
    sign_in_as(@artist_user)
    
    assert_no_difference '@artist_profile.posts.count' do
      post posts_path, params: {
        post: {
          post_type: "text"
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should not create text post with image" do
    sign_in_as(@artist_user)
    
    assert_no_difference '@artist_profile.posts.count' do
      post posts_path, params: {
        post: {
          post_type: "text",
          body: "Text content",
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should not create image post with body" do
    sign_in_as(@artist_user)
    
    assert_no_difference '@artist_profile.posts.count' do
      post posts_path, params: {
        post: {
          post_type: "image",
          body: "Should not be allowed",
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')
        }
      }
    end
    
    assert_response :unprocessable_entity
  end
end