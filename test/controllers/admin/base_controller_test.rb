require "test_helper"

class Admin::BaseControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @regular_user = create(:user, :client, :approved, admin: false)
  end

  test "admin user can access admin area" do
    sign_in_as(@admin_user)
    get admin_root_path
    assert_response :success
  end

  test "non-admin user is redirected from admin area" do
    sign_in_as(@regular_user)
    get admin_root_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
  end

  test "unauthenticated user is redirected from admin area" do
    get admin_root_path
    assert_redirected_to new_session_path
  end

  test "nav_link_class helper returns correct classes for active path" do
    sign_in_as(@admin_user)
    get admin_dashboard_path
    assert_response :success
    
    # Test that the controller provides the helper method
    controller = Admin::DashboardController.new
    controller.request = ActionDispatch::Request.new("PATH_INFO" => admin_dashboard_path)
    
    active_class = controller.send(:nav_link_class, admin_dashboard_path)
    inactive_class = controller.send(:nav_link_class, admin_users_path)
    
    assert_includes active_class, "bg-indigo-700 text-white"
    assert_not_includes inactive_class, "bg-indigo-700 text-white"
  end

  test "admin layout is used for admin controllers" do
    sign_in_as(@admin_user)
    get admin_root_path
    assert_select "html" # Basic test that admin layout renders
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end