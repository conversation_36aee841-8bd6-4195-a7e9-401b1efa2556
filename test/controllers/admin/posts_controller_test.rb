require "test_helper"

class Admin::PostsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @regular_user = create(:user, :client, :approved, admin: false)
    @artist_user = create(:user, :artist, :approved)
    @post = create(:post, artist_profile: @artist_user.artist_profile)
    @hidden_post = create(:post, artist_profile: @artist_user.artist_profile, hidden: true)
    @portfolio_post = create(:post, artist_profile: @artist_user.artist_profile, in_portfolio: true)
  end

  test "admin can access posts index" do
    sign_in_as(@admin_user)
    get admin_posts_path
    assert_response :success
    assert_select "h1", text: /Posts/i
  end

  test "non-admin cannot access posts index" do
    sign_in_as(@regular_user)
    get admin_posts_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
  end

  test "posts index displays all posts by default" do
    sign_in_as(@admin_user)
    get admin_posts_path
    assert_response :success
    
    posts = assigns(:posts)
    assert_includes posts, @post
    assert_includes posts, @hidden_post
    assert_includes posts, @portfolio_post
  end

  test "posts index filters by visibility status" do
    sign_in_as(@admin_user)
    
    # Test hidden filter
    get admin_posts_path, params: { status: 'hidden' }
    assert_response :success
    
    posts = assigns(:posts)
    assert_includes posts, @hidden_post
    assert_not_includes posts, @post
    
    # Test visible filter
    get admin_posts_path, params: { status: 'visible' }
    assert_response :success
    
    posts = assigns(:posts)
    assert_includes posts, @post
    assert_not_includes posts, @hidden_post
  end

  test "posts index filters by portfolio status" do
    sign_in_as(@admin_user)
    
    # Test portfolio filter
    get admin_posts_path, params: { portfolio: 'true' }
    assert_response :success
    
    posts = assigns(:posts)
    assert_includes posts, @portfolio_post
    assert_not_includes posts, @post
    
    # Test non-portfolio filter
    get admin_posts_path, params: { portfolio: 'false' }
    assert_response :success
    
    posts = assigns(:posts)
    assert_includes posts, @post
    assert_not_includes posts, @portfolio_post
  end

  test "posts index searches by caption and artist name" do
    @post.update!(caption: "Amazing dragon tattoo")
    
    sign_in_as(@admin_user)
    get admin_posts_path, params: { search: "dragon" }
    assert_response :success
    
    posts = assigns(:posts)
    assert_includes posts, @post
    
    # Search by artist username
    get admin_posts_path, params: { search: @artist_user.username }
    assert_response :success
    
    posts = assigns(:posts)
    assert_includes posts, @post
    assert_includes posts, @hidden_post
    assert_includes posts, @portfolio_post
  end

  test "admin can view post details" do
    sign_in_as(@admin_user)
    get admin_post_path(@post)
    assert_response :success
  end

  test "admin can hide post" do
    sign_in_as(@admin_user)
    assert_not @post.hidden?
    
    patch hide_admin_post_path(@post)
    assert_redirected_to admin_posts_path
    assert_equal "Post has been hidden.", flash[:notice]
    
    @post.reload
    assert @post.hidden?
  end

  test "admin can unhide post" do
    sign_in_as(@admin_user)
    assert @hidden_post.hidden?
    
    patch unhide_admin_post_path(@hidden_post)
    assert_redirected_to admin_posts_path
    assert_equal "Post has been made visible.", flash[:notice]
    
    @hidden_post.reload
    assert_not @hidden_post.hidden?
  end

  test "admin can delete post" do
    sign_in_as(@admin_user)
    post_id = @post.id
    
    assert_difference 'Post.count', -1 do
      delete admin_post_path(@post)
    end
    
    assert_redirected_to admin_posts_path
    assert_equal "Post was successfully deleted.", flash[:notice]
    assert_raises(ActiveRecord::RecordNotFound) { Post.find(post_id) }
  end

  test "posts index displays correct counts" do
    sign_in_as(@admin_user)
    get admin_posts_path
    assert_response :success
    
    assert_equal Post.where(hidden: true).count, assigns(:hidden_count)
    assert_equal Post.count, assigns(:total_count)
  end

  test "posts are ordered by creation date descending" do
    sign_in_as(@admin_user)
    older_post = create(:post, artist_profile: @artist_user.artist_profile, created_at: 1.week.ago)
    newer_post = create(:post, artist_profile: @artist_user.artist_profile, created_at: 1.day.ago)
    
    get admin_posts_path
    assert_response :success
    
    posts = assigns(:posts).to_a
    newer_index = posts.index(newer_post)
    older_index = posts.index(older_post)
    
    assert newer_index < older_index, "Newer posts should appear before older posts"
  end

  test "posts include necessary associations to avoid N+1 queries" do
    sign_in_as(@admin_user)
    
    # This test verifies that the controller includes the right associations
    # to prevent N+1 query problems when displaying posts with their data
    assert_queries_count = lambda do |&block|
      count = 0
      ActiveSupport::Notifications.subscribe("sql.active_record") { count += 1 }
      block.call
      count
    end
    
    get admin_posts_path
    assert_response :success
    
    # The controller should include :artist_profile, :likes, :comments, image_attachment: :blob
    posts = assigns(:posts)
    assert posts.first.association(:artist_profile).loaded?
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end