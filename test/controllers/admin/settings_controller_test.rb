require "test_helper"

class Admin::SettingsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @regular_user = create(:user, :client, :approved, admin: false)
    @site_setting = SiteSetting.current
  end

  test "admin can access settings" do
    sign_in_as(@admin_user)
    get admin_settings_path
    assert_response :success
    assert_select "h1", text: /Settings/i
  end

  test "non-admin cannot access settings" do
    sign_in_as(@regular_user)
    get admin_settings_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
  end

  test "settings show displays current settings" do
    sign_in_as(@admin_user)
    get admin_settings_path
    assert_response :success
    
    settings = assigns(:settings)
    assert_equal @site_setting, settings
    assert_equal SiteSetting.current, settings
  end

  test "admin can access settings edit page" do
    sign_in_as(@admin_user)
    get edit_admin_settings_path
    assert_response :success
    
    # Verify form fields are present
    assert_select "form"
    assert_select "input[name='site_setting[site_name]']"
    assert_select "input[name='site_setting[contact_email]']"
    assert_select "textarea[name='site_setting[site_description]']"
  end

  test "admin can update site settings" do
    sign_in_as(@admin_user)
    
    patch admin_settings_path, params: {
      site_setting: {
        site_name: "Updated Tattoo Marketplace",
        contact_email: "<EMAIL>",
        site_description: "Updated description",
        require_approval: true,
        allow_public_registration: false,
        enable_comments: true,
        enable_messaging: true,
        enable_following: true
      }
    }
    
    assert_redirected_to admin_settings_path
    assert_equal "Settings were successfully updated.", flash[:notice]
    
    @site_setting.reload
    assert_equal "Updated Tattoo Marketplace", @site_setting.site_name
    assert_equal "<EMAIL>", @site_setting.contact_email
    assert_equal "Updated description", @site_setting.site_description
    assert @site_setting.require_approval?
    assert_not @site_setting.allow_public_registration?
    assert @site_setting.enable_comments?
  end

  test "admin cannot update settings with invalid data" do
    sign_in_as(@admin_user)
    
    # Assuming contact_email has validation
    patch admin_settings_path, params: {
      site_setting: {
        contact_email: "invalid_email"
      }
    }
    
    assert_response :unprocessable_entity
    assert_template :show
  end

  test "boolean settings are handled correctly" do
    sign_in_as(@admin_user)
    
    # Test enabling all boolean settings
    patch admin_settings_path, params: {
      site_setting: {
        require_approval: "1",
        allow_public_registration: "1",
        auto_approve_posts: "1",
        enable_comments: "1",
        enable_messaging: "1",
        enable_following: "1",
        enable_inspiration_boards: "1",
        enable_public_profiles: "1"
      }
    }
    
    assert_redirected_to admin_settings_path
    
    @site_setting.reload
    assert @site_setting.require_approval?
    assert @site_setting.allow_public_registration?
    assert @site_setting.auto_approve_posts?
    assert @site_setting.enable_comments?
    assert @site_setting.enable_messaging?
    assert @site_setting.enable_following?
    assert @site_setting.enable_inspiration_boards?
    assert @site_setting.enable_public_profiles?
  end

  test "boolean settings can be disabled" do
    sign_in_as(@admin_user)
    
    # First enable all settings
    @site_setting.update!(
      require_approval: true,
      allow_public_registration: true,
      enable_comments: true
    )
    
    # Then disable them (Rails checkboxes send "0" when unchecked)
    patch admin_settings_path, params: {
      site_setting: {
        require_approval: "0",
        allow_public_registration: "0",
        enable_comments: "0"
      }
    }
    
    assert_redirected_to admin_settings_path
    
    @site_setting.reload
    assert_not @site_setting.require_approval?
    assert_not @site_setting.allow_public_registration?
    assert_not @site_setting.enable_comments?
  end

  test "settings form includes all expected fields" do
    sign_in_as(@admin_user)
    get edit_admin_settings_path
    assert_response :success
    
    # Text fields
    assert_select "input[name='site_setting[site_name]']"
    assert_select "input[name='site_setting[contact_email]']"
    assert_select "textarea[name='site_setting[site_description]']"
    
    # Boolean checkboxes
    assert_select "input[type='checkbox'][name='site_setting[require_approval]']"
    assert_select "input[type='checkbox'][name='site_setting[allow_public_registration]']"
    assert_select "input[type='checkbox'][name='site_setting[auto_approve_posts]']"
    assert_select "input[type='checkbox'][name='site_setting[enable_comments]']"
    assert_select "input[type='checkbox'][name='site_setting[enable_messaging]']"
    assert_select "input[type='checkbox'][name='site_setting[enable_following]']"
    assert_select "input[type='checkbox'][name='site_setting[enable_inspiration_boards]']"
    assert_select "input[type='checkbox'][name='site_setting[enable_public_profiles]']"
  end

  test "settings persistence across requests" do
    sign_in_as(@admin_user)
    
    # Update settings
    patch admin_settings_path, params: {
      site_setting: {
        site_name: "Persistent Test Site",
        enable_messaging: true
      }
    }
    
    # Verify changes persist in database
    updated_settings = SiteSetting.current
    assert_equal "Persistent Test Site", updated_settings.site_name
    assert updated_settings.enable_messaging?
    
    # Verify changes appear in subsequent requests
    get admin_settings_path
    assert_response :success
    settings = assigns(:settings)
    assert_equal "Persistent Test Site", settings.site_name
    assert settings.enable_messaging?
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end