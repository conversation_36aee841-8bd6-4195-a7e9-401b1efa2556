require "test_helper"

class Admin::SpecialtiesControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @regular_user = create(:user, :client, :approved, admin: false)
    @specialty = create(:specialty, title: "Traditional", description: "Traditional tattoo style")
    @artist_user = create(:user, :artist, :approved)
  end

  test "admin can access specialties index" do
    sign_in_as(@admin_user)
    get admin_specialties_path
    assert_response :success
    assert_select "h1", text: /Specialties/i
  end

  test "non-admin cannot access specialties index" do
    sign_in_as(@regular_user)
    get admin_specialties_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
  end

  test "specialties index displays all specialties" do
    sign_in_as(@admin_user)
    get admin_specialties_path
    assert_response :success
    
    specialties = assigns(:specialties)
    assert_includes specialties, @specialty
  end

  test "specialties index searches by title" do
    create(:specialty, title: "Realistic", description: "Realistic tattoo style")
    
    sign_in_as(@admin_user)
    get admin_specialties_path, params: { search: "Traditional" }
    assert_response :success
    
    specialties = assigns(:specialties)
    assert_includes specialties, @specialty
    assert_equal 1, specialties.count
  end

  test "specialties are ordered by title" do
    zebra_specialty = create(:specialty, title: "Zebra Style")
    alpha_specialty = create(:specialty, title: "Abstract")
    
    sign_in_as(@admin_user)
    get admin_specialties_path
    assert_response :success
    
    specialties = assigns(:specialties).to_a
    alpha_index = specialties.index(alpha_specialty)
    traditional_index = specialties.index(@specialty)
    zebra_index = specialties.index(zebra_specialty)
    
    assert alpha_index < traditional_index
    assert traditional_index < zebra_index
  end

  test "admin can view specialty details" do
    sign_in_as(@admin_user)
    get admin_specialty_path(@specialty)
    assert_response :success
  end

  test "admin can access new specialty form" do
    sign_in_as(@admin_user)
    get new_admin_specialty_path
    assert_response :success
    
    assert_select "form"
    assert_select "input[name='specialty[title]']"
    assert_select "textarea[name='specialty[description]']"
  end

  test "admin can create specialty" do
    sign_in_as(@admin_user)
    
    assert_difference 'Specialty.count', 1 do
      post admin_specialties_path, params: {
        specialty: {
          title: "Neo-Traditional",
          description: "Modern take on traditional tattooing"
        }
      }
    end
    
    assert_redirected_to admin_specialties_path
    assert_equal "Specialty was successfully created.", flash[:notice]
    
    specialty = Specialty.last
    assert_equal "Neo-Traditional", specialty.title
    assert_equal "Modern take on traditional tattooing", specialty.description
  end

  test "admin cannot create specialty with invalid data" do
    sign_in_as(@admin_user)
    
    assert_no_difference 'Specialty.count' do
      post admin_specialties_path, params: {
        specialty: {
          title: "", # Invalid - likely required
          description: "Some description"
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_template :new
  end

  test "admin can edit specialty" do
    sign_in_as(@admin_user)
    get edit_admin_specialty_path(@specialty)
    assert_response :success
    
    assert_select "form"
    assert_select "input[name='specialty[title]'][value='Traditional']"
  end

  test "admin can update specialty" do
    sign_in_as(@admin_user)
    
    patch admin_specialty_path(@specialty), params: {
      specialty: {
        title: "Updated Traditional",
        description: "Updated description"
      }
    }
    
    assert_redirected_to admin_specialties_path
    assert_equal "Specialty was successfully updated.", flash[:notice]
    
    @specialty.reload
    assert_equal "Updated Traditional", @specialty.title
    assert_equal "Updated description", @specialty.description
  end

  test "admin cannot update specialty with invalid data" do
    sign_in_as(@admin_user)
    
    patch admin_specialty_path(@specialty), params: {
      specialty: {
        title: "" # Invalid
      }
    }
    
    assert_response :unprocessable_entity
    assert_template :edit
    
    @specialty.reload
    assert_equal "Traditional", @specialty.title # Should not have changed
  end

  test "admin can delete specialty without artist associations" do
    unassigned_specialty = create(:specialty, title: "Unused Style")
    
    sign_in_as(@admin_user)
    specialty_id = unassigned_specialty.id
    
    assert_difference 'Specialty.count', -1 do
      delete admin_specialty_path(unassigned_specialty)
    end
    
    assert_redirected_to admin_specialties_path
    assert_equal "Specialty was successfully deleted.", flash[:notice]
    assert_raises(ActiveRecord::RecordNotFound) { Specialty.find(specialty_id) }
  end

  test "admin cannot delete specialty with artist associations" do
    # Create artist specialty association
    ArtistSpecialty.create!(artist_profile: @artist_user.artist_profile, specialty: @specialty)
    
    sign_in_as(@admin_user)
    
    assert_no_difference 'Specialty.count' do
      delete admin_specialty_path(@specialty)
    end
    
    assert_redirected_to admin_specialties_path
    assert_equal "Cannot delete specialty that is assigned to artists.", flash[:alert]
  end

  test "specialties index includes artist associations for efficiency" do
    sign_in_as(@admin_user)
    get admin_specialties_path
    assert_response :success
    
    # Verify that artist_profiles association is included to prevent N+1 queries
    specialties = assigns(:specialties)
    assert specialties.first.association(:artist_profiles).loaded?
  end

  test "search is case insensitive" do
    sign_in_as(@admin_user)
    get admin_specialties_path, params: { search: "TRADITIONAL" }
    assert_response :success
    
    specialties = assigns(:specialties)
    assert_includes specialties, @specialty
  end

  test "partial search matches work" do
    sign_in_as(@admin_user)
    get admin_specialties_path, params: { search: "Trad" }
    assert_response :success
    
    specialties = assigns(:specialties)
    assert_includes specialties, @specialty
  end

  test "empty search returns all specialties" do
    other_specialty = create(:specialty, title: "Watercolor")
    
    sign_in_as(@admin_user)
    get admin_specialties_path, params: { search: "" }
    assert_response :success
    
    specialties = assigns(:specialties)
    assert_includes specialties, @specialty
    assert_includes specialties, other_specialty
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end