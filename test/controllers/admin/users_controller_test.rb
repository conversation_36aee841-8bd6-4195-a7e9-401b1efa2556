require "test_helper"

class Admin::UsersControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @regular_user = create(:user, :client, :approved, admin: false)
    @artist_user = create(:user, :artist, :approved)
    @pending_user = create(:user, :artist, approved: false)
    @another_admin = create(:user, :client, :approved, admin: true)
  end

  test "admin can access users index" do
    sign_in_as(@admin_user)
    get admin_users_path
    assert_response :success
    assert_select "h1", text: /Users/<USER>
  end

  test "non-admin cannot access users index" do
    sign_in_as(@regular_user)
    get admin_users_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
  end

  test "users index displays all users by default" do
    sign_in_as(@admin_user)
    get admin_users_path
    assert_response :success
    
    users = assigns(:users)
    assert_includes users, @admin_user
    assert_includes users, @regular_user
    assert_includes users, @artist_user
    assert_includes users, @pending_user
  end

  test "users index filters by role" do
    sign_in_as(@admin_user)
    get admin_users_path, params: { role: 'artist' }
    assert_response :success
    
    users = assigns(:users)
    assert_includes users, @artist_user
    assert_includes users, @pending_user
    assert_not_includes users, @regular_user
  end

  test "users index filters by status" do
    sign_in_as(@admin_user)
    get admin_users_path, params: { status: 'pending' }
    assert_response :success
    
    users = assigns(:users)
    assert_includes users, @pending_user
    assert_not_includes users, @artist_user
  end

  test "users index searches by email and username" do
    sign_in_as(@admin_user)
    get admin_users_path, params: { search: @artist_user.username }
    assert_response :success
    
    users = assigns(:users)
    assert_includes users, @artist_user
    assert_not_includes users, @regular_user
  end

  test "admin can view user details" do
    sign_in_as(@admin_user)
    get admin_user_path(@artist_user)
    assert_response :success
  end

  test "admin can edit user" do
    sign_in_as(@admin_user)
    get edit_admin_user_path(@artist_user)
    assert_response :success
  end

  test "admin can update user" do
    sign_in_as(@admin_user)
    patch admin_user_path(@artist_user), params: {
      user: { username: "updated_username", approved: true }
    }
    assert_redirected_to admin_user_path(@artist_user)
    assert_equal "User was successfully updated.", flash[:notice]
    
    @artist_user.reload
    assert_equal "updated_username", @artist_user.username
    assert @artist_user.approved?
  end

  test "admin cannot update user with invalid data" do
    sign_in_as(@admin_user)
    patch admin_user_path(@artist_user), params: {
      user: { username: "", email_address: "invalid_email" }
    }
    assert_response :unprocessable_entity
  end

  test "admin can approve user" do
    sign_in_as(@admin_user)
    assert_not @pending_user.approved?
    
    patch approve_admin_user_path(@pending_user)
    assert_redirected_to admin_users_path
    assert_equal "#{@pending_user.username} has been approved.", flash[:notice]
    
    @pending_user.reload
    assert @pending_user.approved?
  end

  test "admin can reject user" do
    sign_in_as(@admin_user)
    @artist_user.update!(approved: true)
    
    patch reject_admin_user_path(@artist_user)
    assert_redirected_to admin_users_path
    assert_equal "#{@artist_user.username} has been rejected.", flash[:notice]
    
    @artist_user.reload
    assert_not @artist_user.approved?
  end

  test "admin can delete non-admin user" do
    sign_in_as(@admin_user)
    user_id = @regular_user.id
    
    assert_difference 'User.count', -1 do
      delete admin_user_path(@regular_user)
    end
    
    assert_redirected_to admin_users_path
    assert_equal "User was successfully deleted.", flash[:notice]
    assert_raises(ActiveRecord::RecordNotFound) { User.find(user_id) }
  end

  test "admin cannot delete admin user" do
    sign_in_as(@admin_user)
    
    assert_no_difference 'User.count' do
      delete admin_user_path(@another_admin)
    end
    
    assert_redirected_to admin_users_path
    assert_equal "Cannot delete admin users.", flash[:alert]
  end

  test "approval sends notification email" do
    sign_in_as(@admin_user)
    
    assert_enqueued_emails 1 do
      patch approve_admin_user_path(@pending_user)
    end
  end

  test "approving already approved user does not send email" do
    sign_in_as(@admin_user)
    @artist_user.update!(approved: true)
    
    assert_no_enqueued_emails do
      patch approve_admin_user_path(@artist_user)
    end
  end

  test "users index displays correct counts" do
    sign_in_as(@admin_user)
    get admin_users_path
    assert_response :success
    
    assert_equal User.where(approved: false).count, assigns(:pending_count)
    assert_equal User.count, assigns(:total_count)
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end