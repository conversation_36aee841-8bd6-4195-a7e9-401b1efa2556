require "test_helper"

class Admin::DashboardControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @regular_user = create(:user, :client, :approved, admin: false)
    
    # Create test data for stats
    @artist_user = create(:user, :artist, :approved)
    @pending_user = create(:user, :artist, approved: false)
    @post = create(:post, artist_profile: @artist_user.artist_profile)
    @conversation = create(:conversation, user1: @admin_user, user2: @artist_user)
    @message = create(:message, conversation: @conversation, sender: @admin_user)
  end

  test "admin can access dashboard" do
    sign_in_as(@admin_user)
    get admin_dashboard_path
    assert_response :success
  end

  test "non-admin cannot access dashboard" do
    sign_in_as(@regular_user)
    get admin_dashboard_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
  end

  test "dashboard displays correct statistics" do
    sign_in_as(@admin_user)
    get admin_dashboard_path
    assert_response :success
    
    # Check that stats are loaded and displayed
    assert_select "h1", text: /Dashboard/i
    
    # Verify that instance variables are set with correct data
    assert assigns(:stats)
    assert assigns(:recent_users)
    assert assigns(:pending_approvals)
    assert assigns(:recent_posts)
    
    stats = assigns(:stats)
    assert_equal User.count, stats[:total_users]
    assert_equal User.where(approved: false).count, stats[:pending_users]
    assert_equal User.where(role: 'artist').count, stats[:artists]
    assert_equal User.where(role: 'client').count, stats[:clients]
    assert_equal Post.count, stats[:total_posts]
    assert_equal Conversation.count, stats[:total_conversations]
    assert_equal Message.count, stats[:total_messages]
  end

  test "dashboard shows recent users" do
    sign_in_as(@admin_user)
    get admin_dashboard_path
    assert_response :success
    
    recent_users = assigns(:recent_users)
    assert_equal 5, recent_users.limit_value
    assert_equal User.order(created_at: :desc).limit(5).to_a, recent_users.to_a
  end

  test "dashboard shows pending approvals" do
    sign_in_as(@admin_user)
    get admin_dashboard_path
    assert_response :success
    
    pending_approvals = assigns(:pending_approvals)
    assert_equal 10, pending_approvals.limit_value
    assert_includes pending_approvals, @pending_user
    assert_not_includes pending_approvals, @artist_user
  end

  test "dashboard shows recent posts" do
    sign_in_as(@admin_user)
    get admin_dashboard_path
    assert_response :success
    
    recent_posts = assigns(:recent_posts)
    assert_equal 5, recent_posts.limit_value
    assert_includes recent_posts, @post
  end

  test "dashboard root redirects to index" do
    sign_in_as(@admin_user)
    get admin_root_path
    assert_response :success
    # Should render the same as dashboard index
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end