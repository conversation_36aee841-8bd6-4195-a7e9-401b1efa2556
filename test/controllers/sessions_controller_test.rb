require "test_helper"

class SessionsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = create(:user, :client, :approved)
    @artist_user = create(:user, :artist, :approved)
    @unapproved_user = create(:user, :client, approved: false)
  end

  test "should get new" do
    get new_session_path
    assert_response :success
    assert_select "form"
    assert_select "input[name='login']"
    assert_select "input[name='password']"
    assert_select "input[type='submit']"
  end

  test "authenticated user is redirected from new session page" do
    sign_in_as(@user)
    get new_session_path
    assert_redirected_to client_path(@user.client_profile.slug)
  end

  test "authenticated artist is redirected to artist profile" do
    sign_in_as(@artist_user)
    get new_session_path
    assert_redirected_to artist_path(@artist_user.artist_profile.slug)
  end

  test "authenticated client is redirected to client profile" do
    sign_in_as(@user)
    get new_session_path
    assert_redirected_to client_path(@user.client_profile.slug)
  end

  test "should create session with valid email credentials" do
    assert_difference 'Session.count', 1 do
      post session_path, params: { 
        login: @user.email_address, 
        password: "password123" 
      }
    end
    
    assert_redirected_to root_path
    assert_not_nil session[:session_id] if defined?(session[:session_id])
    assert_not_nil cookies.signed[:session_id]
  end

  test "should create session with valid username credentials" do
    assert_difference 'Session.count', 1 do
      post session_path, params: { 
        login: @user.username, 
        password: "password123" 
      }
    end
    
    assert_redirected_to root_path
    assert_not_nil session[:session_id] if defined?(session[:session_id])
    assert_not_nil cookies.signed[:session_id]
  end

  test "should not create session with invalid email" do
    assert_no_difference 'Session.count' do
      post session_path, params: { 
        login: "<EMAIL>", 
        password: "password123" 
      }
    end
    
    assert_redirected_to new_session_path
    assert_equal "Try another email address/username or password.", flash[:alert]
  end

  test "should not create session with invalid username" do
    assert_no_difference 'Session.count' do
      post session_path, params: { 
        login: "nonexistentuser", 
        password: "password123" 
      }
    end
    
    assert_redirected_to new_session_path
    assert_equal "Try another email address/username or password.", flash[:alert]
  end

  test "should not create session with invalid password" do
    assert_no_difference 'Session.count' do
      post session_path, params: { 
        login: @user.email_address, 
        password: "wrongpassword" 
      }
    end
    
    assert_redirected_to new_session_path
    assert_equal "Try another email address/username or password.", flash[:alert]
  end

  test "should create session for unapproved user" do
    # Authentication doesn't check approval status - that's handled elsewhere
    assert_difference 'Session.count', 1 do
      post session_path, params: { 
        login: @unapproved_user.email_address, 
        password: "password123" 
      }
    end
    
    assert_redirected_to root_path
  end

  test "should destroy session" do
    sign_in_as(@user)
    session_id = Current.session.id
    
    assert_difference 'Session.count', -1 do
      delete session_path
    end
    
    assert_redirected_to new_session_path
    assert_raises(ActiveRecord::RecordNotFound) do
      Session.find(session_id)
    end
  end

  test "destroying session when not logged in should redirect safely" do
    delete session_path
    assert_redirected_to new_session_path
  end

  test "session stores user agent and ip address" do
    post session_path, params: { 
      login: @user.email_address, 
      password: "password123" 
    }, headers: { 
      "User-Agent" => "Test Browser 1.0",
      "REMOTE_ADDR" => "*************"
    }
    
    session = Session.last
    assert_equal "Test Browser 1.0", session.user_agent
    assert_equal "*************", session.ip_address
  end

  test "redirect after authentication uses return_to_url" do
    # Set a return URL in session
    get artists_path # This should set return_to_after_authenticating
    
    post session_path, params: { 
      login: @user.email_address, 
      password: "password123" 
    }
    
    assert_redirected_to artists_path
  end

  test "redirect after authentication defaults to root when no return_to" do
    post session_path, params: { 
      login: @user.email_address, 
      password: "password123" 
    }
    
    assert_redirected_to root_path
  end

  test "rate limiting prevents brute force attacks" do
    # This test verifies the rate limiting configuration exists
    # Actual rate limiting testing would require 11 requests in 3 minutes
    11.times do |i|
      post session_path, params: { 
        login: "<EMAIL>", 
        password: "invalid" 
      }
      
      if i < 10
        assert_redirected_to new_session_path
        assert_equal "Try another email address/username or password.", flash[:alert]
      else
        # 11th request should be rate limited
        assert_redirected_to new_session_path
        assert_equal "Try again later.", flash[:alert]
        break
      end
    end
  end

  test "session cookie is httponly and same_site" do
    post session_path, params: { 
      login: @user.email_address, 
      password: "password123" 
    }
    
    # Verify the cookie was set (we can't directly test httponly/same_site in integration tests)
    assert_not_nil cookies.signed[:session_id]
  end

  test "session persists user association" do
    post session_path, params: { 
      login: @user.email_address, 
      password: "password123" 
    }
    
    session = Session.find(cookies.signed[:session_id])
    assert_equal @user, session.user
  end

  test "case insensitive email authentication" do
    assert_difference 'Session.count', 1 do
      post session_path, params: { 
        login: @user.email_address.upcase, 
        password: "password123" 
      }
    end
    
    assert_redirected_to root_path
  end

  test "case insensitive username authentication" do
    assert_difference 'Session.count', 1 do
      post session_path, params: { 
        login: @user.username.upcase, 
        password: "password123" 
      }
    end
    
    assert_redirected_to root_path
  end

  private

  def sign_in_as(user)
    post session_path, params: { login: user.email_address, password: "password123" }
  end
end