require "test_helper"

class PortfolioItemTest < ActiveSupport::TestCase
  def setup
    @artist_profile = create(:artist_profile)
    @portfolio_item = build(:portfolio_item, artist_profile: @artist_profile)
  end

  test "should be valid with valid attributes" do
    assert @portfolio_item.valid?
    assert @portfolio_item.save
  end

  test "should belong to artist profile" do
    assert_respond_to @portfolio_item, :artist_profile
    assert @portfolio_item.artist_profile.present?
  end

  test "should require image attachment" do
    portfolio_item = PortfolioItem.new(artist_profile: @artist_profile, position: 0)
    assert_not portfolio_item.valid?
    assert_includes portfolio_item.errors[:image], "must be attached"
  end

  test "should require position" do
    @portfolio_item.position = nil
    assert_not @portfolio_item.valid?
    assert_includes @portfolio_item.errors[:position], "can't be blank"
  end

  test "should require position to be non-negative" do
    @portfolio_item.position = -1
    assert_not @portfolio_item.valid?
    assert_includes @portfolio_item.errors[:position], "must be greater than or equal to 0"
  end

  test "should allow zero position" do
    @portfolio_item.position = 0
    assert @portfolio_item.valid?
  end

  test "should have ordered scope" do
    item1 = create(:portfolio_item, artist_profile: @artist_profile, position: 2)
    item2 = create(:portfolio_item, artist_profile: @artist_profile, position: 0)
    item3 = create(:portfolio_item, artist_profile: @artist_profile, position: 1)
    
    ordered_items = PortfolioItem.ordered
    
    assert_equal item2, ordered_items.first
    assert_equal item3, ordered_items.second
    assert_equal item1, ordered_items.third
  end

  test "should have many inspiration board items" do
    @portfolio_item.save!
    
    user = create(:user, :approved)
    board = create(:inspiration_board, user: user)
    inspiration_item = InspirationBoardItem.create!(
      inspiration_board: board,
      portfolio_item: @portfolio_item
    )
    
    assert_includes @portfolio_item.inspiration_board_items, inspiration_item
  end

  test "should destroy inspiration board items when destroyed" do
    @portfolio_item.save!
    
    user = create(:user, :approved)
    board = create(:inspiration_board, user: user)
    inspiration_item = InspirationBoardItem.create!(
      inspiration_board: board,
      portfolio_item: @portfolio_item
    )
    item_id = inspiration_item.id
    
    @portfolio_item.destroy
    assert_raises(ActiveRecord::RecordNotFound) { InspirationBoardItem.find(item_id) }
  end

  test "should allow caption to be blank" do
    @portfolio_item.caption = nil
    assert @portfolio_item.valid?
    
    @portfolio_item.caption = ""
    assert @portfolio_item.valid?
  end

  test "should allow caption to be present" do
    @portfolio_item.caption = "My latest tattoo work"
    assert @portfolio_item.valid?
    assert @portfolio_item.save
    
    @portfolio_item.reload
    assert_equal "My latest tattoo work", @portfolio_item.caption
  end
end