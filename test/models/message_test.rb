require "test_helper"

class MessageTest < ActiveSupport::Test<PERSON>ase
  def setup
    @user1 = create(:user, :client, :approved)
    @user2 = create(:user, :artist, :approved)
    @conversation = create(:conversation, user1: @user1, user2: @user2)
    @message = create(:message, conversation: @conversation, sender: @user1)
  end

  test "should belong to conversation and sender" do
    assert_equal @conversation, @message.conversation
    assert_equal @user1, @message.sender
  end

  test "should validate presence of content when no image attached" do
    message = Message.new(conversation: @conversation, sender: @user1, content: "")
    assert_not message.valid?
    assert_includes message.errors[:content], "can't be blank"
  end

  test "should be valid with image but no content" do
    message = Message.new(conversation: @conversation, sender: @user1, content: "")
    # Simulate image attachment
    message.define_singleton_method(:image_attached?) { true }
    
    assert message.valid?
  end

  test "should validate content length" do
    long_content = "a" * 1001
    message = Message.new(conversation: @conversation, sender: @user1, content: long_content)
    assert_not message.valid?
    assert_includes message.errors[:content], "is too long (maximum is 1000 characters)"
  end

  test "should default read to false" do
    message = Message.new(conversation: @conversation, sender: @user1, content: "Test")
    assert_equal false, message.read
  end

  test "mark_as_read! should set read to true and set read_at" do
    assert_equal false, @message.read
    assert_nil @message.read_at
    
    @message.mark_as_read!
    @message.reload
    
    assert_equal true, @message.read
    assert_not_nil @message.read_at
  end

  test "recipient should return the other participant" do
    assert_equal @user2, @message.recipient
  end

  test "should update conversation timestamp after create" do
    old_timestamp = @conversation.last_message_at
    sleep 0.1 # Ensure different timestamp
    
    new_message = create(:message, conversation: @conversation, sender: @user2)
    @conversation.reload
    
    assert @conversation.last_message_at > old_timestamp
    assert_equal new_message.created_at.to_i, @conversation.last_message_at.to_i
  end

  test "should update conversation timestamp after read status changes" do
    # The after_update callback only triggers when read status changes
    @message.update!(read: false) # Ensure it starts as unread
    old_timestamp = @conversation.last_message_at
    sleep 0.1
    
    @message.mark_as_read!
    @conversation.reload
    
    assert @conversation.last_message_at >= old_timestamp, "Expected conversation timestamp to be updated"
  end

  test "recent scope should order by created_at" do
    # Clear existing messages to ensure clean test
    Message.destroy_all
    
    old_message = create(:message, conversation: @conversation, sender: @user1, created_at: 1.hour.ago)
    recent_message = create(:message, conversation: @conversation, sender: @user2, created_at: 1.minute.ago)
    
    messages = Message.recent
    assert_equal old_message, messages.first
    assert_equal recent_message, messages.last
  end

  test "unread scope should only return unread messages" do
    read_message = create(:message, conversation: @conversation, sender: @user1, read: true)
    unread_message = create(:message, conversation: @conversation, sender: @user2, read: false)
    
    unread_messages = Message.unread
    assert_includes unread_messages, unread_message
    assert_not_includes unread_messages, read_message
  end

  test "image_attached? should return false when no image" do
    assert_equal false, @message.image_attached?
  end
end
