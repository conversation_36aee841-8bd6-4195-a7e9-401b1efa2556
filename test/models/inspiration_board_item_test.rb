require "test_helper"

class InspirationBoardItemTest < ActiveSupport::TestCase
  def setup
    @user = create(:user, :approved)
    @board = create(:inspiration_board, user: @user)
    @post = create(:post)
    @artist_profile = create(:artist_profile)
    @portfolio_item = create(:portfolio_item, artist_profile: @artist_profile)
    @item = build(:inspiration_board_item, inspiration_board: @board, post: @post)
  end

  test "should be valid with valid attributes" do
    assert @item.valid?
    assert @item.save
  end

  test "should belong to inspiration board" do
    assert_respond_to @item, :inspiration_board
    assert @item.inspiration_board.present?
  end

  test "should belong to post" do
    assert_respond_to @item, :post
    assert @item.post.present?
  end

  test "should not allow duplicate post in same board" do
    @item.save
    
    duplicate_item = build(:inspiration_board_item, 
                          inspiration_board: @board, 
                          post: @post)
    
    assert_not duplicate_item.valid?
    assert_includes duplicate_item.errors[:inspiration_board_id], "Item is already in this inspiration board"
  end

  test "should allow same post in different boards" do
    @item.save
    
    other_board = create(:inspiration_board, user: @user)
    other_item = build(:inspiration_board_item, 
                      inspiration_board: other_board, 
                      post: @post)
    
    assert other_item.valid?
    assert other_item.save
  end

  test "should allow different posts in same board" do
    @item.save
    
    other_post = create(:post)
    other_item = build(:inspiration_board_item, 
                      inspiration_board: @board, 
                      post: other_post)
    
    assert other_item.valid?
    assert other_item.save
  end

  test "should have notes field" do
    @item.notes = "This is a great design for my next tattoo"
    assert @item.valid?
    assert @item.save
    
    @item.reload
    assert_equal "This is a great design for my next tattoo", @item.notes
  end

  test "should allow blank notes" do
    @item.notes = nil
    assert @item.valid?
    
    @item.notes = ""
    assert @item.valid?
  end

  test "should have with_notes scope" do
    item_with_notes = create(:inspiration_board_item, notes: "Great design")
    item_without_notes = create(:inspiration_board_item, notes: nil)
    item_with_blank_notes = create(:inspiration_board_item, notes: "")
    
    items_with_notes = InspirationBoardItem.with_notes
    
    assert_includes items_with_notes, item_with_notes
    assert_not_includes items_with_notes, item_without_notes
    assert_not_includes items_with_notes, item_with_blank_notes
  end

  test "should have recent scope" do
    old_item = create(:inspiration_board_item, created_at: 2.days.ago)
    new_item = create(:inspiration_board_item, created_at: 1.hour.ago)
    
    recent_items = InspirationBoardItem.recent
    
    assert_equal new_item, recent_items.first
    assert_equal old_item, recent_items.last
  end

  test "should validate presence of inspiration board" do
    @item.inspiration_board = nil
    assert_not @item.valid?
    assert_includes @item.errors[:inspiration_board], "must exist"
  end

  test "should validate presence of post" do
    @item.post = nil
    assert_not @item.valid?
    assert_includes @item.errors[:base], "Must reference either a post or portfolio item"
  end

  test "should be valid with portfolio item instead of post" do
    portfolio_item = build(:inspiration_board_item, 
                          inspiration_board: @board, 
                          portfolio_item: @portfolio_item,
                          post: nil)
    
    assert portfolio_item.valid?
    assert portfolio_item.save
  end

  test "should not allow both post and portfolio item" do
    @item.portfolio_item = @portfolio_item
    
    assert_not @item.valid?
    assert_includes @item.errors[:base], "Cannot reference both post and portfolio item"
  end

  test "should require either post or portfolio item" do
    @item.post = nil
    @item.portfolio_item = nil
    
    assert_not @item.valid?
    assert_includes @item.errors[:base], "Must reference either a post or portfolio item"
  end

  test "should have from_posts scope" do
    post_item = create(:inspiration_board_item, post: @post)
    portfolio_item = create(:inspiration_board_item, portfolio_item: @portfolio_item, post: nil)
    
    from_posts = InspirationBoardItem.from_posts
    
    assert_includes from_posts, post_item
    assert_not_includes from_posts, portfolio_item
  end

  test "should have from_portfolio scope" do
    post_item = create(:inspiration_board_item, post: @post)
    portfolio_item = create(:inspiration_board_item, portfolio_item: @portfolio_item, post: nil)
    
    from_portfolio = InspirationBoardItem.from_portfolio
    
    assert_includes from_portfolio, portfolio_item
    assert_not_includes from_portfolio, post_item
  end

  test "image method should return post image when from post" do
    @item.save
    assert_equal @post.image, @item.image
  end

  test "image method should return portfolio item image when from portfolio" do
    portfolio_item = create(:inspiration_board_item, 
                           inspiration_board: @board, 
                           portfolio_item: @portfolio_item, 
                           post: nil)
    
    assert_equal @portfolio_item.image, portfolio_item.image
  end

  test "source_artist method should return correct artist profile" do
    @item.save
    assert_equal @post.artist_profile, @item.source_artist
    
    portfolio_item = create(:inspiration_board_item, 
                           inspiration_board: @board, 
                           portfolio_item: @portfolio_item, 
                           post: nil)
    
    assert_equal @portfolio_item.artist_profile, portfolio_item.source_artist
  end

  test "source method should return the referenced source" do
    @item.save
    assert_equal @post, @item.source
    
    portfolio_item = create(:inspiration_board_item, 
                           inspiration_board: @board, 
                           portfolio_item: @portfolio_item, 
                           post: nil)
    
    assert_equal @portfolio_item, portfolio_item.source
  end
end
