require "test_helper"

class WaitlistEntryTest < ActiveSupport::TestCase
  include ActiveJob::<PERSON><PERSON><PERSON><PERSON>
  def setup
    @artist_profile = create(:artist_profile)
    @waitlist_entry = build(:waitlist_entry, artist_profile: @artist_profile)
  end

  test "should create waitlist entry with valid attributes" do
    assert @waitlist_entry.valid?
    assert @waitlist_entry.save
  end

  test "should belong to artist_profile" do
    assert_respond_to @waitlist_entry, :artist_profile
    @waitlist_entry.save
    assert_equal @artist_profile, @waitlist_entry.artist_profile
  end

  test "should have attachment" do
    assert_respond_to @waitlist_entry, :attachment
  end

  # Validation tests
  test "should require name" do
    @waitlist_entry.name = nil
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:name], "can't be blank"
  end

  test "should require email" do
    @waitlist_entry.email = nil
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:email], "can't be blank"
  end

  test "should validate email format" do
    @waitlist_entry.email = "invalid-email"
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:email], "is invalid"

    @waitlist_entry.email = "<EMAIL>"
    assert @waitlist_entry.valid?
  end

  test "should validate name length" do
    @waitlist_entry.name = "a" * 101
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:name], "is too long (maximum is 100 characters)"
  end

  test "should validate phone_number length" do
    @waitlist_entry.phone_number = "1" * 21
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:phone_number], "is too long (maximum is 20 characters)"
  end

  test "should validate message length" do
    @waitlist_entry.message = "a" * 1001
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:message], "is too long (maximum is 1000 characters)"
  end

  test "should allow blank phone_number and message" do
    @waitlist_entry.phone_number = nil
    @waitlist_entry.message = nil
    assert @waitlist_entry.valid?

    @waitlist_entry.phone_number = ""
    @waitlist_entry.message = ""
    assert @waitlist_entry.valid?
  end

  test "should validate email uniqueness per artist" do
    @waitlist_entry.save
    
    duplicate_entry = build(:waitlist_entry, 
                           artist_profile: @artist_profile, 
                           email: @waitlist_entry.email)
    
    assert_not duplicate_entry.valid?
    assert_includes duplicate_entry.errors[:email], "is already on this artist's waitlist"
  end

  test "should allow same email for different artists" do
    other_artist = create(:artist_profile, name: "Other Artist")
    @waitlist_entry.save
    
    other_entry = build(:waitlist_entry, 
                       artist_profile: other_artist, 
                       email: @waitlist_entry.email)
    
    assert other_entry.valid?
    assert other_entry.save
  end

  # Status enum tests
  test "should have pending status by default" do
    @waitlist_entry.save
    assert @waitlist_entry.pending?
    assert_equal "pending", @waitlist_entry.status
  end

  test "should have status enum" do
    assert_respond_to @waitlist_entry, :pending?
    assert_respond_to @waitlist_entry, :contacted?
    
    @waitlist_entry.status = :contacted
    assert @waitlist_entry.contacted?
    assert_not @waitlist_entry.pending?
  end

  # Scope tests
  test "should have recent scope" do
    assert_respond_to WaitlistEntry, :recent
    
    old_entry = create(:waitlist_entry, artist_profile: @artist_profile, created_at: 2.days.ago)
    new_entry = create(:waitlist_entry, artist_profile: @artist_profile, email: "<EMAIL>", created_at: 1.day.ago)
    
    recent_entries = WaitlistEntry.recent
    assert_equal new_entry, recent_entries.first
    assert_equal old_entry, recent_entries.last
  end

  test "should have pending and contacted scopes" do
    pending_entry = create(:waitlist_entry, artist_profile: @artist_profile, status: :pending)
    contacted_entry = create(:waitlist_entry, artist_profile: @artist_profile, email: "<EMAIL>", status: :contacted)
    
    assert_includes WaitlistEntry.pending, pending_entry
    assert_not_includes WaitlistEntry.pending, contacted_entry
    
    assert_includes WaitlistEntry.contacted, contacted_entry
    assert_not_includes WaitlistEntry.contacted, pending_entry
  end

  # File attachment validation tests
  test "should have attachment validation methods" do
    # Test that the validation methods exist as private methods
    assert @waitlist_entry.respond_to?(:attachment_is_image, true)
    assert @waitlist_entry.respond_to?(:attachment_size_limit, true)
  end

  # Callback tests
  test "should have send notification email callback method" do
    assert @waitlist_entry.respond_to?(:send_notification_email, true)
  end

  test "should send notification email after create" do
    # Test that the callback is triggered
    assert_enqueued_jobs 1, only: ActionMailer::MailDeliveryJob do
      @waitlist_entry.save
    end
  end

  test "should not send notification email on update" do
    @waitlist_entry.save
    
    perform_enqueued_jobs do
      # Clear previous emails
      ActionMailer::Base.deliveries.clear
      
      # Update should not trigger email
      @waitlist_entry.update(status: :contacted)
      
      # Check no new emails were sent
      assert_equal 0, ActionMailer::Base.deliveries.size
    end
  end
end
