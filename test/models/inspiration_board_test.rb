require "test_helper"

class InspirationBoardTest < ActiveSupport::TestCase
  def setup
    @user = create(:user, :approved)
    @inspiration_board = build(:inspiration_board, user: @user)
  end

  test "should create inspiration board with valid attributes" do
    assert @inspiration_board.valid?
    assert @inspiration_board.save
  end

  test "should belong to user" do
    assert_respond_to @inspiration_board, :user
    assert @inspiration_board.user.present?
  end

  test "should require name" do
    @inspiration_board.name = nil
    assert_not @inspiration_board.valid?
    assert_includes @inspiration_board.errors[:name], "can't be blank"
  end

  test "should require unique name per user" do
    @inspiration_board.save
    duplicate_board = build(:inspiration_board, user: @user, name: @inspiration_board.name)
    
    assert_not duplicate_board.valid?
    assert_includes duplicate_board.errors[:name], "has already been taken"
  end

  test "should allow same name for different users" do
    @inspiration_board.save
    other_user = create(:user, :approved)
    other_board = build(:inspiration_board, user: other_user, name: @inspiration_board.name)
    
    assert other_board.valid?
    assert other_board.save
  end

  test "should have many inspiration board items" do
    @inspiration_board.save
    post = create(:post)
    
    item = create(:inspiration_board_item, inspiration_board: @inspiration_board, post: post)
    assert_includes @inspiration_board.inspiration_board_items, item
  end

  test "should have posts through inspiration board items" do
    @inspiration_board.save
    post = create(:post)
    
    create(:inspiration_board_item, inspiration_board: @inspiration_board, post: post)
    assert_includes @inspiration_board.posts, post
  end

  test "should destroy associated items when destroyed" do
    @inspiration_board.save
    item = create(:inspiration_board_item, inspiration_board: @inspiration_board)
    item_id = item.id
    
    @inspiration_board.destroy
    assert_raises(ActiveRecord::RecordNotFound) do
      InspirationBoardItem.find(item_id)
    end
  end

  test "should have by_user scope" do
    @inspiration_board.save
    other_user = create(:user, :approved)
    other_board = create(:inspiration_board, user: other_user)
    
    user_boards = InspirationBoard.by_user(@user)
    assert_includes user_boards, @inspiration_board
    assert_not_includes user_boards, other_board
  end

  test "should default to private" do
    new_board = create(:inspiration_board, user: @user)
    assert new_board.privacy
    assert new_board.private?
    assert_not new_board.public?
  end

  test "should have public? and private? methods" do
    @inspiration_board.privacy = false
    assert @inspiration_board.public?
    assert_not @inspiration_board.private?
    
    @inspiration_board.privacy = true
    assert @inspiration_board.private?
    assert_not @inspiration_board.public?
  end

  test "should scope public boards" do
    public_board = create(:inspiration_board, :public, user: @user)
    private_board = create(:inspiration_board, :private, user: @user)
    
    public_boards = InspirationBoard.public_boards
    
    assert_includes public_boards, public_board
    assert_not_includes public_boards, private_board
  end

  test "should scope private boards" do
    public_board = create(:inspiration_board, :public, user: @user)
    private_board = create(:inspiration_board, :private, user: @user)
    
    private_boards = InspirationBoard.private_boards
    
    assert_includes private_boards, private_board
    assert_not_includes private_boards, public_board
  end
end