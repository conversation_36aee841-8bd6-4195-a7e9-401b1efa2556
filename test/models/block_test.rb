require "test_helper"

class BlockTest < ActiveSupport::Test<PERSON>ase
  def setup
    @user1 = create(:user, :client, :approved)
    @user2 = create(:user, :artist, :approved)
    @block = create(:block, blocker: @user1, blocked: @user2)
  end

  test "should belong to blocker and blocked users" do
    assert_equal @user1, @block.blocker
    assert_equal @user2, @block.blocked
  end

  test "should validate unique blocker and blocked pair" do
    duplicate_block = Block.new(blocker: @user1, blocked: @user2)
    assert_not duplicate_block.valid?
    assert_includes duplicate_block.errors[:blocker_id], "has already been taken"
  end

  test "should not allow user to block themselves" do
    self_block = Block.new(blocker: @user1, blocked: @user1)
    assert_not self_block.valid?
    assert_includes self_block.errors[:blocked], "cannot block yourself"
  end

  test "should allow different users to block the same user" do
    user3 = create(:user, :client, :approved)
    block2 = Block.new(blocker: user3, blocked: @user2)
    assert block2.valid?
  end

  test "should allow user to block multiple different users" do
    user3 = create(:user, :artist, :approved)
    block2 = Block.new(blocker: @user1, blocked: user3)
    assert block2.valid?
  end
end
