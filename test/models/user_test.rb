require "test_helper"

class UserTest < ActiveSupport::TestCase
  def setup
    @user = build(:user)
  end

  test "should create user with valid attributes" do
    assert @user.valid?
    assert @user.save
  end

  test "should require email_address" do
    @user.email_address = nil
    assert_not @user.valid?
    assert_includes @user.errors[:email_address], "can't be blank"
  end

  test "should require username" do
    @user.username = nil
    assert_not @user.valid?
    assert_includes @user.errors[:username], "can't be blank"
  end

  test "should require unique email_address" do
    create(:user, email_address: "<EMAIL>")
    @user.email_address = "<EMAIL>"
    assert_not @user.valid?
    assert_includes @user.errors[:email_address], "has already been taken"
  end

  test "should require unique username" do
    create(:user, username: "testuser")
    @user.username = "testuser"
    assert_not @user.valid?
    assert_includes @user.errors[:username], "has already been taken"
  end

  test "should normalize email_address" do
    @user.email_address = "  <EMAIL>  "
    @user.save
    assert_equal "<EMAIL>", @user.email_address
  end

  test "should normalize username" do
    @user.username = "  TestUser  "
    @user.save
    assert_equal "TestUser", @user.username
  end

  test "should validate username format" do
    valid_usernames = %w[user123 test_user user.name my-username]
    invalid_usernames = ["user name", "user@email", "user#tag", "ab", "a" * 31]

    valid_usernames.each do |username|
      @user.username = username
      assert @user.valid?, "#{username} should be valid"
    end

    invalid_usernames.each do |username|
      @user.username = username
      assert_not @user.valid?, "#{username} should be invalid"
    end
  end

  test "should validate username length" do
    @user.username = "ab"
    assert_not @user.valid?
    assert_includes @user.errors[:username], "is too short (minimum is 3 characters)"

    @user.username = "a" * 31
    assert_not @user.valid?
    assert_includes @user.errors[:username], "is too long (maximum is 30 characters)"

    @user.username = "abc"
    assert @user.valid?

    @user.username = "a" * 30
    assert @user.valid?
  end

  test "username uniqueness should be case insensitive" do
    create(:user, username: "TestUser")
    @user.username = "testuser"
    assert_not @user.valid?
    assert_includes @user.errors[:username], "has already been taken"
  end

  test "find_by_email_or_username should find user by email" do
    user = create(:user, email_address: "<EMAIL>", username: "testuser")
    
    found_user = User.find_by_email_or_username("<EMAIL>")
    assert_equal user, found_user
    
    found_user = User.find_by_email_or_username("<EMAIL>")
    assert_equal user, found_user
  end

  test "find_by_email_or_username should find user by username" do
    user = create(:user, email_address: "<EMAIL>", username: "testuser")
    
    found_user = User.find_by_email_or_username("testuser")
    assert_equal user, found_user
    
    found_user = User.find_by_email_or_username("TestUser")
    assert_equal user, found_user
  end

  test "find_by_email_or_username should return nil for non-existent user" do
    assert_nil User.find_by_email_or_username("<EMAIL>")
    assert_nil User.find_by_email_or_username("nonexistent")
    assert_nil User.find_by_email_or_username("")
    assert_nil User.find_by_email_or_username(nil)
  end

  test "should have default role as client" do
    assert @user.client?
    assert_not @user.artist?
  end

  test "should have default admin as false" do
    assert_not @user.admin?
  end

  test "should have default approved as false" do
    assert_not @user.approved?
  end

  test "should set role to artist" do
    @user.role = :artist
    assert @user.artist?
    assert_not @user.client?
  end

  test "display_name should return profile name if available" do
    @user.save
    profile = create(:client_profile, user: @user, name: "John Doe")
    assert_equal "John Doe", @user.display_name
  end

  test "display_name should return username if no profile" do
    @user.username = "johndoe"
    assert_equal "johndoe", @user.display_name
  end

  test "approved_or_admin should return true for approved user" do
    @user.approved = true
    assert @user.approved_or_admin?
  end

  test "approved_or_admin should return true for admin user" do
    @user.admin = true
    assert @user.approved_or_admin?
  end

  test "approved_or_admin should return false for unapproved non-admin" do
    @user.approved = false
    @user.admin = false
    assert_not @user.approved_or_admin?
  end

  test "should destroy associated profiles when user is destroyed" do
    user = create(:user, :artist)
    artist_profile_id = user.artist_profile.id
    
    user.destroy
    assert_raises(ActiveRecord::RecordNotFound) do
      ArtistProfile.find(artist_profile_id)
    end
  end

  test "should destroy associated sessions when user is destroyed" do
    user = create(:user)
    session = user.sessions.create!
    session_id = session.id
    
    user.destroy
    assert_raises(ActiveRecord::RecordNotFound) do
      Session.find(session_id)
    end
  end

  test "profile should return artist_profile for artist" do
    user = create(:user, :artist)
    assert_equal user.artist_profile, user.profile
  end

  test "profile should return client_profile for client" do
    user = create(:user, :client)
    assert_equal user.client_profile, user.profile
  end

  # Messaging system tests
  test "conversations should return user's conversations" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    user3 = create(:user, :client, :approved)
    
    conv1 = create(:conversation, user1: user1, user2: user2)
    conv2 = create(:conversation, user1: user3, user2: user1)
    create(:conversation, user1: user2, user2: user3) # Should not include this
    
    conversations = user1.conversations
    assert_includes conversations, conv1
    assert_includes conversations, conv2
    assert_equal 2, conversations.count
  end

  test "unread_messages_count should count unread messages across all conversations" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    user3 = create(:user, :client, :approved)
    
    conv1 = create(:conversation, user1: user1, user2: user2)
    conv2 = create(:conversation, user1: user1, user2: user3)
    
    create(:message, conversation: conv1, sender: user2, read: false)
    create(:message, conversation: conv1, sender: user2, read: false)
    create(:message, conversation: conv2, sender: user3, read: false)
    create(:message, conversation: conv1, sender: user1, read: false) # Own message
    
    assert_equal 3, user1.unread_messages_count
  end

  test "blocked? should return true if user has blocked another user" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    create(:block, blocker: user1, blocked: user2)
    
    assert user1.blocked?(user2)
    assert_not user2.blocked?(user1)
  end

  test "blocked_by? should return true if user is blocked by another user" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    create(:block, blocker: user2, blocked: user1)
    
    assert user1.blocked_by?(user2)
    assert_not user2.blocked_by?(user1)
  end

  test "can_message? should return false if users are the same" do
    user = create(:user, :client, :approved)
    assert_not user.can_message?(user)
  end

  test "can_message? should return false if user has blocked other user" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    create(:block, blocker: user1, blocked: user2)
    
    assert_not user1.can_message?(user2)
  end

  test "can_message? should return false if user is blocked by other user" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    create(:block, blocker: user2, blocked: user1)
    
    assert_not user1.can_message?(user2)
  end

  test "can_message? should return false if current user is artist with messages disabled" do
    artist = create(:user, :artist, :approved)
    client = create(:user, :client, :approved)
    
    artist.artist_profile.update!(messages_enabled: false)
    
    assert_not artist.can_message?(client)
  end

  test "can_message? should return false if target user is artist with messages disabled" do
    artist = create(:user, :artist, :approved)
    client = create(:user, :client, :approved)
    
    artist.artist_profile.update!(messages_enabled: false)
    
    assert_not client.can_message?(artist)
  end

  test "can_message? should return true for valid messaging scenario" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    assert user1.can_message?(user2)
    assert user2.can_message?(user1)
  end

  test "should destroy associated conversations when user is destroyed" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    conv = create(:conversation, user1: user1, user2: user2)
    conversation_id = conv.id
    
    user1.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) do
      Conversation.find(conversation_id)
    end
  end

  test "should destroy associated messages when user is destroyed" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    conv = create(:conversation, user1: user1, user2: user2)
    message = create(:message, conversation: conv, sender: user1)
    message_id = message.id
    
    user1.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) do
      Message.find(message_id)
    end
  end

  test "should destroy associated blocks when user is destroyed" do
    user1 = create(:user, :client, :approved)
    user2 = create(:user, :artist, :approved)
    
    block = create(:block, blocker: user1, blocked: user2)
    block_id = block.id
    
    user1.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) do
      Block.find(block_id)
    end
  end
end
