require "test_helper"

class ConversationTest < ActiveSupport::TestCase
  def setup
    @user1 = create(:user, :client, :approved)
    @user2 = create(:user, :artist, :approved)
    @conversation = create(:conversation, user1: @user1, user2: @user2)
  end

  test "should belong to two users" do
    assert_equal @user1, @conversation.user1
    assert_equal @user2, @conversation.user2
  end

  test "should have many messages" do
    message = create(:message, conversation: @conversation, sender: @user1)
    assert_includes @conversation.messages, message
  end

  test "should validate unique user pair" do
    duplicate_conversation = Conversation.new(user1: @user1, user2: @user2)
    assert_not duplicate_conversation.valid?
    assert_includes duplicate_conversation.errors[:user1_id], "has already been taken"
  end

  test "should not allow user to conversation with themselves" do
    conversation = Conversation.new(user1: @user1, user2: @user1)
    assert_not conversation.valid?
    assert_includes conversation.errors[:user2], "cannot be the same as user1"
  end

  test "participants should return both users" do
    participants = @conversation.participants
    assert_includes participants, @user1
    assert_includes participants, @user2
    assert_equal 2, participants.length
  end

  test "other_participant should return the other user" do
    assert_equal @user2, @conversation.other_participant(@user1)
    assert_equal @user1, @conversation.other_participant(@user2)
  end

  test "latest_message should return the most recent message" do
    old_message = create(:message, conversation: @conversation, sender: @user1, created_at: 1.hour.ago)
    latest_message = create(:message, conversation: @conversation, sender: @user2, created_at: 1.minute.ago)
    
    assert_equal latest_message, @conversation.latest_message
  end

  test "unread_count should count unread messages for user" do
    # Clear all messages and create fresh test data
    Message.destroy_all
    
    msg1 = create(:message, conversation: @conversation, sender: @user2, read: false)
    msg2 = create(:message, conversation: @conversation, sender: @user2, read: false)  
    msg3 = create(:message, conversation: @conversation, sender: @user1, read: false) # Own message - shouldn't count
    msg4 = create(:message, conversation: @conversation, sender: @user2, read: true) # Read message - shouldn't count
    
    # User1 should see 2 unread messages from user2
    assert_equal 2, @conversation.unread_count(@user1)
    
    # User2 should see 1 unread message from user1
    assert_equal 1, @conversation.unread_count(@user2)
  end

  test "between_users should find existing conversation" do
    found_conversation = Conversation.between_users(@user1, @user2)
    assert_equal @conversation, found_conversation
    
    # Should work in reverse order too
    found_conversation = Conversation.between_users(@user2, @user1)
    assert_equal @conversation, found_conversation
  end

  test "between_users should return nil if no conversation exists" do
    user3 = create(:user, :client, :approved)
    assert_nil Conversation.between_users(@user1, user3)
  end

  test "find_or_create_between should find existing conversation" do
    found_conversation = Conversation.find_or_create_between(@user1, @user2)
    assert_equal @conversation, found_conversation
  end

  test "find_or_create_between should create new conversation if none exists" do
    user3 = create(:user, :client, :approved)
    
    assert_difference 'Conversation.count', 1 do
      new_conversation = Conversation.find_or_create_between(@user1, user3)
      assert_not_nil new_conversation
      assert_includes new_conversation.participants, @user1
      assert_includes new_conversation.participants, user3
    end
  end

  test "find_or_create_between should maintain consistent user ordering" do
    @conversation.destroy # Remove existing
    
    # Create with users in different orders
    conv1 = Conversation.find_or_create_between(@user1, @user2)
    conv1.destroy
    conv2 = Conversation.find_or_create_between(@user2, @user1)
    
    # Should create with consistent ordering (lower id first)
    if @user1.id < @user2.id
      assert_equal @user1, conv2.user1
      assert_equal @user2, conv2.user2
    else
      assert_equal @user2, conv2.user1
      assert_equal @user1, conv2.user2
    end
  end

  test "recent scope should order by last_message_at" do
    # Clear all existing conversations to ensure clean test
    Conversation.destroy_all
    
    # Use specific timestamps to ensure proper ordering
    old_time = 2.hours.ago
    recent_time = 1.minute.ago
    
    old_conversation = create(:conversation, last_message_at: old_time)
    recent_conversation = create(:conversation, last_message_at: recent_time)
    
    conversations = Conversation.recent
    assert_equal recent_conversation, conversations.first
    assert_equal old_conversation, conversations.second
  end
end
