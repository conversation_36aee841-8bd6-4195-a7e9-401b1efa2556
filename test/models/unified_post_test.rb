require "test_helper"

class UnifiedPostTest < ActiveSupport::TestCase
  def setup
    @post = build(:post)
  end

  test "should create post with image and optional caption" do
    assert @post.valid?
    assert @post.save
    assert @post.image.attached?
  end

  test "should require image attachment" do
    @post.image.detach
    assert_not @post.valid?
    assert_includes @post.errors[:image], "must be attached"
  end

  test "should allow post without caption" do
    post = build(:post, :without_caption)
    assert post.valid?
    assert post.save
  end

  test "should have portfolio functionality" do
    @post.save
    
    # Default is not in portfolio
    assert_not @post.in_portfolio?
    
    # Can add to portfolio
    @post.add_to_portfolio!
    assert @post.in_portfolio?
    
    # Can remove from portfolio
    @post.remove_from_portfolio!
    assert_not @post.in_portfolio?
    
    # Can toggle
    @post.toggle_portfolio!
    assert @post.in_portfolio?
  end

  test "should have correct scopes" do
    feed_post = create(:post, in_portfolio: false)
    portfolio_post = create(:post, :in_portfolio)
    
    assert_includes Post.in_feed, feed_post
    assert_not_includes Post.in_feed, portfolio_post
    
    assert_includes Post.in_portfolio, portfolio_post
    assert_not_includes Post.in_portfolio, feed_post
  end

  test "should belong to artist profile" do
    assert_respond_to @post, :artist_profile
    assert @post.artist_profile.present?
  end

  test "should have comments" do
    @post.save
    user = create(:user, :approved)
    
    # Test comments
    comment = create(:comment, post: @post, user: user)
    assert_includes @post.comments, comment
    assert_equal 1, @post.comments_count
  end

  test "should auto-set published_at on creation" do
    @post.published_at = nil
    @post.save
    assert_not_nil @post.published_at
    assert @post.published_at <= Time.current
  end

  test "should have published and recent scopes" do
    published_post = create(:post, published_at: 1.hour.ago)
    unpublished_post = create(:post)
    unpublished_post.update_column(:published_at, nil)
    
    published = Post.published
    assert_includes published, published_post
    assert_not_includes published, unpublished_post
    
    # Test recent ordering (only published posts)
    old_post = create(:post, published_at: 2.days.ago)
    new_post = create(:post, published_at: 1.hour.ago)
    
    recent = Post.published.recent
    assert_equal [new_post, published_post, old_post], recent.to_a
  end

  test "should process image variants after creation" do
    assert @post.save
    assert @post.image.attached?
    
    # Verify the private method exists
    assert @post.respond_to?(:process_image_variants, true)
  end

  test "artist profile associations work correctly" do
    artist_profile = @post.artist_profile
    
    @post.save
    feed_post = create(:post, artist_profile: artist_profile, in_portfolio: false)
    portfolio_post = create(:post, artist_profile: artist_profile, in_portfolio: true)
    
    assert_includes artist_profile.posts, @post
    assert_includes artist_profile.posts, feed_post
    assert_includes artist_profile.posts, portfolio_post
    
    assert_includes artist_profile.feed_posts, feed_post
    assert_not_includes artist_profile.feed_posts, portfolio_post
    
    assert_includes artist_profile.portfolio_posts, portfolio_post
    assert_not_includes artist_profile.portfolio_posts, feed_post
  end
end