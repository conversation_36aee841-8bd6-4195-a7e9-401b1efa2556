require "test_helper"

class ArtistProfileTest < ActiveSupport::TestCase
  def setup
    @artist_profile = build(:artist_profile)
  end

  test "should create artist profile with valid attributes" do
    assert @artist_profile.valid?
    assert @artist_profile.save
  end

  test "should require name" do
    @artist_profile.name = nil
    assert_not @artist_profile.valid?
    assert_includes @artist_profile.errors[:name], "can't be blank"
  end

  test "should auto-generate slug from name" do
    @artist_profile.name = "Test Artist"
    @artist_profile.save
    assert_not_nil @artist_profile.slug
    assert_match(/test-artist/, @artist_profile.slug)
  end

  test "should ensure unique slugs" do
    existing = create(:artist_profile, name: "Test Artist")
    @artist_profile.name = "Test Artist" 
    @artist_profile.save
    
    assert_not_equal existing.slug, @artist_profile.slug
    assert @artist_profile.slug.include?("test-artist")
  end

  test "should belong to user" do
    assert_respond_to @artist_profile, :user
  end

  test "should have default messages_enabled as true" do
    @artist_profile.save
    assert @artist_profile.messages_enabled?
  end

  test "should have default available as true" do
    @artist_profile.save
    assert @artist_profile.available?
  end

  test "should generate slug from name" do
    @artist_profile.name = "<PERSON> Doe Artist"
    @artist_profile.save
    assert_match(/john-doe-artist/, @artist_profile.slug)
  end

  test "should generate unique slug when name conflict exists" do
    existing = create(:artist_profile, name: "<PERSON> Doe", slug: "john-doe")
    @artist_profile.name = "John Doe"
    @artist_profile.save
    
    assert_not_equal existing.slug, @artist_profile.slug
    assert_match(/john-doe-\d+/, @artist_profile.slug)
  end

  test "should regenerate slug when name changes" do
    @artist_profile.name = "Original Name"
    @artist_profile.save
    original_slug = @artist_profile.slug
    
    @artist_profile.name = "New Name"
    @artist_profile.save
    
    assert_not_equal original_slug, @artist_profile.slug
    assert_match(/new-name/, @artist_profile.slug)
  end

  test "should use slug for to_param" do
    @artist_profile.slug = "test-artist"
    assert_equal "test-artist", @artist_profile.to_param
  end

  test "should have many specialties through artist_specialties" do
    @artist_profile.save
    style1 = create(:style, title: "Traditional")
    style2 = create(:style, title: "Japanese")
    
    @artist_profile.specialties << [style1, style2]
    
    assert_includes @artist_profile.specialties, style1
    assert_includes @artist_profile.specialties, style2
    assert_equal 2, @artist_profile.specialties.count
  end

  test "should destroy associated artist_specialties when destroyed" do
    @artist_profile.save
    style = create(:style)
    artist_specialty = create(:artist_specialty, artist_profile: @artist_profile, style: style)
    artist_specialty_id = artist_specialty.id
    
    @artist_profile.destroy
    assert_raises(ActiveRecord::RecordNotFound) do
      ArtistSpecialty.find(artist_specialty_id)
    end
  end

  test "should validate url formats" do
    @artist_profile.instagram_url = "not-a-url"
    @artist_profile.save
    # Note: We haven't added URL format validation yet, but this test documents the expected behavior
    
    @artist_profile.website_url = "https://valid-url.com"
    @artist_profile.booking_link = "https://booking.com/artist"
    assert @artist_profile.save
  end

  test "should allow nil for optional fields" do
    @artist_profile.contact_email = nil
    @artist_profile.instagram_url = nil
    @artist_profile.website_url = nil
    @artist_profile.booking_link = nil
    @artist_profile.biography = nil
    @artist_profile.location = nil
    
    assert @artist_profile.valid?
  end

  test "should have friendly_id enabled" do
    assert_respond_to ArtistProfile, :friendly
    assert @artist_profile.class.included_modules.any? { |m| m.to_s.include?("FriendlyId") }
  end

  # Waitlist functionality tests
  test "should have default waitlist_enabled as false" do
    @artist_profile.save
    assert_not @artist_profile.waitlist_enabled?
  end

  test "should have many waitlist_entries" do
    @artist_profile.save
    entry1 = create(:waitlist_entry, artist_profile: @artist_profile)
    entry2 = create(:waitlist_entry, artist_profile: @artist_profile, email: "<EMAIL>")
    
    assert_includes @artist_profile.waitlist_entries, entry1
    assert_includes @artist_profile.waitlist_entries, entry2
    assert_equal 2, @artist_profile.waitlist_entries.count
  end

  test "should destroy associated waitlist_entries when destroyed" do
    @artist_profile.save
    entry = create(:waitlist_entry, artist_profile: @artist_profile)
    entry_id = entry.id
    
    @artist_profile.destroy
    assert_raises(ActiveRecord::RecordNotFound) do
      WaitlistEntry.find(entry_id)
    end
  end

  test "waitlist_count should return correct count" do
    @artist_profile.save
    assert_equal 0, @artist_profile.waitlist_count
    
    create(:waitlist_entry, artist_profile: @artist_profile)
    create(:waitlist_entry, artist_profile: @artist_profile, email: "<EMAIL>")
    
    assert_equal 2, @artist_profile.waitlist_count
  end

  test "pending_waitlist_count should return correct count" do
    @artist_profile.save
    pending_entry = create(:waitlist_entry, artist_profile: @artist_profile, status: :pending)
    contacted_entry = create(:waitlist_entry, artist_profile: @artist_profile, email: "<EMAIL>", status: :contacted)
    
    assert_equal 1, @artist_profile.pending_waitlist_count
  end
end
