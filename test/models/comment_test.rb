require "test_helper"

class CommentTest < ActiveSupport::TestCase
  def setup
    @comment = build(:comment)
  end

  test "should create comment with valid attributes" do
    assert @comment.valid?
    assert @comment.save
  end

  test "should belong to user" do
    assert_respond_to @comment, :user
    assert @comment.user.present?
  end

  test "should belong to post" do
    assert_respond_to @comment, :post
    assert @comment.post.present?
  end

  test "should require content" do
    @comment.content = nil
    assert_not @comment.valid?
    assert_includes @comment.errors[:content], "can't be blank"

    @comment.content = ""
    assert_not @comment.valid?
    assert_includes @comment.errors[:content], "can't be blank"
  end

  test "should require minimum content length" do
    @comment.content = ""
    assert_not @comment.valid?
    assert_includes @comment.errors[:content], "is too short (minimum is 1 character)"
  end

  test "should enforce maximum content length" do
    @comment.content = "x" * 1001
    assert_not @comment.valid?
    assert_includes @comment.errors[:content], "is too long (maximum is 1000 characters)"
  end

  test "should allow content at maximum length" do
    @comment.content = "x" * 1000
    assert @comment.valid?
  end

  test "should require user" do
    @comment.user = nil
    assert_not @comment.valid?
    assert_includes @comment.errors[:user], "must exist"
  end

  test "should require post" do
    @comment.post = nil
    assert_not @comment.valid?
    assert_includes @comment.errors[:post], "must exist"
  end

  test "should have recent scope" do
    old_comment = create(:comment, created_at: 2.hours.ago)
    new_comment = create(:comment, created_at: 1.hour.ago)
    
    recent = Comment.recent
    assert_equal [new_comment, old_comment], recent.to_a
  end

  test "should allow multiple comments from same user on same post" do
    @comment.save
    another_comment = build(:comment, user: @comment.user, post: @comment.post)
    
    assert another_comment.valid?
    assert another_comment.save
  end

  test "should be destroyed when user is destroyed" do
    @comment.save
    comment_id = @comment.id
    
    @comment.user.destroy
    assert_raises(ActiveRecord::RecordNotFound) do
      Comment.find(comment_id)
    end
  end

  test "should be destroyed when post is destroyed" do
    @comment.save
    comment_id = @comment.id
    
    @comment.post.destroy
    assert_raises(ActiveRecord::RecordNotFound) do
      Comment.find(comment_id)
    end
  end
end
