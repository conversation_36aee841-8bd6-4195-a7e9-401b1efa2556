require "test_helper"

class WaitlistEntryAttachmentTest < ActiveSupport::TestCase
  def setup
    @artist_profile = create(:artist_profile)
    @waitlist_entry = build(:waitlist_entry, artist_profile: @artist_profile)
  end

  test "should allow valid image attachment" do
    # Create a mock image file
    image_file = fixture_file_upload("test_image.jpg", "image/jpeg")
    @waitlist_entry.attachment.attach(image_file)
    
    assert @waitlist_entry.valid?
  end

  test "should reject non-image attachment" do
    # Create a mock text file
    text_file = fixture_file_upload("test_file.txt", "text/plain")
    @waitlist_entry.attachment.attach(text_file)
    
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:attachment], "must be an image file"
  end

  test "should reject oversized image" do
    # Mock a large file by stubbing the byte_size method
    large_image = fixture_file_upload("test_image.jpg", "image/jpeg")
    @waitlist_entry.attachment.attach(large_image)
    
    # Stub the byte_size method on the attached file
    @waitlist_entry.attachment.define_singleton_method(:byte_size) { 6.megabytes }
    
    assert_not @waitlist_entry.valid?
    assert_includes @waitlist_entry.errors[:attachment], "must be less than 5MB"
  end

  test "should accept image under size limit" do
    # Mock a small file
    small_image = fixture_file_upload("test_image.jpg", "image/jpeg")
    
    # Stub the byte_size to simulate a file under 5MB
    small_image.define_singleton_method(:byte_size) { 1.megabyte }
    
    @waitlist_entry.attachment.attach(small_image)
    
    assert @waitlist_entry.valid?
  end

  test "should accept exactly 5MB image" do
    image = fixture_file_upload("test_image.jpg", "image/jpeg")
    
    # Stub the byte_size to simulate exactly 5MB
    image.define_singleton_method(:byte_size) { 5.megabytes }
    
    @waitlist_entry.attachment.attach(image)
    
    assert @waitlist_entry.valid?
  end

  test "should be valid without attachment" do
    # No attachment should be valid
    assert @waitlist_entry.valid?
  end

  test "should accept various image formats" do
    valid_formats = [
      ["test_image.jpg", "image/jpeg"],
      ["test_image.png", "image/png"], 
      ["test_image.gif", "image/gif"],
      ["test_image.webp", "image/webp"]
    ]
    
    valid_formats.each do |filename, content_type|
      entry = build(:waitlist_entry, artist_profile: @artist_profile)
      image = fixture_file_upload(filename, content_type)
      image.define_singleton_method(:byte_size) { 1.megabyte }
      
      entry.attachment.attach(image)
      assert entry.valid?, "Should be valid for #{content_type}"
    end
  end

  test "should reject invalid image formats" do
    invalid_formats = [
      ["test_document.pdf", "application/pdf"],
      ["test_video.mp4", "video/mp4"],
      ["test_audio.mp3", "audio/mpeg"]
    ]
    
    invalid_formats.each do |filename, content_type|
      entry = build(:waitlist_entry, artist_profile: @artist_profile)
      file = fixture_file_upload(filename, content_type)
      
      entry.attachment.attach(file)
      assert_not entry.valid?, "Should be invalid for #{content_type}"
      assert_includes entry.errors[:attachment], "must be an image file"
    end
  end

  private

  def fixture_file_upload(filename, content_type)
    # Create a temporary file for testing
    temp_file = Tempfile.new([filename.split('.').first, ".#{filename.split('.').last}"])
    temp_file.write("fake file content")
    temp_file.rewind
    
    # Create an ActionDispatch::Http::UploadedFile
    ActionDispatch::Http::UploadedFile.new(
      tempfile: temp_file,
      filename: filename,
      type: content_type
    )
  end
end