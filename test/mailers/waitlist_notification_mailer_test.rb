require "test_helper"

class WaitlistNotificationMailerTest < ActionMailer::TestCase
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @waitlist_entry = create(:waitlist_entry, 
                            artist_profile: @artist_profile,
                            name: "<PERSON>",
                            email: "<EMAIL>")
  end

  test "new_entry should send email to artist user's email when no contact_email" do
    mail = WaitlistNotificationMailer.new_entry(@waitlist_entry)
    
    assert_equal [@artist_profile.contact_email.presence || @artist_user.email_address], mail.to
    assert_equal "New Waitlist Entry - John Doe", mail.subject
    assert_match "<PERSON>", mail.body.encoded
    assert_match "<EMAIL>", mail.body.encoded
    assert_match @artist_profile.name, mail.body.encoded
  end

  test "new_entry should send email to contact_email when available" do
    @artist_profile.update!(contact_email: "<EMAIL>")
    
    mail = WaitlistNotificationMailer.new_entry(@waitlist_entry)
    
    assert_equal ["<EMAIL>"], mail.to
    assert_equal "New Waitlist Entry - <PERSON>", mail.subject
  end

  test "new_entry should include waitlist entry details in body" do
    @waitlist_entry.update!(
      phone_number: "555-1234", 
      message: "I'd love to get a tattoo!"
    )
    
    mail = WaitlistNotificationMailer.new_entry(@waitlist_entry)
    
    assert_match "John Doe", mail.body.encoded
    assert_match "<EMAIL>", mail.body.encoded
    assert_match "555-1234", mail.body.encoded
    assert_match "I'd love to get a tattoo!", mail.body.encoded
  end

  test "new_entry should handle entry without phone and message" do
    mail = WaitlistNotificationMailer.new_entry(@waitlist_entry)
    
    assert_match "John Doe", mail.body.encoded
    assert_match "<EMAIL>", mail.body.encoded
    # Should not break when phone_number and message are nil
    assert_not_nil mail.body.encoded
  end

  test "new_entry should be delivered" do
    assert_emails 1 do
      WaitlistNotificationMailer.new_entry(@waitlist_entry).deliver_now
    end
  end

  test "new_entry should have both html and text parts" do
    mail = WaitlistNotificationMailer.new_entry(@waitlist_entry)
    
    assert mail.multipart?
    assert_equal 2, mail.parts.length
    
    html_part = mail.parts.find { |part| part.content_type.include?('text/html') }
    text_part = mail.parts.find { |part| part.content_type.include?('text/plain') }
    
    assert_not_nil html_part
    assert_not_nil text_part
    
    # Both parts should contain the key information
    assert_match "John Doe", html_part.body.encoded
    assert_match "John Doe", text_part.body.encoded
  end
end