require "test_helper"

class UserNotificationMailerTest < ActionMailer::TestCase
  test "welcome" do
    mail = UserNotificationMailer.welcome
    assert_equal "Welcome", mail.subject
    assert_equal [ "<EMAIL>" ], mail.to
    assert_equal [ "<EMAIL>" ], mail.from
    assert_match "Hi", mail.body.encoded
  end

  test "approval_granted" do
    mail = UserNotificationMailer.approval_granted
    assert_equal "Approval granted", mail.subject
    assert_equal [ "<EMAIL>" ], mail.to
    assert_equal [ "<EMAIL>" ], mail.from
    assert_match "Hi", mail.body.encoded
  end

  test "new_message" do
    mail = UserNotificationMailer.new_message
    assert_equal "New message", mail.subject
    assert_equal [ "<EMAIL>" ], mail.to
    assert_equal [ "<EMAIL>" ], mail.from
    assert_match "Hi", mail.body.encoded
  end
end
