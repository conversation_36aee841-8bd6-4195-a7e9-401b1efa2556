FactoryBot.define do
  factory :tattoo_item do
    association :client_profile
    artist_name { Faker::Name.name }
    caption { Faker::Lorem.paragraph(sentence_count: 2) }
    
    after(:build) do |tattoo_item|
      tattoo_item.image.attach(
        io: StringIO.new("fake image data"),
        filename: "tattoo.jpg",
        content_type: "image/jpeg"
      )
    end

    trait :with_linked_artist do
      association :artist_profile
      artist_name { nil }
    end

    trait :with_long_caption do
      caption { Faker::Lorem.paragraph(sentence_count: 8) }
    end

    trait :without_caption do
      caption { nil }
    end
  end
end
