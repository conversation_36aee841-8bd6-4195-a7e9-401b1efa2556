require 'factory_bot_rails'

FactoryBot.define do
  # User factory
  factory :user do
    sequence(:email_address) { |n| "user#{n}@example.com" }
    sequence(:username) { |n| "user#{n}" }
    password { "password123" }
    password_confirmation { "password123" }
    approved { false }
    admin { false }
    role { :client }

    trait :admin do
      admin { true }
    end

    trait :approved do
      approved { true }
    end

    trait :unapproved do
      approved { false }
    end

    trait :artist do
      role { :artist }
      after(:create) do |user|
        create(:artist_profile, user: user)
      end
    end

    trait :client do
      role { :client }
      after(:create) do |user|
        create(:client_profile, user: user)
      end
    end
  end

  # Artist Profile factory
  factory :artist_profile do
    association :user, factory: [:user, :artist, :approved]
    sequence(:name) { |n| "Artist #{n}" }
    biography { Faker::Lorem.paragraph(sentence_count: 3) }
    location { "#{Faker::Address.city}, #{Faker::Address.state_abbr}" }
    contact_email { Faker::Internet.email }
    instagram_url { "https://instagram.com/#{Faker::Internet.username}" }
    website_url { Faker::Internet.url }
    booking_link { Faker::Internet.url }
    messages_enabled { true }
    available { true }

    trait :unavailable do
      available { false }
    end

    trait :messages_disabled do
      messages_enabled { false }
    end

    trait :minimal do
      contact_email { nil }
      instagram_url { nil }
      website_url { nil }
      booking_link { nil }
    end
  end

  # Client Profile factory
  factory :client_profile do
    association :user, factory: [:user, :client, :approved]
    sequence(:name) { |n| "Client #{n}" }
    location { "#{Faker::Address.city}, #{Faker::Address.state_abbr}" }
  end

  # Specialty factory
  factory :specialty do
    sequence(:title) { |n| "Style #{n}" }
    description { Faker::Lorem.sentence }

    trait :traditional do
      title { "Traditional" }
      description { "Bold lines, limited color palette, classic imagery" }
    end

    trait :japanese do
      title { "Japanese" }
      description { "Traditional Japanese motifs and techniques" }
    end
  end

  # Artist Specialty factory (join table)
  factory :artist_specialty do
    association :artist_profile
    association :specialty
  end

  # Follow factory
  factory :follow do
    association :client_profile
    association :artist_profile
  end

  # Site Setting factory
  factory :site_setting do
    approval_required { true }

    trait :no_approval_required do
      approval_required { false }
    end
  end

  # Portfolio Item factory
  factory :portfolio_item do
    association :artist_profile
    caption { Faker::Lorem.sentence }
    position { 0 }

    after(:build) do |portfolio_item|
      portfolio_item.image.attach(
        io: StringIO.new("fake image data"),
        filename: "test.jpg",
        content_type: "image/jpeg"
      )
    end

    trait :with_position do |n|
      position { n }
    end
  end

  # Post factory
  factory :post do
    association :artist_profile
    caption { Faker::Lorem.paragraph }
    published_at { Time.current }
    post_type { :image }

    after(:build) do |post|
      post.image.attach(
        io: StringIO.new("fake image data"),
        filename: "test.jpg",
        content_type: "image/jpeg"
      )
    end

    trait :text_post do
      post_type { :text }
      body { Faker::Lorem.paragraph }
      caption { nil }
      
      after(:build) do |post|
        post.image.purge if post.image.attached?
      end
    end

    trait :unpublished do
      published_at { nil }
    end

    trait :without_caption do
      caption { nil }
    end
  end

  # Like factory
  factory :like do
    association :user, :approved
    association :post
  end

  # Comment factory  
  factory :comment do
    association :user, :approved
    association :post
    content { Faker::Lorem.paragraph }
  end

  # Inspiration Board factory
  factory :inspiration_board do
    association :user
    name { Faker::Lorem.words(number: 2).join(' ').titleize }
    privacy { true }

    trait :public do
      privacy { false }
    end

    trait :private do
      privacy { true }
    end

    trait :with_items do
      after(:create) do |board|
        3.times do
          create(:inspiration_board_item, inspiration_board: board)
        end
      end
    end
  end

  # Inspiration Board Item factory
  factory :inspiration_board_item do
    association :inspiration_board
    association :post
    notes { Faker::Lorem.sentence }

    trait :without_notes do
      notes { nil }
    end
  end
end