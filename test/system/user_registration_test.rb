require "application_system_test_case"

class UserRegistrationTest < ApplicationSystemTestCase
  def setup
    # Ensure approval is required by default
    SiteSetting.create!(approval_required: true)
  end
  test "client registration flow" do
    visit new_registration_path
    
    fill_in "Email address", with: "<EMAIL>"
    fill_in "Username", with: "testclient"
    fill_in "Password", with: "password123"
    fill_in "Password confirmation", with: "password123"
    fill_in "Full Name", with: "Test Client"
    fill_in "Location", with: "Test City, TS"
    choose "Client looking for tattoo artists"
    
    click_button "Create Account"
    
    # Check if user was created and redirected successfully
    assert_current_path root_path
    
    # Verify user was created with correct attributes
    user = User.find_by(email_address: "<EMAIL>")
    assert user.present?, "User should have been created"
    assert user.client?
    assert_not user.approved?
    assert_equal "testclient", user.username
    
    # Verify client profile was created
    assert user.client_profile.present?
    assert_equal "Test Client", user.client_profile.name
    assert_equal "Test City, TS", user.client_profile.location
  end

  test "artist registration flow" do
    visit new_registration_path
    
    fill_in "Email address", with: "<EMAIL>"
    fill_in "Username", with: "testartist"
    fill_in "Password", with: "password123"
    fill_in "Password confirmation", with: "password123"
    fill_in "Full Name", with: "Test Artist"
    fill_in "Location", with: "Test City, TS"
    choose "Tattoo artist"
    
    fill_in "Biography", with: "I create amazing tattoos"
    
    click_button "Create Account"
    
    # Check if user was created and redirected successfully
    assert_current_path root_path
    
    # Verify user was created with correct attributes
    user = User.find_by(email_address: "<EMAIL>")
    assert user.present?, "User should have been created"
    assert user.artist?
    assert_not user.approved?
    assert_equal "testartist", user.username
    
    # Verify artist profile was created
    assert user.artist_profile.present?
    assert_equal "Test Artist", user.artist_profile.name
    assert_equal "Test City, TS", user.artist_profile.location
    assert_equal "I create amazing tattoos", user.artist_profile.biography
  end

  test "registration with invalid data shows errors" do
    visit new_registration_path
    
    # Fill in a name but leave required fields blank
    fill_in "Full Name", with: "Test User"
    choose "Client looking for tattoo artists"
    
    # Try to submit with missing required fields
    page.execute_script("document.querySelectorAll('[required]').forEach(el => el.removeAttribute('required'))")
    click_button "Create Account"
    
    # Should stay on registration page and show errors
    assert_current_path registrations_path
    assert_text "Please fix the following errors:"
    assert_text "Email address can't be blank"
    assert_text "Username can't be blank"
    assert_text "Password can't be blank"
  end

  test "registration with duplicate email shows error" do
    existing_user = create(:user, email_address: "<EMAIL>")
    
    visit new_registration_path
    
    fill_in "Email address", with: "<EMAIL>"
    fill_in "Username", with: "newuser"
    fill_in "Password", with: "password123"
    fill_in "Password confirmation", with: "password123"
    fill_in "Full Name", with: "New User"
    choose "Client looking for tattoo artists"
    
    click_button "Create Account"
    
    assert_text "Email address has already been taken"
  end

  test "registration with duplicate username shows error" do
    existing_user = create(:user, username: "existinguser")
    
    visit new_registration_path
    
    fill_in "Email address", with: "<EMAIL>"
    fill_in "Username", with: "existinguser"
    fill_in "Password", with: "password123"
    fill_in "Password confirmation", with: "password123"
    fill_in "Full Name", with: "New User"
    choose "Client looking for tattoo artists"
    
    click_button "Create Account"
    
    assert_text "Username has already been taken"
  end

  test "registration with password mismatch shows error" do
    visit new_registration_path
    
    fill_in "Email address", with: "<EMAIL>"
    fill_in "Username", with: "testuser"
    fill_in "Password", with: "password123"
    fill_in "Password confirmation", with: "different123"
    fill_in "Full Name", with: "Test User"
    choose "Client looking for tattoo artists"
    
    click_button "Create Account"
    
    assert_text "Password confirmation doesn't match Password"
  end
end