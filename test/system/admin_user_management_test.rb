require "application_system_test_case"

class AdminUserManagementTest < ApplicationSystemTestCase
  def setup
    @admin_user = create(:user, :admin, :approved)
    @pending_artist = create(:user, :artist, approved: false)
    @pending_client = create(:user, :client, approved: false)
    @approved_artist = create(:user, :artist, :approved)
    @approved_client = create(:user, :client, :approved)
  end

  test "admin can view dashboard" do
    sign_in_as(@admin_user)
    
    visit admin_root_path
    
    assert_text "Admin Dashboard"
    assert_text "Users"
    assert_text "Posts"
    assert_text "Specialties"
    assert_text "Settings"
  end

  test "admin can view users list" do
    sign_in_as(@admin_user)
    
    visit admin_users_path
    
    assert_text "User Management"
    assert_text @pending_artist.username
    assert_text @pending_client.username
    assert_text @approved_artist.username
    assert_text @approved_client.username
    
    # Should show approval status
    assert_text "Pending"
    assert_text "Approved"
  end

  test "admin can approve pending artist" do
    sign_in_as(@admin_user)
    
    visit admin_users_path
    
    within "#user_#{@pending_artist.id}" do
      click_button "Approve"
    end
    
    assert_text "User approved successfully"
    
    # Verify user was approved
    @pending_artist.reload
    assert @pending_artist.approved?
    
    # Should show updated status
    within "#user_#{@pending_artist.id}" do
      assert_text "Approved"
      assert_button "Reject"
      assert_no_button "Approve"
    end
  end

  test "admin can reject approved user" do
    sign_in_as(@admin_user)
    
    visit admin_users_path
    
    within "#user_#{@approved_artist.id}" do
      click_button "Reject"
    end
    
    assert_text "User rejected successfully"
    
    # Verify user was rejected
    @approved_artist.reload
    assert_not @approved_artist.approved?
    
    # Should show updated status
    within "#user_#{@approved_artist.id}" do
      assert_text "Pending"
      assert_button "Approve"
      assert_no_button "Reject"
    end
  end

  test "admin can filter users by approval status" do
    sign_in_as(@admin_user)
    
    visit admin_users_path
    
    # Filter to show only pending users
    select "Pending", from: "Status"
    click_button "Filter"
    
    assert_text @pending_artist.username
    assert_text @pending_client.username
    assert_no_text @approved_artist.username
    assert_no_text @approved_client.username
    
    # Filter to show only approved users
    select "Approved", from: "Status"
    click_button "Filter"
    
    assert_no_text @pending_artist.username
    assert_no_text @pending_client.username
    assert_text @approved_artist.username
    assert_text @approved_client.username
  end

  test "admin can filter users by role" do
    sign_in_as(@admin_user)
    
    visit admin_users_path
    
    # Filter to show only artists
    select "Artist", from: "Role"
    click_button "Filter"
    
    assert_text @pending_artist.username
    assert_text @approved_artist.username
    assert_no_text @pending_client.username
    assert_no_text @approved_client.username
    
    # Filter to show only clients
    select "Client", from: "Role"
    click_button "Filter"
    
    assert_no_text @pending_artist.username
    assert_no_text @approved_artist.username
    assert_text @pending_client.username
    assert_text @approved_client.username
  end

  test "admin can view user details" do
    sign_in_as(@admin_user)
    
    visit admin_users_path
    
    within "#user_#{@approved_artist.id}" do
      click_link "View"
    end
    
    assert_text @approved_artist.username
    assert_text @approved_artist.email_address
    assert_text "Artist"
    assert_text "Approved"
    
    # Should show profile information if available
    if @approved_artist.artist_profile
      assert_text @approved_artist.artist_profile.name
      assert_text @approved_artist.artist_profile.location
    end
  end

  test "admin can manage posts" do
    post = create(:post, artist_profile: @approved_artist.artist_profile, caption: "Test post")
    
    sign_in_as(@admin_user)
    
    visit admin_posts_path
    
    assert_text "Post Management"
    assert_text "Test post"
    
    # Should be able to hide post
    within "#post_#{post.id}" do
      click_button "Hide"
    end
    
    assert_text "Post hidden successfully"
    
    # Should be able to unhide post
    within "#post_#{post.id}" do
      click_button "Unhide"
    end
    
    assert_text "Post unhidden successfully"
  end

  test "admin can manage specialties" do
    specialty = create(:specialty, title: "Traditional", description: "Bold lines and limited colors")
    
    sign_in_as(@admin_user)
    
    visit admin_specialties_path
    
    assert_text "Specialty Management"
    assert_text "Traditional"
    assert_text "Bold lines and limited colors"
    
    # Should be able to create new specialty
    click_link "New Specialty"
    
    fill_in "Title", with: "Realistic"
    fill_in "Description", with: "Photorealistic tattoo style"
    click_button "Create Specialty"
    
    assert_text "Specialty was successfully created"
    assert_text "Realistic"
    assert_text "Photorealistic tattoo style"
  end

  test "admin can edit specialty" do
    specialty = create(:specialty, title: "Traditional", description: "Bold lines and limited colors")
    
    sign_in_as(@admin_user)
    
    visit admin_specialties_path
    
    within "#specialty_#{specialty.id}" do
      click_link "Edit"
    end
    
    fill_in "Title", with: "Neo-Traditional"
    fill_in "Description", with: "Modern take on traditional tattooing"
    click_button "Update Specialty"
    
    assert_text "Specialty was successfully updated"
    assert_text "Neo-Traditional"
    assert_text "Modern take on traditional tattooing"
  end

  test "admin can delete specialty" do
    specialty = create(:specialty, title: "To Delete")
    
    sign_in_as(@admin_user)
    
    visit admin_specialties_path
    
    within "#specialty_#{specialty.id}" do
      accept_confirm do
        click_link "Delete"
      end
    end
    
    assert_text "Specialty was successfully deleted"
    assert_no_text "To Delete"
    
    # Verify specialty was deleted
    assert_not Specialty.exists?(specialty.id)
  end

  test "admin can view and update site settings" do
    create(:site_setting, approval_required: true)
    
    sign_in_as(@admin_user)
    
    visit admin_settings_path
    
    assert_text "Site Settings"
    assert_checked_field "Approval required"
    
    # Update setting
    uncheck "Approval required"
    click_button "Update Settings"
    
    assert_text "Settings updated successfully"
    assert_unchecked_field "Approval required"
    
    # Verify setting was updated
    site_setting = SiteSetting.first
    assert_not site_setting.approval_required?
  end

  test "non-admin user cannot access admin area" do
    regular_user = create(:user, :client, :approved)
    
    sign_in_as(regular_user)
    
    visit admin_root_path
    
    assert_text "Access denied"
    assert_current_path root_path
  end

  test "unauthenticated user cannot access admin area" do
    visit admin_root_path
    
    assert_text "Please sign in"
    assert_current_path new_session_path
  end

  test "admin dashboard shows statistics" do
    # Create some data for statistics
    3.times { create(:user, :artist, approved: false) }
    2.times { create(:user, :client, :approved) }
    5.times { create(:post, artist_profile: @approved_artist.artist_profile) }
    
    sign_in_as(@admin_user)
    
    visit admin_root_path
    
    # Should show counts
    assert_text "Pending Users"
    assert_text "Total Posts"
    assert_text "Total Artists"
    assert_text "Total Clients"
  end

  private

  def sign_in_as(user)
    visit new_session_path
    fill_in "Email address", with: user.email_address
    fill_in "Password", with: "password123"
    click_button "Sign in"
  end
end