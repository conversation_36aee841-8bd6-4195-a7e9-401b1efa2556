require "application_system_test_case"

class PostCreationAndInteractionTest < ApplicationSystemTestCase
  def setup
    @artist_user = create(:user, :artist, :approved)
    @client_user = create(:user, :client, :approved)
    @post = create(:post, artist_profile: @artist_user.artist_profile, caption: "Amazing tattoo work")
  end

  test "artist can create a new post" do
    sign_in_as(@artist_user)
    
    visit new_post_path
    
    fill_in "Caption", with: "Check out this new piece I just finished!"
    attach_file "Image", Rails.root.join("test", "fixtures", "files", "test_image.jpg")
    
    click_button "Create Post"
    
    assert_text "Post was successfully created"
    assert_text "Check out this new piece I just finished!"
    
    # Verify post was created
    post = Post.last
    assert_equal "Check out this new piece I just finished!", post.caption
    assert_equal @artist_user.artist_profile, post.artist_profile
  end

  test "artist can add post to portfolio during creation" do
    sign_in_as(@artist_user)
    
    visit new_post_path
    
    fill_in "Caption", with: "Portfolio piece"
    attach_file "Image", Rails.root.join("test", "fixtures", "files", "test_image.jpg")
    check "Add to portfolio"
    
    click_button "Create Post"
    
    assert_text "Post was successfully created"
    
    # Verify post was added to portfolio
    post = Post.last
    assert post.in_portfolio?
  end

  test "user can like a post" do
    sign_in_as(@client_user)
    
    visit post_path(@post)
    
    click_button "Like"
    
    assert_text "1 like"
    
    # Verify like was created
    assert_equal 1, @post.reload.likes_count
    assert Like.exists?(user: @client_user, post: @post)
  end

  test "user can unlike a post" do
    # Create existing like
    like = create(:like, user: @client_user, post: @post)
    @post.update(likes_count: 1)
    
    sign_in_as(@client_user)
    
    visit post_path(@post)
    
    click_button "Unlike"
    
    assert_text "0 likes"
    
    # Verify like was removed
    assert_equal 0, @post.reload.likes_count
    assert_not Like.exists?(user: @client_user, post: @post)
  end

  test "user can comment on a post" do
    sign_in_as(@client_user)
    
    visit post_path(@post)
    
    fill_in "Add a comment...", with: "This is absolutely stunning work!"
    click_button "Post Comment"
    
    assert_text "This is absolutely stunning work!"
    
    # Verify comment was created
    comment = Comment.last
    assert_equal "This is absolutely stunning work!", comment.content
    assert_equal @client_user, comment.user
    assert_equal @post, comment.post
  end

  test "user can edit their own comment" do
    comment = create(:comment, user: @client_user, post: @post, content: "Original comment")
    
    sign_in_as(@client_user)
    
    visit post_path(@post)
    
    within "#comment_#{comment.id}" do
      click_link "Edit"
      fill_in "Content", with: "Updated comment text"
      click_button "Update Comment"
    end
    
    assert_text "Updated comment text"
    assert_no_text "Original comment"
    
    # Verify comment was updated
    assert_equal "Updated comment text", comment.reload.content
  end

  test "user can delete their own comment" do
    comment = create(:comment, user: @client_user, post: @post, content: "Comment to delete")
    
    sign_in_as(@client_user)
    
    visit post_path(@post)
    
    within "#comment_#{comment.id}" do
      accept_confirm do
        click_link "Delete"
      end
    end
    
    assert_no_text "Comment to delete"
    
    # Verify comment was deleted
    assert_not Comment.exists?(comment.id)
  end

  test "artist can edit their own post" do
    sign_in_as(@artist_user)
    
    visit post_path(@post)
    
    click_link "Edit Post"
    
    fill_in "Caption", with: "Updated caption for this amazing piece"
    click_button "Update Post"
    
    assert_text "Post was successfully updated"
    assert_text "Updated caption for this amazing piece"
    
    # Verify post was updated
    assert_equal "Updated caption for this amazing piece", @post.reload.caption
  end

  test "artist can delete their own post" do
    sign_in_as(@artist_user)
    
    visit post_path(@post)
    
    accept_confirm do
      click_link "Delete Post"
    end
    
    assert_text "Post was successfully deleted"
    assert_current_path root_path
    
    # Verify post was deleted
    assert_not Post.exists?(@post.id)
  end

  test "post displays engagement statistics" do
    # Create some engagement
    3.times { create(:like, post: @post) }
    2.times { create(:comment, post: @post) }
    @post.update(likes_count: 3, comments_count: 2)
    
    visit post_path(@post)
    
    assert_text "3 likes"
    assert_text "2 comments"
  end

  test "unauthenticated user can view post but cannot interact" do
    visit post_path(@post)
    
    assert_text @post.caption
    assert_no_button "Like"
    assert_no_field "Add a comment..."
    assert_no_link "Edit Post"
    assert_no_link "Delete Post"
  end

  test "user cannot edit or delete other users' posts" do
    other_artist = create(:user, :artist, :approved)
    other_post = create(:post, artist_profile: other_artist.artist_profile)
    
    sign_in_as(@client_user)
    
    visit post_path(other_post)
    
    assert_no_link "Edit Post"
    assert_no_link "Delete Post"
  end

  test "user cannot edit or delete other users' comments" do
    other_user = create(:user, :client, :approved)
    other_comment = create(:comment, user: other_user, post: @post, content: "Other user's comment")
    
    sign_in_as(@client_user)
    
    visit post_path(@post)
    
    within "#comment_#{other_comment.id}" do
      assert_no_link "Edit"
      assert_no_link "Delete"
    end
  end

  private

  def sign_in_as(user)
    visit new_session_path
    fill_in "Email address", with: user.email_address
    fill_in "Password", with: "password123"
    click_button "Sign in"
  end
end