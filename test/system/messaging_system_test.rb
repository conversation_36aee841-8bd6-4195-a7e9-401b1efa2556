require "application_system_test_case"

class MessagingSystemTest < ApplicationSystemTestCase
  def setup
    @artist_user = create(:user, :artist, :approved)
    @client_user = create(:user, :client, :approved)
    @artist_profile = @artist_user.artist_profile
    @client_profile = @client_user.client_profile
  end

  test "client can start a new conversation with artist" do
    sign_in_as(@client_user)
    
    visit artist_path(@artist_profile.slug)
    
    click_button "Send Message"
    
    fill_in "Message", with: "Hi! I'm interested in getting a tattoo. Are you available?"
    click_button "Send Message"
    
    assert_text "Message sent successfully"
    assert_text "Hi! I'm interested in getting a tattoo. Are you available?"
    
    # Verify conversation and message were created
    conversation = Conversation.last
    assert_equal @client_user, conversation.user1
    assert_equal @artist_user, conversation.user2
    
    message = Message.last
    assert_equal @client_user, message.sender
    assert_equal "Hi! I'm interested in getting a tattoo. Are you available?", message.content
  end

  test "artist can reply to a conversation" do
    conversation = create(:conversation, user1: @client_user, user2: @artist_user)
    initial_message = create(:message, conversation: conversation, sender: @client_user, content: "Initial message")
    
    sign_in_as(@artist_user)
    
    visit conversation_path(conversation)
    
    fill_in "Message", with: "Thanks for reaching out! Yes, I have availability next month."
    click_button "Send Message"
    
    assert_text "Thanks for reaching out! Yes, I have availability next month."
    
    # Verify reply message was created
    reply_message = Message.last
    assert_equal @artist_user, reply_message.sender
    assert_equal "Thanks for reaching out! Yes, I have availability next month.", reply_message.content
  end

  test "user can view their conversations list" do
    conversation1 = create(:conversation, user1: @client_user, user2: @artist_user)
    conversation2 = create(:conversation, user1: @client_user, user2: create(:user, :artist, :approved))
    
    # Create messages to set last_message_at
    create(:message, conversation: conversation1, sender: @client_user, content: "First conversation")
    create(:message, conversation: conversation2, sender: @client_user, content: "Second conversation")
    
    sign_in_as(@client_user)
    
    visit conversations_path
    
    assert_text "Conversations"
    assert_text @artist_user.display_name
    assert_text "First conversation"
    
    # Should show most recent conversations first
    page_text = page.text
    first_pos = page_text.index("First conversation")
    second_pos = page_text.index("Second conversation")
    assert first_pos < second_pos, "Conversations should be ordered by most recent"
  end

  test "messages are marked as read when viewed" do
    conversation = create(:conversation, user1: @client_user, user2: @artist_user)
    message1 = create(:message, conversation: conversation, sender: @artist_user, content: "Unread message 1")
    message2 = create(:message, conversation: conversation, sender: @artist_user, content: "Unread message 2")
    
    sign_in_as(@client_user)
    
    # Should show unread count before viewing
    visit conversations_path
    assert_text "2 unread"
    
    # View the conversation
    visit conversation_path(conversation)
    
    # Messages should be marked as read
    message1.reload
    message2.reload
    assert message1.read_at.present?
    assert message2.read_at.present?
    
    # Return to conversations list - should no longer show unread count
    visit conversations_path
    assert_no_text "unread"
  end

  test "user cannot message artist with messages disabled" do
    @artist_profile.update(messages_enabled: false)
    
    sign_in_as(@client_user)
    
    visit artist_path(@artist_profile.slug)
    
    assert_no_button "Send Message"
    assert_text "Messages are currently disabled"
  end

  test "user cannot message blocked artist" do
    create(:block, blocker: @artist_user, blocked: @client_user)
    
    sign_in_as(@client_user)
    
    visit artist_path(@artist_profile.slug)
    
    assert_no_button "Send Message"
    assert_text "Unable to send messages"
  end

  test "blocked user cannot see conversations or send messages" do
    conversation = create(:conversation, user1: @client_user, user2: @artist_user)
    create(:message, conversation: conversation, sender: @client_user, content: "Before block")
    
    # Block the client
    create(:block, blocker: @artist_user, blocked: @client_user)
    
    sign_in_as(@client_user)
    
    # Should not see the conversation in list
    visit conversations_path
    assert_no_text "Before block"
    
    # Should not be able to access conversation directly
    visit conversation_path(conversation)
    assert_text "Access denied"
  end

  test "conversation shows both users' messages in chronological order" do
    conversation = create(:conversation, user1: @client_user, user2: @artist_user)
    
    msg1 = create(:message, conversation: conversation, sender: @client_user, content: "First message", created_at: 1.hour.ago)
    msg2 = create(:message, conversation: conversation, sender: @artist_user, content: "Second message", created_at: 30.minutes.ago)
    msg3 = create(:message, conversation: conversation, sender: @client_user, content: "Third message", created_at: 15.minutes.ago)
    
    sign_in_as(@client_user)
    
    visit conversation_path(conversation)
    
    assert_text "First message"
    assert_text "Second message"
    assert_text "Third message"
    
    # Check chronological order
    page_text = page.text
    first_pos = page_text.index("First message")
    second_pos = page_text.index("Second message")
    third_pos = page_text.index("Third message")
    
    assert first_pos < second_pos, "Messages should be in chronological order"
    assert second_pos < third_pos, "Messages should be in chronological order"
  end

  test "empty message cannot be sent" do
    conversation = create(:conversation, user1: @client_user, user2: @artist_user)
    
    sign_in_as(@client_user)
    
    visit conversation_path(conversation)
    
    # Try to send empty message
    fill_in "Message", with: ""
    click_button "Send Message"
    
    assert_text "Message can't be blank"
  end

  test "long message is accepted" do
    conversation = create(:conversation, user1: @client_user, user2: @artist_user)
    long_message = "A" * 1000 # 1000 character message
    
    sign_in_as(@client_user)
    
    visit conversation_path(conversation)
    
    fill_in "Message", with: long_message
    click_button "Send Message"
    
    assert_text long_message[0..50] # Should show at least the beginning
    
    # Verify message was saved
    message = Message.last
    assert_equal long_message, message.content
  end

  test "user sees their own name and avatar in messages" do
    conversation = create(:conversation, user1: @client_user, user2: @artist_user)
    create(:message, conversation: conversation, sender: @client_user, content: "My message")
    create(:message, conversation: conversation, sender: @artist_user, content: "Their message")
    
    sign_in_as(@client_user)
    
    visit conversation_path(conversation)
    
    # Should show sender names/avatars appropriately
    assert_text @client_user.display_name
    assert_text @artist_user.display_name
  end

  private

  def sign_in_as(user)
    visit new_session_path
    fill_in "Email address", with: user.email_address
    fill_in "Password", with: "password123"
    click_button "Sign in"
  end
end