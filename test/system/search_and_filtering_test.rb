require "application_system_test_case"

class SearchAndFilteringTest < ApplicationSystemTestCase
  def setup
    @client_user = create(:user, :client, :approved)
    
    # Create test artists with different specialties and locations
    @traditional_artist = create(:user, :artist, :approved)
    @traditional_artist.artist_profile.update(
      name: "Traditional Tom",
      location: "New York, NY",
      biography: "Specializing in traditional American tattoos"
    )
    
    @japanese_artist = create(:user, :artist, :approved)
    @japanese_artist.artist_profile.update(
      name: "Japanese Jane",
      location: "Los Angeles, CA",
      biography: "Expert in Japanese traditional tattoos"
    )
    
    @realistic_artist = create(:user, :artist, :approved)
    @realistic_artist.artist_profile.update(
      name: "Realistic Rick",
      location: "Chicago, IL",
      biography: "Photorealistic portraits and tattoos"
    )
    
    # Create specialties
    @traditional_specialty = create(:specialty, title: "Traditional", description: "Bold lines and limited colors")
    @japanese_specialty = create(:specialty, title: "Japanese", description: "Traditional Japanese motifs")
    @realistic_specialty = create(:specialty, title: "Realistic", description: "Photorealistic artwork")
    
    # Associate artists with specialties
    create(:artist_specialty, artist_profile: @traditional_artist.artist_profile, specialty: @traditional_specialty)
    create(:artist_specialty, artist_profile: @japanese_artist.artist_profile, specialty: @japanese_specialty)
    create(:artist_specialty, artist_profile: @realistic_artist.artist_profile, specialty: @realistic_specialty)
    
    # Create some posts
    @traditional_post = create(:post, artist_profile: @traditional_artist.artist_profile, caption: "Traditional eagle tattoo")
    @japanese_post = create(:post, artist_profile: @japanese_artist.artist_profile, caption: "Dragon sleeve tattoo")
    @realistic_post = create(:post, artist_profile: @realistic_artist.artist_profile, caption: "Portrait tattoo")
  end

  test "user can search artists by name" do
    visit artists_path
    
    fill_in "Search", with: "Traditional Tom"
    click_button "Search"
    
    assert_text "Traditional Tom"
    assert_no_text "Japanese Jane"
    assert_no_text "Realistic Rick"
  end

  test "user can search artists by location" do
    visit artists_path
    
    fill_in "Search", with: "New York"
    click_button "Search"
    
    assert_text "Traditional Tom"
    assert_text "New York, NY"
    assert_no_text "Japanese Jane"
    assert_no_text "Realistic Rick"
  end

  test "user can search artists by biography content" do
    visit artists_path
    
    fill_in "Search", with: "traditional"
    click_button "Search"
    
    assert_text "Traditional Tom"
    assert_text "Japanese Jane"
    assert_no_text "Realistic Rick"
  end

  test "user can filter artists by specialty" do
    visit artists_path
    
    select "Traditional", from: "Specialty"
    click_button "Filter"
    
    assert_text "Traditional Tom"
    assert_no_text "Japanese Jane"
    assert_no_text "Realistic Rick"
  end

  test "user can filter artists by location" do
    visit artists_path
    
    select "California", from: "State"
    click_button "Filter"
    
    assert_text "Japanese Jane"
    assert_text "Los Angeles, CA"
    assert_no_text "Traditional Tom"
    assert_no_text "Realistic Rick"
  end

  test "user can combine search and filters" do
    visit artists_path
    
    fill_in "Search", with: "tattoo"
    select "Japanese", from: "Specialty"
    click_button "Search"
    
    assert_text "Japanese Jane"
    assert_no_text "Traditional Tom"
    assert_no_text "Realistic Rick"
  end

  test "user can filter artists by availability" do
    @traditional_artist.artist_profile.update(available: false)
    
    visit artists_path
    
    check "Available only"
    click_button "Filter"
    
    assert_text "Japanese Jane"
    assert_text "Realistic Rick"
    assert_no_text "Traditional Tom"
  end

  test "user can filter artists with messages enabled" do
    @traditional_artist.artist_profile.update(messages_enabled: false)
    
    visit artists_path
    
    check "Messages enabled"
    click_button "Filter"
    
    assert_text "Japanese Jane"
    assert_text "Realistic Rick"
    assert_no_text "Traditional Tom"
  end

  test "search shows no results message when no artists match" do
    visit artists_path
    
    fill_in "Search", with: "nonexistent artist"
    click_button "Search"
    
    assert_text "No artists found matching your criteria"
    assert_no_text "Traditional Tom"
    assert_no_text "Japanese Jane"
    assert_no_text "Realistic Rick"
  end

  test "user can clear search and filters" do
    visit artists_path
    
    fill_in "Search", with: "Traditional"
    select "Traditional", from: "Specialty"
    click_button "Search"
    
    # Should show filtered results
    assert_text "Traditional Tom"
    assert_no_text "Japanese Jane"
    
    # Clear filters
    click_link "Clear All"
    
    # Should show all artists again
    assert_text "Traditional Tom"
    assert_text "Japanese Jane"
    assert_text "Realistic Rick"
  end

  test "user can browse discovery page" do
    visit discover_path
    
    assert_text "Discover Artists"
    assert_text "Traditional Tom"
    assert_text "Japanese Jane"
    assert_text "Realistic Rick"
    
    # Should show recent posts
    assert_text "Traditional eagle tattoo"
    assert_text "Dragon sleeve tattoo"
    assert_text "Portrait tattoo"
  end

  test "discovery page can be filtered by specialty" do
    visit discover_path
    
    select "Japanese", from: "Specialty"
    click_button "Filter"
    
    assert_text "Japanese Jane"
    assert_text "Dragon sleeve tattoo"
    assert_no_text "Traditional Tom"
    assert_no_text "Traditional eagle tattoo"
  end

  test "user can search posts by caption" do
    visit root_path
    
    fill_in "Search posts", with: "eagle"
    click_button "Search"
    
    assert_text "Traditional eagle tattoo"
    assert_no_text "Dragon sleeve tattoo"
    assert_no_text "Portrait tattoo"
  end

  test "search results are paginated" do
    # Create many artists to test pagination
    25.times do |i|
      artist = create(:user, :artist, :approved)
      artist.artist_profile.update(name: "Artist #{i}")
    end
    
    visit artists_path
    
    # Should show pagination controls
    assert_selector ".pagination" 
    assert_link "Next"
    
    # Should show limited number per page
    artists_on_page = page.all(".artist-card").count
    assert artists_on_page <= 20
  end

  test "user can sort artists by different criteria" do
    # Update creation times to test sorting
    @traditional_artist.artist_profile.user.update(created_at: 3.days.ago)
    @japanese_artist.artist_profile.user.update(created_at: 1.day.ago)
    @realistic_artist.artist_profile.user.update(created_at: 2.days.ago)
    
    visit artists_path
    
    # Sort by newest first
    select "Newest", from: "Sort by"
    click_button "Sort"
    
    artist_names = page.all(".artist-name").map(&:text)
    assert_equal "Japanese Jane", artist_names.first
    
    # Sort by oldest first
    select "Oldest", from: "Sort by"
    click_button "Sort"
    
    artist_names = page.all(".artist-name").map(&:text)
    assert_equal "Traditional Tom", artist_names.first
  end

  test "search preserves filters when navigating" do
    visit artists_path
    
    fill_in "Search", with: "Traditional"
    select "Traditional", from: "Specialty"
    click_button "Search"
    
    # Click on artist profile
    click_link "Traditional Tom"
    
    # Go back using browser back button
    page.go_back
    
    # Search and filters should be preserved
    assert_field "Search", with: "Traditional"
    assert_field "Specialty", with: "Traditional"
  end

  test "mobile responsive search and filters work" do
    # Simulate mobile viewport
    page.driver.resize_window(375, 667)
    
    visit artists_path
    
    # Should be able to use search on mobile
    fill_in "Search", with: "Traditional"
    click_button "Search"
    
    assert_text "Traditional Tom"
    assert_no_text "Japanese Jane"
    
    # Filters should be accessible (possibly in mobile menu)
    if page.has_selector?(".mobile-filter-toggle")
      click_button "Filters"
    end
    
    select "Traditional", from: "Specialty"
    click_button "Apply Filters"
    
    assert_text "Traditional Tom"
  end

  private

  def sign_in_as(user)
    visit new_session_path
    fill_in "Email address", with: user.email_address
    fill_in "Password", with: "password123"
    click_button "Sign in"
  end
end