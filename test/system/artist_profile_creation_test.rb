require "application_system_test_case"

class ArtistProfileCreationTest < ApplicationSystemTestCase
  def setup
    @artist_user = create(:user, :artist, :approved)
    @specialty = create(:specialty, :traditional)
  end

  test "artist can edit their profile" do
    sign_in_as(@artist_user)
    
    visit edit_artist_path(@artist_user.artist_profile.slug)
    
    fill_in "Name", with: "Updated Artist Name"
    fill_in "Biography", with: "This is my updated artist biography with detailed information about my work."
    fill_in "Location", with: "Updated City, ST"
    fill_in "Contact email", with: "<EMAIL>"
    fill_in "Instagram URL", with: "https://instagram.com/updatedartist"
    fill_in "Website URL", with: "https://updatedartist.com"
    fill_in "Booking link", with: "https://booking.com/updatedartist"
    
    check "Available for new work"
    check "Enable messages"
    
    click_button "Update Profile"
    
    assert_text "Profile updated successfully"
    
    # Verify updates were saved
    @artist_user.artist_profile.reload
    assert_equal "Updated Artist Name", @artist_user.artist_profile.name
    assert_equal "This is my updated artist biography with detailed information about my work.", @artist_user.artist_profile.biography
    assert_equal "Updated City, ST", @artist_user.artist_profile.location
    assert_equal "<EMAIL>", @artist_user.artist_profile.contact_email
    assert @artist_user.artist_profile.available?
    assert @artist_user.artist_profile.messages_enabled?
  end

  test "artist profile displays correctly" do
    artist_profile = create(:artist_profile, 
      name: "Test Artist",
      biography: "I create amazing tattoos",
      location: "Test City, TS",
      contact_email: "<EMAIL>",
      instagram_url: "https://instagram.com/testartist",
      website_url: "https://testartist.com"
    )
    
    visit artist_path(artist_profile.slug)
    
    assert_text "Test Artist"
    assert_text "I create amazing tattoos"
    assert_text "Test City, TS"
    assert_text "<EMAIL>"
    
    # Check for social links presence
    assert_selector "a[href='https://instagram.com/testartist']"
    assert_selector "a[href='https://testartist.com']"
  end

  test "artist portfolio section" do
    artist_profile = create(:artist_profile)
    portfolio_post = create(:post, :in_portfolio, artist_profile: artist_profile)
    
    visit portfolio_artist_path(artist_profile.slug)
    
    assert_text "Portfolio"
    assert_selector "img" # Portfolio image should be present
  end

  test "artist posts section" do
    artist_profile = create(:artist_profile)
    post = create(:post, artist_profile: artist_profile, caption: "Latest tattoo work")
    
    visit posts_artist_path(artist_profile.slug)
    
    assert_text "Recent Posts"
    assert_text "Latest tattoo work"
    assert_selector "img" # Post image should be present
  end

  test "artist inspiration boards section" do
    artist_profile = create(:artist_profile)
    inspiration_board = create(:inspiration_board, :public, user: artist_profile.user, name: "My Inspiration")
    
    visit inspiration_artist_path(artist_profile.slug)
    
    assert_text "Inspiration Boards"
    assert_text "My Inspiration"
  end

  test "unapproved artist profile shows pending message" do
    unapproved_artist = create(:user, :artist, approved: false)
    
    visit artist_path(unapproved_artist.artist_profile.slug)
    
    assert_text "This profile is pending approval"
  end

  test "artist with messages disabled shows appropriate message" do
    artist_profile = create(:artist_profile, :messages_disabled)
    client_user = create(:user, :client, :approved)
    
    sign_in_as(client_user)
    visit artist_path(artist_profile.slug)
    
    assert_text "Messages are currently disabled"
    assert_no_button "Send Message"
  end

  test "artist with messages enabled shows message button to clients" do
    artist_profile = create(:artist_profile, messages_enabled: true)
    client_user = create(:user, :client, :approved)
    
    sign_in_as(client_user)
    visit artist_path(artist_profile.slug)
    
    assert_button "Send Message"
  end

  test "unavailable artist shows unavailable status" do
    artist_profile = create(:artist_profile, :unavailable)
    
    visit artist_path(artist_profile.slug)
    
    assert_text "Currently unavailable for new work"
  end

  private

  def sign_in_as(user)
    visit new_session_path
    fill_in "Email address", with: user.email_address
    fill_in "Password", with: "password123"
    click_button "Sign in"
  end
end