require "test_helper"

class InteractionFlowTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @client_user = create(:user, :client, :approved)
    @other_client = create(:user, :client, :approved)
    @post = create(:post, artist_profile: @artist_profile)
  end

  test "complete comment flow" do
    sign_in_as(@client_user)
    
    # Visit post - should show 0 comments initially
    get post_path(@post)
    assert_response :success
    assert_select "span", text: "0 comments"
    assert_select "form textarea[name='comment[content]']"
    
    # Add a comment
    assert_difference '@post.comments.count', 1 do
      post post_comments_path(@post), params: {
        comment: {
          content: "Amazing work! Love the shading technique."
        }
      }
    end
    
    assert_redirected_to post_path(@post)
    follow_redirect!
    
    # Verify comment appears
    comment = Comment.last
    assert_equal @client_user, comment.user
    assert_equal @post, comment.post
    assert_equal "Amazing work! Love the shading technique.", comment.content
    
    assert_select "span", text: "1 comment"
    assert_select "div", text: /Amazing work! Love the shading/
    assert_select "h4", text: @client_user.display_name
    
    # Edit the comment
    get edit_post_comment_path(@post, comment)
    assert_response :success
    assert_select "form textarea[name='comment[content]']"
    
    patch post_comment_path(@post, comment), params: {
      comment: {
        content: "Updated: Amazing work! The shading technique is incredible."
      }
    }
    
    assert_redirected_to post_path(@post)
    follow_redirect!
    
    comment.reload
    assert_equal "Updated: Amazing work! The shading technique is incredible.", comment.content
    assert_select "div", text: /Updated: Amazing work! The shading technique is incredible/
    
    # Delete the comment
    assert_difference '@post.comments.count', -1 do
      delete post_comment_path(@post, comment)
    end
    
    assert_redirected_to post_path(@post)
    follow_redirect!
    
    assert_select "span", text: "0 comments"
    assert_select "p", text: "No comments yet. Be the first to comment!"
  end

  test "comment access control" do
    comment = create(:comment, post: @post, user: @client_user)
    
    # Other users cannot edit this comment
    sign_in_as(@other_client)
    
    get edit_post_comment_path(@post, comment)
    assert_redirected_to post_path(@post)
    assert_equal "Access denied.", flash[:alert]
    
    # Other users cannot delete this comment
    delete post_comment_path(@post, comment)
    assert_redirected_to post_path(@post)
    assert_equal "Access denied.", flash[:alert]
    
    # Original commenter can edit
    sign_in_as(@client_user)
    
    get edit_post_comment_path(@post, comment)
    assert_response :success
    
    # Admin can edit any comment
    admin_user = create(:user, :client, :approved, admin: true)
    sign_in_as(admin_user)
    
    get edit_post_comment_path(@post, comment)
    assert_response :success
    
    # Admin can delete any comment
    assert_difference 'Comment.count', -1 do
      delete post_comment_path(@post, comment)
    end
  end

  test "multiple comments from different users" do
    # Artist comments on their own post
    sign_in_as(@artist_user)
    
    post post_comments_path(@post), params: {
      comment: { content: "Thanks for all the positive feedback!" }
    }
    
    artist_comment = Comment.last
    
    # Client comments
    sign_in_as(@client_user)
    
    post post_comments_path(@post), params: {
      comment: { content: "Beautiful work! How long did this take?" }
    }
    
    client_comment = Comment.last
    
    # Another client comments
    sign_in_as(@other_client)
    
    post post_comments_path(@post), params: {
      comment: { content: "Incredible detail work!" }
    }
    
    # Visit post and verify all comments appear
    get post_path(@post)
    assert_response :success
    
    assert_select "span", text: "3 comments"
    assert_select "div", text: /Thanks for all the positive feedback/
    assert_select "div", text: /Beautiful work! How long did this take/
    assert_select "div", text: /Incredible detail work/
    
    # Comments should appear in chronological order
    comments = @post.comments.order(:created_at)
    assert_equal artist_comment, comments.first
    assert_equal client_comment, comments.second
    assert_equal Comment.last, comments.third
  end

  test "unauthenticated and unapproved user restrictions" do
    # Unauthenticated users can view but not interact
    get post_path(@post)
    assert_response :success
    
    # No comment form visible
    assert_select "form textarea[name='comment[content]']", count: 0
    
    
    # Cannot comment
    post post_comments_path(@post), params: {
      comment: { content: "Test comment" }
    }
    assert_redirected_to new_session_path
    
    # Unapproved users are redirected
    unapproved_user = create(:user, :client, approved: false)
    sign_in_as(unapproved_user)
    
    post post_comments_path(@post), params: {
      comment: { content: "Test comment" }
    }
    assert_redirected_to root_path
  end

  test "comment validation errors" do
    sign_in_as(@client_user)
    
    # Try to create empty comment
    assert_no_difference 'Comment.count' do
      post post_comments_path(@post), params: {
        comment: { content: "" }
      }
    end
    
    assert_redirected_to post_path(@post)
    assert_equal "Unable to create comment.", flash[:alert]
    
    # Try to update comment with empty content
    comment = create(:comment, post: @post, user: @client_user)
    
    patch post_comment_path(@post, comment), params: {
      comment: { content: "" }
    }
    
    assert_response :unprocessable_entity
    comment.reload
    assert_not_equal "", comment.content  # Should not have been updated
  end

  test "comment counts update correctly" do
    sign_in_as(@client_user)
    
    # Initial state
    get post_path(@post)
    assert_select "span", text: "0 comments"
    
    # Add comment
    post post_comments_path(@post), params: {
      comment: { content: "Great work!" }
    }
    follow_redirect!
    assert_select "span", text: "1 comment"
    
    # Add second user comment
    sign_in_as(@other_client)
    
    post post_comments_path(@post), params: {
      comment: { content: "Amazing!" }
    }
    
    # Check final counts
    get post_path(@post)
    assert_select "span", text: "2 comments"
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end