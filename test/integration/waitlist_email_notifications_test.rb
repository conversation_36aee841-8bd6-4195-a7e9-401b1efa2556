require "test_helper"

class WaitlistEmailNotificationsTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved, email_address: "<EMAIL>")
    @artist_profile = @artist_user.artist_profile
    @artist_profile.update!(waitlist_enabled: true)
    
    # Clear any existing emails
    ActionMailer::Base.deliveries.clear
  end

  test "should send email notification when new waitlist entry is created" do
    perform_enqueued_jobs do
      assert_difference "ActionMailer::Base.deliveries.size", 1 do
        post artist_waitlist_entries_path(@artist_profile), params: {
          waitlist_entry: {
            name: "John Client",
            email: "<EMAIL>",
            phone_number: "555-1234",
            message: "I'd love to get a tattoo!"
          }
        }
      end
    end
    
    assert_redirected_to artist_path(@artist_profile)
    
    # Check the email
    email = ActionMailer::Base.deliveries.last
    assert_not_nil email
    assert_equal ["<EMAIL>"], email.to
    assert_equal "New Waitlist Entry - John Client", email.subject
    assert_match "John Client", email.body.to_s
    assert_match "<EMAIL>", email.body.to_s
    assert_match "555-1234", email.body.to_s
    assert_match "I'd love to get a tattoo!", email.body.to_s
  end

  test "should send email to contact_email when available instead of user email" do
    @artist_profile.update!(contact_email: "<EMAIL>")
    
    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Jane Client", 
          email: "<EMAIL>"
        }
      }
    end
    
    email = ActionMailer::Base.deliveries.last
    assert_equal ["<EMAIL>"], email.to
    assert_equal "New Waitlist Entry - Jane Client", email.subject
  end

  test "should not send email when waitlist entry fails validation" do
    assert_no_difference "ActionMailer::Base.deliveries.size" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "", # Invalid - blank name
          email: "invalid-email" # Invalid format
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should not send duplicate emails for same email address" do
    # Create first entry - should send email
    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "First User",
          email: "<EMAIL>"
        }
      }
    end
    
    # Try to create duplicate - should not send email since validation fails
    assert_no_difference "ActionMailer::Base.deliveries.size" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Second User",
          email: "<EMAIL>" # Duplicate email
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "should send email for same email to different artists" do
    other_artist = create(:artist_profile, waitlist_enabled: true)
    other_artist.user.update!(email_address: "<EMAIL>")
    
    # First artist gets entry
    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "User One",
          email: "<EMAIL>"
        }
      }
    end
    
    first_email = ActionMailer::Base.deliveries.last
    assert_equal ["<EMAIL>"], first_email.to
    
    # Same email to different artist should also send email
    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post artist_waitlist_entries_path(other_artist), params: {
        waitlist_entry: {
          name: "User Two", 
          email: "<EMAIL>"
        }
      }
    end
    
    second_email = ActionMailer::Base.deliveries.last
    assert_equal ["<EMAIL>"], second_email.to
    assert_match "User Two", second_email.body.to_s
  end

  test "should not send email when waitlist is disabled" do
    @artist_profile.update!(waitlist_enabled: false)
    
    assert_no_difference "ActionMailer::Base.deliveries.size" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Test User",
          email: "<EMAIL>"
        }
      }
    end
    
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Waitlist is not enabled for this artist.", flash[:alert]
  end

  test "should include all available entry information in email" do
    post artist_waitlist_entries_path(@artist_profile), params: {
      waitlist_entry: {
        name: "Complete User",
        email: "<EMAIL>", 
        phone_number: "555-9876",
        message: "I'm interested in a sleeve tattoo with traditional Japanese style. I've been following your work for a while!"
      }
    }
    
    email = ActionMailer::Base.deliveries.last
    email_body = email.body.to_s
    
    # Should include all provided information
    assert_match "Complete User", email_body
    assert_match "<EMAIL>", email_body  
    assert_match "555-9876", email_body
    assert_match "I'm interested in a sleeve tattoo", email_body
    assert_match "traditional Japanese style", email_body
    assert_match @artist_profile.name, email_body
  end

  test "should handle entries with minimal information" do
    post artist_waitlist_entries_path(@artist_profile), params: {
      waitlist_entry: {
        name: "Minimal User",
        email: "<EMAIL>"
        # No phone or message
      }
    }
    
    email = ActionMailer::Base.deliveries.last
    email_body = email.body.to_s
    
    # Should still send email with available information
    assert_match "Minimal User", email_body
    assert_match "<EMAIL>", email_body
    assert_match @artist_profile.name, email_body
    
    # Should not break with missing optional fields
    assert_not_match "Phone:", email_body
    assert_not_match "Message:", email_body
  end

  test "should not send email on entry updates" do
    # Create entry first
    entry = create(:waitlist_entry, artist_profile: @artist_profile)
    
    # Clear deliveries from the create
    ActionMailer::Base.deliveries.clear
    
    # Update the entry - should not send email
    assert_no_difference "ActionMailer::Base.deliveries.size" do
      sign_in_as(@artist_user)
      patch mark_as_contacted_artist_waitlist_entry_path(@artist_profile, entry)
    end
    
    assert_redirected_to waitlist_artist_path(@artist_profile)
    entry.reload
    assert entry.contacted?
  end

  test "should not send email on entry deletion" do
    # Create and save entry
    entry = create(:waitlist_entry, artist_profile: @artist_profile)
    
    # Clear deliveries from the create
    ActionMailer::Base.deliveries.clear
    
    # Delete the entry - should not send email
    assert_no_difference "ActionMailer::Base.deliveries.size" do
      sign_in_as(@artist_user)
      delete artist_waitlist_entry_path(@artist_profile, entry)
    end
    
    assert_redirected_to waitlist_artist_path(@artist_profile)
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end