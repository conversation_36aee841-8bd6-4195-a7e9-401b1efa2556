require "test_helper"

class AdminDashboardFlowTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @regular_user = create(:user, :client, :approved)
    @pending_artist = create(:user, :artist, approved: false)
    @approved_artist = create(:user, :artist, :approved)
    @pending_client = create(:user, :client, approved: false)
    
    # Create test data for dashboard statistics
    @test_posts = create_list(:post, 3, artist_profile: @approved_artist.artist_profile)
    @test_conversation = create(:conversation, user1: @regular_user, user2: @approved_artist)
    @test_messages = create_list(:message, 2, conversation: @test_conversation, sender: @regular_user)
    @test_follow = create(:follow, client_profile: @regular_user.client_profile, artist_profile: @approved_artist.artist_profile)
    @test_board = create(:inspiration_board, user: @regular_user)
  end

  test "admin dashboard overview and statistics" do
    sign_in_as(@admin_user)
    
    # Access admin dashboard
    get admin_dashboard_path
    assert_response :success
    assert_select "h1", text: /Admin Dashboard/i
    
    # Verify statistics are displayed
    assert_select "div", text: /#{User.count}.*total.*users/i
    assert_select "div", text: /#{User.where(approved: false).count}.*pending/i
    assert_select "div", text: /#{User.where(role: 'artist').count}.*artists/i
    assert_select "div", text: /#{User.where(role: 'client').count}.*clients/i
    assert_select "div", text: /#{Post.count}.*posts/i
    assert_select "div", text: /#{Conversation.count}.*conversations/i
    assert_select "div", text: /#{Message.count}.*messages/i
    assert_select "div", text: /#{Follow.count}.*follows/i
    assert_select "div", text: /#{InspirationBoard.count}.*inspiration boards/i
    
    # Verify navigation links to admin sections
    assert_select "a[href='#{admin_users_path}']"
    assert_select "a[href='#{admin_posts_path}']"
    assert_select "a[href='#{admin_settings_path}']"
    assert_select "a[href='#{admin_specialties_path}']"
    
    # Verify recent activity sections
    assert_select "h2", text: /Recent Users/i
    assert_select "h2", text: /Pending Approvals/i
    assert_select "h2", text: /Recent Posts/i
    
    # Check that pending users are highlighted
    assert_select "div", text: /#{@pending_artist.username}/
    assert_select "div", text: /#{@pending_client.username}/
  end

  test "admin user management workflow" do
    sign_in_as(@admin_user)
    
    # Navigate to user management from dashboard
    get admin_dashboard_path
    assert_response :success
    
    # Navigate directly to user management
    get admin_users_path
    assert_response :success
    assert_select "h1", text: /Users/<USER>
    
    # View all users
    assert_select "tbody tr", minimum: 5  # All test users
    assert_select "tbody tr", text: /#{@admin_user.username}/
    assert_select "tbody tr", text: /#{@pending_artist.username}/
    assert_select "tbody tr", text: /#{@pending_client.username}/
    
    # Filter pending users
    get admin_users_path(status: 'pending')
    assert_response :success
    assert_select "tbody tr", count: 2  # Two pending users
    
    # Quick approve from dashboard
    get admin_dashboard_path
    assert_response :success
    
    # Approve pending artist
    patch approve_admin_user_path(@pending_artist)
    assert_redirected_to admin_users_path
    follow_redirect!
    assert_match /approved/, flash[:notice]
    
    # Return to dashboard and verify pending count decreased
    get admin_dashboard_path
    assert_response :success
    assert_select "div", text: /1.*pending/i  # One less pending user
  end

  test "admin post management workflow" do
    sign_in_as(@admin_user)
    
    # Navigate to post management
    get admin_posts_path
    assert_response :success
    assert_select "h1", text: /Posts/i
    
    # View all posts
    assert_select "tbody tr", minimum: 3  # Test posts
    
    # View specific post
    test_post = @test_posts.first
    get admin_post_path(test_post)
    assert_response :success
    assert_select "h1", text: /Post ##{test_post.id}/
    assert_select "div", text: /#{test_post.caption}/
    assert_select "div", text: /#{test_post.artist_profile.name}/
    
    # Edit post if needed
    get edit_admin_post_path(test_post)
    assert_response :success
    assert_select "form"
    
    # Update post
    patch admin_post_path(test_post), params: {
      post: {
        caption: "Admin updated caption"
      }
    }
    
    assert_redirected_to admin_post_path(test_post)
    follow_redirect!
    assert_match /updated/, flash[:notice]
    
    test_post.reload
    assert_equal "Admin updated caption", test_post.caption
  end

  test "admin settings management workflow" do
    # Create or ensure site setting exists
    site_setting = SiteSetting.first || create(:site_setting)
    
    sign_in_as(@admin_user)
    
    # Navigate to settings
    get admin_settings_path
    assert_response :success
    assert_select "h1", text: /Settings/i
    
    # View current settings
    assert_select "form"
    assert_select "input[type='checkbox'][name='site_setting[approval_required]']"
    
    # Update settings
    patch admin_settings_path, params: {
      site_setting: {
        approval_required: false
      }
    }
    
    assert_redirected_to admin_settings_path
    follow_redirect!
    assert_match /updated/, flash[:notice]
    
    site_setting.reload
    assert_not site_setting.approval_required?
  end

  test "admin specialty management workflow" do
    sign_in_as(@admin_user)
    
    # Navigate to specialties
    get admin_specialties_path
    assert_response :success
    assert_select "h1", text: /Specialties/i
    
    # View existing specialties
    assert_select "tbody tr", minimum: 0
    
    # Create new specialty
    get new_admin_specialty_path
    assert_response :success
    assert_select "form"
    assert_select "input[name='specialty[title]']"
    assert_select "textarea[name='specialty[description]']"
    
    assert_difference 'Specialty.count', 1 do
      post admin_specialties_path, params: {
        specialty: {
          title: "Neo-Traditional",
          description: "Modern take on traditional tattoo styles with enhanced color and detail"
        }
      }
    end
    
    assert_redirected_to admin_specialties_path
    follow_redirect!
    assert_match /created/, flash[:notice]
    
    new_specialty = Specialty.last
    assert_equal "Neo-Traditional", new_specialty.title
    
    # Edit specialty
    get edit_admin_specialty_path(new_specialty)
    assert_response :success
    
    patch admin_specialty_path(new_specialty), params: {
      specialty: {
        title: "Neo-Traditional & Illustrative",
        description: "Updated description with more detail"
      }
    }
    
    assert_redirected_to admin_specialties_path
    follow_redirect!
    
    new_specialty.reload
    assert_equal "Neo-Traditional & Illustrative", new_specialty.title
  end

  test "admin content moderation workflow" do
    # Create potentially problematic content
    flagged_post = create(:post, 
      artist_profile: @approved_artist.artist_profile,
      caption: "Potentially inappropriate content"
    )
    
    inappropriate_comment = create(:comment,
      post: flagged_post,
      user: @regular_user,
      content: "This might be inappropriate content"
    )
    
    sign_in_as(@admin_user)
    
    # Review flagged content from dashboard
    get admin_dashboard_path
    assert_response :success
    
    # Navigate to posts for moderation
    get admin_posts_path
    assert_response :success
    
    # View flagged post
    get admin_post_path(flagged_post)
    assert_response :success
    assert_select "div", text: /Potentially inappropriate content/
    
    # Edit or remove inappropriate content
    patch admin_post_path(flagged_post), params: {
      post: {
        caption: "[Content moderated by admin]"
      }
    }
    
    assert_redirected_to admin_post_path(flagged_post)
    follow_redirect!
    
    flagged_post.reload
    assert_equal "[Content moderated by admin]", flagged_post.caption
    
    # Delete post if necessary
    assert_difference 'Post.count', -1 do
      delete admin_post_path(flagged_post)
    end
    
    assert_redirected_to admin_posts_path
    follow_redirect!
    assert_match /deleted/, flash[:notice]
  end

  test "admin activity monitoring and reporting" do
    sign_in_as(@admin_user)
    
    # View dashboard with activity overview
    get admin_dashboard_path
    assert_response :success
    
    # Check recent activity sections show relevant data
    assert_select "div", text: /#{@pending_artist.username}/, count: 1
    assert_select "div", text: /#{@pending_client.username}/, count: 1
    
    # Recent posts section
    @test_posts.each do |post|
      assert_select "div", text: /#{post.caption[0..20]}/
    end
    
    # Navigate to detailed user list
    get admin_users_path
    assert_response :success
    
    # Sort and filter capabilities
    get admin_users_path(role: 'artist')
    assert_response :success
    assert_select "tbody tr", text: /#{@approved_artist.username}/
    assert_select "tbody tr", text: /#{@pending_artist.username}/
    
    # Search functionality
    get admin_users_path(search: @regular_user.username)
    assert_response :success
    assert_select "tbody tr", count: 1
    assert_select "tbody tr", text: /#{@regular_user.username}/
  end

  test "admin system health and maintenance" do
    sign_in_as(@admin_user)
    
    # Dashboard should show system health indicators
    get admin_dashboard_path
    assert_response :success
    
    # Verify all key metrics are present and reasonable
    total_users = User.count
    pending_users = User.where(approved: false).count
    
    assert total_users > 0, "Should have users in system"
    assert_select "div", text: /#{total_users}.*total.*users/i
    
    if pending_users > 0
      assert_select "div", text: /#{pending_users}.*pending/i
    end
    
    # Check that admin can access all management areas
    %w[users posts settings specialties].each do |area|
      get send("admin_#{area}_path")
      assert_response :success, "Admin should be able to access #{area} management"
    end
  end

  test "non-admin access restrictions" do
    # Regular user cannot access admin dashboard
    sign_in_as(@regular_user)
    
    get admin_dashboard_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
    
    # Pending user cannot access admin area
    sign_in_as(@pending_artist)
    
    get admin_dashboard_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
    
    # Unauthenticated user redirected to login
    delete session_path
    
    get admin_dashboard_path
    assert_redirected_to new_session_path
  end

  test "admin bulk operations" do
    # Create multiple pending users for bulk operations
    pending_users = create_list(:user, 3, :artist, approved: false)
    
    sign_in_as(@admin_user)
    
    # Bulk approve multiple users
    pending_users.each do |user|
      patch approve_admin_user_path(user)
    end
    
    # Verify all are approved
    pending_users.each do |user|
      user.reload
      assert user.approved?, "User #{user.username} should be approved"
    end
    
    # Dashboard should reflect updated counts
    get admin_dashboard_path
    assert_response :success
    
    # Pending count should be reduced
    remaining_pending = User.where(approved: false).count
    assert_select "div", text: /#{remaining_pending}.*pending/i
  end

  test "admin error handling and edge cases" do
    sign_in_as(@admin_user)
    
    # Try to access non-existent resources
    get admin_user_path(99999)
    assert_response :not_found
    
    get admin_post_path(99999)
    assert_response :not_found
    
    # Dashboard should handle empty data gracefully
    # Delete test data temporarily
    Post.delete_all
    Message.delete_all
    Conversation.delete_all
    
    get admin_dashboard_path
    assert_response :success
    assert_select "div", text: /0.*posts/i
    assert_select "div", text: /0.*messages/i
    assert_select "div", text: /0.*conversations/i
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end