require "test_helper"

class WaitlistFlowTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @client_user = create(:user, :client, :approved)
  end

  test "complete waitlist flow from enable to signup to management" do
    # Step 1: Artist enables waitlist
    sign_in_as(@artist_user)
    
    get edit_artist_path(@artist_profile)
    assert_response :success
    assert_select "input[name='artist_profile[waitlist_enabled]'][type='checkbox']"
    
    # Enable waitlist
    patch artist_path(@artist_profile), params: {
      artist_profile: {
        name: @artist_profile.name,
        waitlist_enabled: true
      }
    }
    
    assert_redirected_to artist_path(@artist_profile)
    @artist_profile.reload
    assert @artist_profile.waitlist_enabled?
    
    # Step 2: Verify Join Waitlist button appears on artist profile
    get artist_path(@artist_profile)
    assert_response :success
    assert_select "a[href='#{new_artist_waitlist_entry_path(@artist_profile)}']", text: "Join Waitlist"
    
    # Step 3: Verify waitlist tab is visible to artist owner
    assert_select "a[href='#{waitlist_artist_path(@artist_profile)}']", text: /Waitlist/
    
    # Step 4: Access waitlist management page
    get waitlist_artist_path(@artist_profile)
    assert_response :success
    assert_select "h2", text: "Waitlist"
    
    sign_out
    
    # Step 5: Public user joins waitlist
    get new_artist_waitlist_entry_path(@artist_profile)
    assert_response :success
    assert_select "h1", text: /Join.*Waitlist/
    assert_select "form"
    
    # Submit waitlist entry
    assert_difference "WaitlistEntry.count", 1 do
      assert_enqueued_emails 1 do
        post artist_waitlist_entries_path(@artist_profile), params: {
          waitlist_entry: {
            name: "Jane Client",
            email: "<EMAIL>", 
            phone_number: "555-9876",
            message: "I'd love to get a traditional tattoo!"
          }
        }
      end
    end
    
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Successfully joined the waitlist!", flash[:notice]
    
    waitlist_entry = WaitlistEntry.last
    assert_equal "Jane Client", waitlist_entry.name
    assert_equal "<EMAIL>", waitlist_entry.email
    assert waitlist_entry.pending?
    
    # Step 6: Artist manages waitlist entry
    sign_in_as(@artist_user)
    
    get waitlist_artist_path(@artist_profile)
    assert_response :success
    assert_select "h3", text: "Jane Client"
    assert_select ".bg-yellow-100", text: "Pending"
    
    # View entry details
    get artist_waitlist_entry_path(@artist_profile, waitlist_entry)
    assert_response :success
    assert_select "h2", text: "Jane Client"
    assert_select "p", text: /<EMAIL>/
    assert_select "p", text: /555-9876/
    assert_select "p", text: /I'd love to get a traditional tattoo!/
    
    # Mark as contacted
    patch mark_as_contacted_artist_waitlist_entry_path(@artist_profile, waitlist_entry)
    assert_redirected_to waitlist_artist_path(@artist_profile)
    assert_equal "Marked as contacted.", flash[:notice]
    
    waitlist_entry.reload
    assert waitlist_entry.contacted?
    
    # Verify status change in waitlist view
    get waitlist_artist_path(@artist_profile)
    assert_select ".bg-green-100", text: "Contacted"
    
    # Step 7: Delete waitlist entry
    assert_difference "WaitlistEntry.count", -1 do
      delete artist_waitlist_entry_path(@artist_profile, waitlist_entry)
    end
    
    assert_redirected_to waitlist_artist_path(@artist_profile)
    assert_equal "Waitlist entry removed.", flash[:notice]
  end

  test "waitlist privacy and access control flow" do
    @artist_profile.update!(waitlist_enabled: true)
    entry = create(:waitlist_entry, artist_profile: @artist_profile)
    
    # Step 1: Non-owner cannot access waitlist management
    sign_in_as(@client_user)
    
    get waitlist_artist_path(@artist_profile)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    get artist_waitlist_entry_path(@artist_profile, entry)
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Access denied.", flash[:alert]
    
    # Step 2: Non-owner cannot modify entries
    patch mark_as_contacted_artist_waitlist_entry_path(@artist_profile, entry)
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Access denied.", flash[:alert]
    
    delete artist_waitlist_entry_path(@artist_profile, entry)
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Access denied.", flash[:alert]
    
    # Step 3: Non-owner should not see waitlist tab
    get artist_path(@artist_profile)
    assert_response :success
    assert_select "a[href='#{waitlist_artist_path(@artist_profile)}']", count: 0
    
    # Step 4: But non-owner should see Join Waitlist button
    assert_select "a[href='#{new_artist_waitlist_entry_path(@artist_profile)}']", text: "Join Waitlist"
    
    sign_out
    
    # Step 5: Unauthenticated users should also not access management
    get waitlist_artist_path(@artist_profile)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    # Step 6: But can access public signup form
    get new_artist_waitlist_entry_path(@artist_profile)
    assert_response :success
  end

  test "disabled waitlist flow" do
    # Ensure waitlist is disabled
    @artist_profile.update!(waitlist_enabled: false)
    
    # Step 1: Join Waitlist button should not appear
    get artist_path(@artist_profile)
    assert_response :success
    assert_select "a[href='#{new_artist_waitlist_entry_path(@artist_profile)}']", count: 0
    
    # Step 2: Waitlist tab should not appear even to owner
    sign_in_as(@artist_user)
    get artist_path(@artist_profile)
    assert_select "a[href='#{waitlist_artist_path(@artist_profile)}']", count: 0
    
    # Step 3: Direct access to signup form should redirect
    sign_out
    get new_artist_waitlist_entry_path(@artist_profile)
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Waitlist is not enabled for this artist.", flash[:alert]
    
    # Step 4: Cannot submit entries when disabled
    assert_no_difference "WaitlistEntry.count" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Test User",
          email: "<EMAIL>"
        }
      }
    end
    
    assert_redirected_to artist_path(@artist_profile)
    assert_equal "Waitlist is not enabled for this artist.", flash[:alert]
  end

  test "duplicate email prevention flow" do
    @artist_profile.update!(waitlist_enabled: true)
    
    # Create first entry
    post artist_waitlist_entries_path(@artist_profile), params: {
      waitlist_entry: {
        name: "First User",
        email: "<EMAIL>"
      }
    }
    assert_redirected_to artist_path(@artist_profile)
    
    # Try to create duplicate
    assert_no_difference "WaitlistEntry.count" do
      post artist_waitlist_entries_path(@artist_profile), params: {
        waitlist_entry: {
          name: "Second User", 
          email: "<EMAIL>"
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select ".text-red-800", /problems with your submission/
    
    # But same email should work for different artist
    other_artist = create(:artist_profile, waitlist_enabled: true)
    assert_difference "WaitlistEntry.count", 1 do
      post artist_waitlist_entries_path(other_artist), params: {
        waitlist_entry: {
          name: "Same Email User",
          email: "<EMAIL>"
        }
      }
    end
    assert_redirected_to artist_path(other_artist)
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end

  def sign_out
    delete session_url
  end
end