require "test_helper"

class MessagingFlowTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @client_user = create(:user, :client, :approved)
    @other_client = create(:user, :client, :approved)
    @blocked_user = create(:user, :client, :approved)
    
    # Create a block relationship
    create(:block, blocker: @client_user, blocked: @blocked_user)
  end

  test "complete messaging flow between client and artist" do
    sign_in_as(@client_user)
    
    # Start conversation from artist profile
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    assert_select "a", text: /Message/i
    
    # Initiate new conversation
    get new_message_path(recipient_id: @artist_user.id)
    assert_response :success
    assert_select "form"
    assert_select "textarea[name='message[content]']"
    
    # Send first message
    assert_difference ['Conversation.count', 'Message.count'], 1 do
      post messages_path, params: {
        message: {
          recipient_id: @artist_user.id,
          content: "Hi! I saw your work and I'm interested in getting a tattoo. Are you taking new clients?"
        }
      }
    end
    
    conversation = Conversation.last
    first_message = Message.last
    
    assert_redirected_to conversation_path(conversation)
    follow_redirect!
    
    # Verify conversation setup
    assert_equal @client_user, first_message.sender
    assert_equal "Hi! I saw your work and I'm interested in getting a tattoo. Are you taking new clients?", first_message.content
    assert_includes conversation.participants, @client_user
    assert_includes conversation.participants, @artist_user
    
    # Verify message appears in conversation
    assert_select "div", text: /Hi! I saw your work and I'm interested/
    assert_select "div", text: /#{@client_user.display_name}/
    
    # Check conversation list
    get conversations_path
    assert_response :success
    assert_select ".conversation", count: 1
    assert_select "div", text: /#{@artist_user.display_name}/
    assert_select "div", text: /interested in getting a tattoo/
    
    # Artist receives and responds
    sign_in_as(@artist_user)
    
    get conversations_path
    assert_response :success
    assert_select ".conversation", count: 1
    assert_select "div", text: /#{@client_user.display_name}/
    
    # Artist views conversation
    get conversation_path(conversation)
    assert_response :success
    assert_select "div", text: /Hi! I saw your work and I'm interested/
    
    # Artist replies
    assert_difference 'Message.count', 1 do
      post conversation_messages_path(conversation), params: {
        message: {
          content: "Thanks for reaching out! Yes, I'm currently taking new clients. What style are you thinking about and what size piece?"
        }
      }
    end
    
    assert_redirected_to conversation_path(conversation)
    follow_redirect!
    
    artist_reply = Message.last
    assert_equal @artist_user, artist_reply.sender
    assert_select "div", text: /Thanks for reaching out! Yes, I'm currently taking new clients/
    
    # Client continues conversation
    sign_in_as(@client_user)
    
    get conversation_path(conversation)
    assert_response :success
    assert_select "div", text: /Thanks for reaching out! Yes, I'm currently taking new clients/
    
    # Client sends follow-up
    assert_difference 'Message.count', 1 do
      post conversation_messages_path(conversation), params: {
        message: {
          content: "I'm thinking about a traditional style piece on my forearm, maybe 4-5 inches. Do you have availability in the next month?"
        }
      }
    end
    
    # Artist provides detailed response with booking info
    sign_in_as(@artist_user)
    
    get conversation_path(conversation)
    assert_response :success
    
    assert_difference 'Message.count', 1 do
      post conversation_messages_path(conversation), params: {
        message: {
          content: "Perfect! Traditional is one of my specialties. For a 4-5 inch forearm piece, I'd estimate 2-3 hours. My rate is $150/hour with a $100 deposit. I have openings next Friday or the following Tuesday. Would either work for you?"
        }
      }
    end
    
    # Verify full conversation history
    get conversation_path(conversation)
    assert_response :success
    
    conversation.reload
    assert_equal 4, conversation.messages.count
    assert_select "div", text: /Hi! I saw your work and I'm interested/, count: 1
    assert_select "div", text: /Thanks for reaching out! Yes, I'm currently taking/, count: 1
    assert_select "div", text: /I'm thinking about a traditional style/, count: 1
    assert_select "div", text: /Perfect! Traditional is one of my specialties/, count: 1
  end

  test "message with image attachment flow" do
    sign_in_as(@client_user)
    
    # Create initial conversation
    conversation = Conversation.find_or_create_between(@client_user, @artist_user)
    
    get conversation_path(conversation)
    assert_response :success
    assert_select "form"
    assert_select "input[type='file'][name='message[image]']"
    
    # Send message with image
    assert_difference 'Message.count', 1 do
      post conversation_messages_path(conversation), params: {
        message: {
          content: "Here's a reference image for the style I'm looking for",
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')
        }
      }
    end
    
    assert_redirected_to conversation_path(conversation)
    follow_redirect!
    
    image_message = Message.last
    assert image_message.image_attached?
    assert_equal "Here's a reference image for the style I'm looking for", image_message.content
    assert_select "div", text: /Here's a reference image/
    
    # Artist can view the image
    sign_in_as(@artist_user)
    
    get conversation_path(conversation)
    assert_response :success
    assert_select "div", text: /Here's a reference image/
  end

  test "message read status and unread counts" do
    sign_in_as(@client_user)
    
    conversation = Conversation.find_or_create_between(@client_user, @artist_user)
    
    # Client sends message
    post conversation_messages_path(conversation), params: {
      message: { content: "Test message from client" }
    }
    
    client_message = Message.last
    assert_not client_message.read?
    
    # Artist views conversations - should show unread count
    sign_in_as(@artist_user)
    
    get conversations_path
    assert_response :success
    # Unread indicator would depend on view implementation
    
    # Artist views conversation - messages should be marked as read
    get conversation_path(conversation)
    assert_response :success
    
    # Simulate marking messages as read (this would typically happen via JavaScript)
    client_message.reload
    # Note: The actual read marking logic might be handled via AJAX or background job
    
    # Artist replies
    post conversation_messages_path(conversation), params: {
      message: { content: "Thanks for your message!" }
    }
    
    artist_message = Message.last
    assert_equal @artist_user, artist_message.sender
    
    # Client checks for unread messages
    sign_in_as(@client_user)
    
    assert_equal 1, @client_user.unread_messages_count
    
    get conversations_path
    assert_response :success
    # Should show unread indicator
    
    get conversation_path(conversation)
    assert_response :success
    # Should show artist's reply
    assert_select "div", text: /Thanks for your message!/
  end

  test "blocked user messaging restrictions" do
    # Client tries to message blocked user
    sign_in_as(@client_user)
    
    get new_message_path(recipient_id: @blocked_user.id)
    assert_redirected_to root_path
    assert_equal "You cannot send messages to this user.", flash[:alert]
    
    # Blocked user tries to message client
    sign_in_as(@blocked_user)
    
    get new_message_path(recipient_id: @client_user.id)
    assert_redirected_to root_path
    assert_equal "You cannot send messages to this user.", flash[:alert]
  end

  test "artist with messages disabled" do
    # Disable messages for artist
    @artist_user.artist_profile.update!(messages_enabled: false)
    
    sign_in_as(@client_user)
    
    # Should not be able to start conversation
    get new_message_path(recipient_id: @artist_user.id)
    assert_redirected_to root_path
    assert_equal "You cannot send messages to this user.", flash[:alert]
    
    # Artist profile should not show message button
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    assert_select "a", text: /Message/i, count: 0
  end

  test "conversation management and organization" do
    sign_in_as(@client_user)
    
    # Create multiple conversations
    conversation1 = Conversation.find_or_create_between(@client_user, @artist_user)
    conversation2 = Conversation.find_or_create_between(@client_user, @other_client)
    
    # Send messages to both
    post conversation_messages_path(conversation1), params: {
      message: { content: "Message to artist" }
    }
    
    post conversation_messages_path(conversation2), params: {
      message: { content: "Message to other client" }
    }
    
    # View conversations list
    get conversations_path
    assert_response :success
    assert_select ".conversation", count: 2
    
    # Most recent conversation should appear first
    conversations = @client_user.conversations.recent
    assert_equal conversation2, conversations.first
    assert_equal conversation1, conversations.second
    
    # Artist responds to first conversation
    sign_in_as(@artist_user)
    
    post conversation_messages_path(conversation1), params: {
      message: { content: "Response from artist" }
    }
    
    # This should bump conversation1 to the top
    sign_in_as(@client_user)
    
    get conversations_path
    assert_response :success
    
    updated_conversations = @client_user.conversations.recent
    assert_equal conversation1, updated_conversations.first
  end

  test "message validation and error handling" do
    sign_in_as(@client_user)
    
    conversation = Conversation.find_or_create_between(@client_user, @artist_user)
    
    # Try to send empty message
    assert_no_difference 'Message.count' do
      post conversation_messages_path(conversation), params: {
        message: { content: "" }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select "form"
    # Error message would depend on view implementation
    
    # Try to send message that's too long
    long_content = "a" * 1001  # Over 1000 character limit
    
    assert_no_difference 'Message.count' do
      post conversation_messages_path(conversation), params: {
        message: { content: long_content }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "conversation access control" do
    # Create conversation between other users
    other_conversation = Conversation.find_or_create_between(@artist_user, @other_client)
    create(:message, conversation: other_conversation, sender: @artist_user, content: "Private message")
    
    # Client should not be able to access other's conversation
    sign_in_as(@client_user)
    
    get conversation_path(other_conversation)
    assert_redirected_to conversations_path
    assert_equal "Access denied.", flash[:alert]
    
    # Should not be able to send messages to conversation they're not part of
    post conversation_messages_path(other_conversation), params: {
      message: { content: "Trying to intrude" }
    }
    assert_redirected_to conversations_path
  end

  test "unauthenticated user messaging restrictions" do
    conversation = Conversation.find_or_create_between(@client_user, @artist_user)
    
    # Unauthenticated user cannot access conversations
    get conversations_path
    assert_redirected_to new_session_path
    
    get conversation_path(conversation)
    assert_redirected_to new_session_path
    
    get new_message_path(recipient_id: @artist_user.id)
    assert_redirected_to new_session_path
    
    post conversation_messages_path(conversation), params: {
      message: { content: "Unauthorized message" }
    }
    assert_redirected_to new_session_path
  end

  test "message ordering and timestamp display" do
    sign_in_as(@client_user)
    
    conversation = Conversation.find_or_create_between(@client_user, @artist_user)
    
    # Send multiple messages with time gaps
    post conversation_messages_path(conversation), params: {
      message: { content: "First message" }
    }
    
    first_message = Message.last
    
    # Simulate time passing
    travel 1.hour do
      post conversation_messages_path(conversation), params: {
        message: { content: "Second message after an hour" }
      }
    end
    
    second_message = Message.last
    
    # Artist responds
    sign_in_as(@artist_user)
    
    post conversation_messages_path(conversation), params: {
      message: { content: "Artist response" }
    }
    
    # View conversation and verify order
    get conversation_path(conversation)
    assert_response :success
    
    # Messages should appear in chronological order
    messages = conversation.messages.recent
    assert_equal first_message, messages.first
    assert_equal second_message, messages.second
    assert_equal Message.last, messages.third
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end