require "test_helper"

class PortfolioFlowTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @other_artist = create(:user, :artist, :approved)
    @client_user = create(:user, :client, :approved)
  end

  test "complete portfolio management flow" do
    sign_in_as(@artist_user)
    
    # Visit empty portfolio
    get portfolio_items_path
    assert_response :success
    assert_select "h1", text: "My Portfolio"
    assert_select ".text-gray-900", text: "No portfolio items"
    assert_select "a[href='#{new_portfolio_item_path}']", text: "Add Portfolio Item"
    
    # Create first portfolio item
    get new_portfolio_item_path
    assert_response :success
    assert_select "form"
    assert_select "input[type='file'][name='portfolio_item[image]']"
    assert_select "textarea[name='portfolio_item[caption]']"
    assert_select "input[type='number'][name='portfolio_item[position]']"
    
    assert_difference 'PortfolioItem.count', 1 do
      post portfolio_items_path, params: {
        portfolio_item: {
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
          caption: "My first tattoo - a traditional rose",
          position: 0
        }
      }
    end
    
    portfolio_item = PortfolioItem.last
    assert_redirected_to portfolio_item_path(portfolio_item)
    follow_redirect!
    
    # Verify portfolio item was created correctly
    assert_equal @artist_profile, portfolio_item.artist_profile
    assert_equal "My first tattoo - a traditional rose", portfolio_item.caption
    assert_equal 0, portfolio_item.position
    assert portfolio_item.image.attached?
    
    # View the portfolio item
    assert_response :success
    assert_select "h1", text: "Portfolio Item"
    assert_select "p", text: /Position: 0/
    assert_select "div", text: /My first tattoo - a traditional rose/
    
    # Edit the portfolio item
    get edit_portfolio_item_path(portfolio_item)
    assert_response :success
    assert_select "form"
    assert_select "label", text: "Current Image"
    assert_select "input[type='file'][name='portfolio_item[image]']"
    
    # Update caption and position
    patch portfolio_item_path(portfolio_item), params: {
      portfolio_item: {
        caption: "Updated: My first tattoo - a beautiful traditional rose",
        position: 1
      }
    }
    
    assert_redirected_to portfolio_item_path(portfolio_item)
    portfolio_item.reload
    assert_equal "Updated: My first tattoo - a beautiful traditional rose", portfolio_item.caption
    assert_equal 1, portfolio_item.position
    
    # Add a second portfolio item
    assert_difference 'PortfolioItem.count', 1 do
      post portfolio_items_path, params: {
        portfolio_item: {
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
          caption: "Geometric sleeve design",
          position: 0  # This should be first now
        }
      }
    end
    
    second_item = PortfolioItem.last
    
    # View portfolio index with multiple items
    get portfolio_items_path
    assert_response :success
    assert_select ".grid .bg-white", count: 2
    
    # Verify items are ordered correctly
    portfolio_items = @artist_profile.portfolio_items.ordered
    assert_equal second_item, portfolio_items.first  # position 0
    assert_equal portfolio_item, portfolio_items.second  # position 1
    
    # Delete a portfolio item
    assert_difference 'PortfolioItem.count', -1 do
      delete portfolio_item_path(second_item)
    end
    
    assert_redirected_to portfolio_items_path
    follow_redirect!
    
    # Verify only one item remains
    assert_select ".grid .bg-white", count: 1
  end

  test "portfolio item access control" do
    portfolio_item = create(:portfolio_item, artist_profile: @artist_profile)
    
    # Other artists cannot access this item
    sign_in_as(@other_artist)
    
    assert_raises(ActiveRecord::RecordNotFound) do
      get portfolio_item_path(portfolio_item)
    end
    
    assert_raises(ActiveRecord::RecordNotFound) do
      get edit_portfolio_item_path(portfolio_item)
    end
    
    assert_raises(ActiveRecord::RecordNotFound) do
      delete portfolio_item_path(portfolio_item)
    end
    
    # Clients cannot manage portfolios
    sign_in_as(@client_user)
    
    get portfolio_items_path
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    get new_portfolio_item_path
    assert_redirected_to root_path
    
    # Unauthenticated users cannot access
    post session_url, params: { email_address: "<EMAIL>", password: "wrong" }
    
    get portfolio_items_path
    assert_redirected_to new_session_path
  end

  test "portfolio item validation errors" do
    sign_in_as(@artist_user)
    
    # Try to create item without image
    assert_no_difference 'PortfolioItem.count' do
      post portfolio_items_path, params: {
        portfolio_item: {
          caption: "Caption without image",
          position: 0
          # No image provided
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select ".text-red-800", text: /There were 1 error/
    assert_select "li", text: /Image must be attached/
    
    # Try to create item with invalid position
    assert_no_difference 'PortfolioItem.count' do
      post portfolio_items_path, params: {
        portfolio_item: {
          image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
          caption: "Valid caption",
          position: -1  # Invalid - must be >= 0
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select ".text-red-800", text: /There were 1 error/
    assert_select "li", text: /Position must be greater than or equal to 0/
  end

  test "portfolio ordering and positioning" do
    sign_in_as(@artist_user)
    
    # Create items in non-sequential order
    item_positions = [5, 1, 3, 0, 2]
    items = []
    
    item_positions.each_with_index do |position, index|
      item = create(:portfolio_item, 
        artist_profile: @artist_profile, 
        position: position,
        caption: "Item at position #{position}"
      )
      items << item
    end
    
    # Verify ordering on index page
    get portfolio_items_path
    assert_response :success
    
    # Check that items appear in correct order (0, 1, 2, 3, 5)
    ordered_items = @artist_profile.portfolio_items.ordered
    expected_positions = [0, 1, 2, 3, 5]
    actual_positions = ordered_items.map(&:position)
    
    assert_equal expected_positions, actual_positions
    
    # Verify captions appear in position order
    page_content = response.body
    position_0_index = page_content.index("Item at position 0")
    position_1_index = page_content.index("Item at position 1")
    position_5_index = page_content.index("Item at position 5")
    
    assert position_0_index < position_1_index
    assert position_1_index < position_5_index
  end

  test "portfolio item image handling" do
    sign_in_as(@artist_user)
    
    # Create item with image
    post portfolio_items_path, params: {
      portfolio_item: {
        image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
        caption: "Test image",
        position: 0
      }
    }
    
    item = PortfolioItem.last
    assert item.image.attached?
    assert_equal "test.jpg", item.image.filename.to_s
    assert_equal "image/jpeg", item.image.content_type
    
    # Visit show page and verify image is displayed
    get portfolio_item_path(item)
    assert_response :success
    assert_select "img[alt='Test image']"
    
    # Update with new image
    patch portfolio_item_path(item), params: {
      portfolio_item: {
        image: fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
        caption: "Updated image"
      }
    }
    
    item.reload
    assert item.image.attached?
    assert_equal "Updated image", item.caption
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end