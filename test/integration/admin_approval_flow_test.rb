require "test_helper"

class AdminApprovalFlowTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = create(:user, :client, :approved, admin: true)
    @pending_artist = create(:user, :artist, approved: false)
    @pending_client = create(:user, :client, approved: false)
    @approved_artist = create(:user, :artist, :approved)
    @approved_client = create(:user, :client, :approved)
    create(:site_setting, approval_required: true)
  end

  test "complete admin approval workflow for artist" do
    # Admin logs in
    sign_in_as(@admin_user)
    
    # Navigate to admin dashboard
    get admin_dashboard_path
    assert_response :success
    assert_select "h1", text: /Admin Dashboard/i
    assert_select "div", text: /Pending Approvals/i
    
    # View pending users
    get admin_users_path
    assert_response :success
    assert_select "tbody tr", minimum: 2  # At least pending artist and client
    
    # Filter to pending users
    get admin_users_path(status: 'pending')
    assert_response :success
    
    # Should see pending artist
    assert_select "tbody tr", text: /#{@pending_artist.username}/
    assert_select "tbody tr", text: /#{@pending_client.username}/
    
    # View pending artist details
    get admin_user_path(@pending_artist)
    assert_response :success
    assert_select "h1", text: /#{@pending_artist.username}/
    assert_select "div", text: /#{@pending_artist.artist_profile.name}/
    assert_select "div", text: /#{@pending_artist.artist_profile.biography}/
    assert_select "a", text: /Approve/i
    assert_select "a", text: /Reject/i
    
    # Approve the artist
    patch approve_admin_user_path(@pending_artist)
    assert_redirected_to admin_users_path
    follow_redirect!
    assert_match /#{@pending_artist.username} has been approved/, flash[:notice]
    
    # Verify artist is approved
    @pending_artist.reload
    assert @pending_artist.approved?
    
    # Artist should now have full access
    sign_in_as(@pending_artist)
    
    # Can create posts
    get new_post_path
    assert_response :success
    
    assert_difference 'Post.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "text",
          body: "My first post after approval!"
        }
      }
    end
    
    assert_redirected_to posts_path
    
    # Can interact with other content
    existing_post = create(:post, artist_profile: @approved_artist.artist_profile)
    
    post post_likes_path(existing_post)
    assert_redirected_to post_path(existing_post)
    
    assert_difference 'Comment.count', 1 do
      post post_comments_path(existing_post), params: {
        comment: { content: "Great work from a newly approved artist!" }
      }
    end
  end

  test "admin rejection workflow" do
    sign_in_as(@admin_user)
    
    # View pending artist
    get admin_user_path(@pending_artist)
    assert_response :success
    
    # Reject the artist
    patch reject_admin_user_path(@pending_artist)
    assert_redirected_to admin_users_path
    follow_redirect!
    assert_match /#{@pending_artist.username} has been rejected/, flash[:notice]
    
    # Verify artist is still not approved
    @pending_artist.reload
    assert_not @pending_artist.approved?
    
    # Artist should still have limited access
    sign_in_as(@pending_artist)
    
    existing_post = create(:post, artist_profile: @approved_artist.artist_profile)
    
    post post_likes_path(existing_post)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    get new_post_path
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
  end

  test "bulk user management workflow" do
    # Create multiple pending users
    pending_artist2 = create(:user, :artist, approved: false)
    pending_client2 = create(:user, :client, approved: false)
    
    sign_in_as(@admin_user)
    
    # View all pending users
    get admin_users_path(status: 'pending')
    assert_response :success
    
    # Should see all 4 pending users
    assert_select "tbody tr", count: 4
    
    # Approve first artist
    patch approve_admin_user_path(@pending_artist)
    assert_redirected_to admin_users_path
    
    # Reject second artist
    patch reject_admin_user_path(pending_artist2)
    assert_redirected_to admin_users_path
    
    # Approve first client
    patch approve_admin_user_path(@pending_client)
    assert_redirected_to admin_users_path
    
    # Check approved users filter
    get admin_users_path(status: 'approved')
    assert_response :success
    
    # Should include the newly approved users plus existing approved users
    assert_select "tbody tr", minimum: 4  # 2 newly approved + 2 existing approved
    
    # Check that approved users can now access platform
    [@pending_artist, @pending_client].each do |user|
      user.reload
      assert user.approved?
      
      sign_in_as(user)
      
      # Should be able to access restricted content
      existing_post = create(:post, artist_profile: @approved_artist.artist_profile)
      
      post post_likes_path(existing_post)
      assert_redirected_to post_path(existing_post)
    end
  end

  test "admin user search and filtering" do
    sign_in_as(@admin_user)
    
    # Search by username
    get admin_users_path(search: @pending_artist.username)
    assert_response :success
    assert_select "tbody tr", count: 1
    assert_select "tbody tr", text: /#{@pending_artist.username}/
    
    # Search by email
    get admin_users_path(search: @pending_client.email_address.split('@').first)
    assert_response :success
    assert_select "tbody tr", text: /#{@pending_client.email_address}/
    
    # Filter by role
    get admin_users_path(role: 'artist')
    assert_response :success
    
    # Should see both pending and approved artists
    assert_select "tbody tr", text: /#{@pending_artist.username}/
    assert_select "tbody tr", text: /#{@approved_artist.username}/
    
    # Filter by client role
    get admin_users_path(role: 'client')
    assert_response :success
    assert_select "tbody tr", text: /#{@pending_client.username}/
    assert_select "tbody tr", text: /#{@approved_client.username}/
    
    # Combine filters - pending artists only
    get admin_users_path(role: 'artist', status: 'pending')
    assert_response :success
    assert_select "tbody tr", count: 1
    assert_select "tbody tr", text: /#{@pending_artist.username}/
  end

  test "admin user editing capabilities" do
    sign_in_as(@admin_user)
    
    # Edit user details
    get edit_admin_user_path(@pending_artist)
    assert_response :success
    assert_select "form"
    assert_select "input[name='user[username]']"
    assert_select "input[name='user[email_address]']"
    assert_select "select[name='user[role]']"
    assert_select "input[type='checkbox'][name='user[approved]']"
    assert_select "input[type='checkbox'][name='user[admin]']"
    
    # Update user information
    patch admin_user_path(@pending_artist), params: {
      user: {
        username: "updated_artist",
        email_address: "<EMAIL>",
        approved: true,
        admin: false
      }
    }
    
    assert_redirected_to admin_user_path(@pending_artist)
    follow_redirect!
    assert_match /successfully updated/, flash[:notice]
    
    @pending_artist.reload
    assert_equal "updated_artist", @pending_artist.username
    assert_equal "<EMAIL>", @pending_artist.email_address
    assert @pending_artist.approved?
    assert_not @pending_artist.admin?
  end

  test "admin user deletion with restrictions" do
    sign_in_as(@admin_user)
    
    # Can delete regular user
    assert_difference 'User.count', -1 do
      delete admin_user_path(@pending_client)
    end
    
    assert_redirected_to admin_users_path
    follow_redirect!
    assert_match /successfully deleted/, flash[:notice]
    
    # Create another admin user
    other_admin = create(:user, :client, :approved, admin: true)
    
    # Cannot delete admin user
    assert_no_difference 'User.count' do
      delete admin_user_path(other_admin)
    end
    
    assert_redirected_to admin_users_path
    follow_redirect!
    assert_match /Cannot delete admin users/, flash[:alert]
  end

  test "non-admin access restrictions" do
    # Regular approved user cannot access admin area
    sign_in_as(@approved_client)
    
    get admin_dashboard_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
    
    get admin_users_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
    
    get admin_user_path(@pending_artist)
    assert_redirected_to root_path
    
    # Pending user cannot access admin area
    sign_in_as(@pending_artist)
    
    get admin_dashboard_path
    assert_redirected_to root_path
    assert_equal "Access denied. Admin privileges required.", flash[:alert]
    
    # Unauthenticated user redirected to login
    delete session_path
    
    get admin_dashboard_path
    assert_redirected_to new_session_path
  end

  test "admin dashboard statistics and overview" do
    sign_in_as(@admin_user)
    
    get admin_dashboard_path
    assert_response :success
    
    # Should show pending count
    assert_select "div", text: /2.*pending/i  # 2 pending users
    
    # Should show total users
    assert_select "div", text: /#{User.count}.*total/i
    
    # Should have links to manage different resources
    assert_select "a[href='#{admin_users_path}']"
    assert_select "a[href='#{admin_posts_path}']"
    assert_select "a[href='#{admin_settings_path}']"
  end

  test "approval notification flow" do
    # Mock mailer to verify notifications are sent
    assert_enqueued_jobs 1, only: ActionMailer::MailDeliveryJob do
      sign_in_as(@admin_user)
      patch approve_admin_user_path(@pending_artist)
    end
    
    @pending_artist.reload
    assert @pending_artist.approved?
    
    # User should be able to log in and access platform
    sign_in_as(@pending_artist)
    
    get posts_path
    assert_response :success
    
    get new_post_path
    assert_response :success
  end

  test "re-approval after rejection" do
    sign_in_as(@admin_user)
    
    # First reject the user
    patch reject_admin_user_path(@pending_artist)
    @pending_artist.reload
    assert_not @pending_artist.approved?
    
    # Then approve them
    patch approve_admin_user_path(@pending_artist)
    @pending_artist.reload
    assert @pending_artist.approved?
    
    # Should only send notification if user was previously unapproved
    # This tests the logic in the controller that checks `was_unapproved`
    assert @pending_artist.approved?
  end

  test "admin management of posts and content" do
    # Create some posts
    artist_post = create(:post, artist_profile: @approved_artist.artist_profile)
    
    sign_in_as(@admin_user)
    
    # Admin can view posts management
    get admin_posts_path
    assert_response :success
    
    # Admin can view specific post
    get admin_post_path(artist_post)
    assert_response :success
    
    # Admin can edit posts if needed
    get edit_admin_post_path(artist_post)
    assert_response :success
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end