require "test_helper"

class UserOnboardingFlowTest < ActionDispatch::IntegrationTest
  def setup
    @existing_artist = create(:user, :artist, :approved)
    @existing_client = create(:user, :client, :approved)
    @specialty_traditional = create(:specialty, :traditional)
    @specialty_japanese = create(:specialty, :japanese)
    create(:site_setting, :no_approval_required)
  end

  test "complete new client onboarding journey" do
    # Start at home page
    get root_path
    assert_response :success
    assert_select "a", text: /Sign up/i
    
    # Navigate to signup
    get new_registration_path
    assert_response :success
    assert_select "form"
    
    # Complete client registration
    assert_difference ['User.count', 'ClientProfile.count'], 1 do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "newclient",
          password: "password123",
          password_confirmation: "password123",
          role: "client",
          name: "Jane Client",
          location: "Austin, TX"
        }
      }
    end
    
    # Should be redirected to root and auto-signed in
    assert_redirected_to root_path
    follow_redirect!
    assert_match /can start using/, flash[:notice]
    
    new_client = User.find_by(email_address: "<EMAIL>")
    assert new_client.approved?
    assert new_client.client?
    
    # Explore artists
    get artists_path
    assert_response :success
    assert_select ".artist-card", minimum: 1
    
    # View specific artist profile
    get artist_path(@existing_artist.artist_profile)
    assert_response :success
    assert_select "h1", text: /#{@existing_artist.artist_profile.name}/
    
    # Follow the artist
    assert_difference 'Follow.count', 1 do
      post follows_path, params: {
        artist_profile_id: @existing_artist.artist_profile.id
      }
    end
    
    assert_redirected_to artist_path(@existing_artist.artist_profile)
    follow_redirect!
    assert_match /following/, flash[:notice]
    
    # Create a post by the artist and interact with it
    post = create(:post, artist_profile: @existing_artist.artist_profile)
    
    get posts_path
    assert_response :success
    assert_select ".post", minimum: 1
    
    # View the post
    get post_path(post)
    assert_response :success
    
    # Like the post
    assert_difference 'Like.count', 1 do
      post post_likes_path(post)
    end
    
    assert_redirected_to post_path(post)
    follow_redirect!
    assert_select "span", text: "1 like"
    
    # Comment on the post
    assert_difference 'Comment.count', 1 do
      post post_comments_path(post), params: {
        comment: {
          content: "Amazing work! I love this style."
        }
      }
    end
    
    assert_redirected_to post_path(post)
    follow_redirect!
    assert_select "div", text: /Amazing work! I love this style/
    
    # Create inspiration board
    assert_difference 'InspirationBoard.count', 1 do
      post inspiration_boards_path, params: {
        inspiration_board: {
          name: "My Tattoo Ideas",
          privacy: true
        }
      }
    end
    
    inspiration_board = InspirationBoard.last
    assert_equal new_client, inspiration_board.user
    assert_equal "My Tattoo Ideas", inspiration_board.name
    assert inspiration_board.privacy?
    
    # Add post to inspiration board
    assert_difference 'InspirationBoardItem.count', 1 do
      post inspiration_board_items_path, params: {
        inspiration_board_item: {
          inspiration_board_id: inspiration_board.id,
          post_id: post.id,
          notes: "Love this style for my next piece"
        }
      }
    end
    
    # Check profile page shows activity
    get client_path(new_client.client_profile)
    assert_response :success
    assert_select "h1", text: /Jane Client/
    
    # Test messaging capability
    get new_message_path(recipient_id: @existing_artist.id)
    assert_response :success
    
    assert_difference ['Conversation.count', 'Message.count'], 1 do
      post messages_path, params: {
        message: {
          recipient_id: @existing_artist.id,
          content: "Hi! I'm interested in booking a tattoo consultation."
        }
      }
    end
    
    conversation = Conversation.last
    assert_includes [new_client.id, @existing_artist.id], conversation.user1_id
    assert_includes [new_client.id, @existing_artist.id], conversation.user2_id
    
    message = Message.last
    assert_equal new_client, message.sender
    assert_equal "Hi! I'm interested in booking a tattoo consultation.", message.content
  end

  test "complete new artist onboarding journey" do
    # Navigate to signup
    get new_registration_path
    assert_response :success
    
    # Complete artist registration with full profile
    assert_difference ['User.count', 'ArtistProfile.count'], 1 do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "newartist",
          password: "password123",
          password_confirmation: "password123",
          role: "artist",
          name: "John Artist",
          biography: "Experienced tattoo artist with 10+ years in the industry. Specializing in traditional and Japanese styles.",
          location: "Denver, CO",
          contact_email: "<EMAIL>",
          instagram_url: "https://instagram.com/johnartist",
          website_url: "https://johnartist.com",
          booking_link: "https://booking.johnartist.com",
          specialty_ids: [@specialty_traditional.id.to_s, @specialty_japanese.id.to_s]
        }
      }
    end
    
    # Should be redirected and auto-signed in
    assert_redirected_to root_path
    follow_redirect!
    
    new_artist = User.find_by(email_address: "<EMAIL>")
    artist_profile = new_artist.artist_profile
    
    # Verify profile creation
    assert_equal "John Artist", artist_profile.name
    assert_equal 2, artist_profile.specialties.count
    assert_includes artist_profile.specialties, @specialty_traditional
    assert_includes artist_profile.specialties, @specialty_japanese
    
    # Visit artist's own profile
    get artist_path(artist_profile)
    assert_response :success
    assert_select "h1", text: /John Artist/
    assert_select "p", text: /Experienced tattoo artist/
    
    # Create first post
    get new_post_path
    assert_response :success
    assert_select "form"
    
    assert_difference 'Post.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "text",
          body: "Just joined the platform! Excited to share my work and connect with clients."
        }
      }
    end
    
    assert_redirected_to posts_path
    follow_redirect!
    
    first_post = Post.last
    assert_equal artist_profile, first_post.artist_profile
    assert first_post.text?
    assert_select ".post", text: /Just joined the platform/
    
    # Create an image post
    assert_difference 'Post.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "image",
          caption: "One of my recent traditional pieces",
          images: [fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')]
        }
      }
    end
    
    assert_redirected_to posts_path
    image_post = Post.last
    assert image_post.image?
    assert_equal "One of my recent traditional pieces", image_post.caption
    assert image_post.images.attached?
    
    # View all posts to see them in feed
    get posts_path
    assert_response :success
    assert_select ".post", count: 2
    
    # Respond to client interaction
    existing_client_comment = create(:comment, 
      post: image_post, 
      user: @existing_client,
      content: "Beautiful work! Do you take bookings?"
    )
    
    get post_path(image_post)
    assert_response :success
    assert_select "div", text: /Beautiful work! Do you take bookings/
    
    # Reply to comment
    assert_difference 'Comment.count', 1 do
      post post_comments_path(image_post), params: {
        comment: {
          content: "Thank you! Yes, I'm currently taking bookings. Please check my website for availability."
        }
      }
    end
    
    # Check messages from potential clients
    get conversations_path
    assert_response :success
    
    # Test receiving a message from a client
    conversation = create(:conversation, user1: @existing_client, user2: new_artist)
    create(:message, conversation: conversation, sender: @existing_client, 
           content: "Hi John! I saw your work and I'm interested in booking a traditional piece.")
    
    get conversation_path(conversation)
    assert_response :success
    assert_select "div", text: /I saw your work and I'm interested/
    
    # Reply to the message
    assert_difference 'Message.count', 1 do
      post conversation_messages_path(conversation), params: {
        message: {
          content: "Thanks for reaching out! I'd love to work with you. What kind of traditional piece are you thinking?"
        }
      }
    end
    
    assert_redirected_to conversation_path(conversation)
    follow_redirect!
    assert_select "div", text: /What kind of traditional piece are you thinking/
    
    # View artist dashboard/profile to see overview
    get artist_path(artist_profile)
    assert_response :success
    assert_select "h1", text: /John Artist/
    assert_select "div", text: /2 posts/i
  end

  test "onboarding with approval required flow" do
    # Set up approval requirement
    create(:site_setting, approval_required: true)
    
    # Artist signs up
    assert_difference 'User.count', 1 do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "pendingartist",
          password: "password123",
          password_confirmation: "password123",
          role: "artist",
          name: "Pending Artist"
        }
      }
    end
    
    assert_redirected_to root_path
    follow_redirect!
    assert_match /pending approval/, flash[:notice]
    
    pending_user = User.find_by(email_address: "<EMAIL>")
    assert_not pending_user.approved?
    
    # User should have limited access
    post = create(:post, artist_profile: @existing_artist.artist_profile)
    
    post post_likes_path(post)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    # Cannot create posts
    get new_post_path
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    # Simulate admin approval
    pending_user.update!(approved: true)
    
    # Now user should have full access
    post post_likes_path(post)
    assert_redirected_to post_path(post)
    
    get new_post_path
    assert_response :success
  end

  test "onboarding with existing content discovery" do
    # Create existing content for new user to discover
    artist1 = create(:user, :artist, :approved)
    artist2 = create(:user, :artist, :approved)
    
    post1 = create(:post, artist_profile: artist1.artist_profile)
    post2 = create(:post, artist_profile: artist2.artist_profile)
    
    create(:comment, post: post1, user: @existing_client, content: "Great work!")
    create(:like, post: post1, user: @existing_client)
    
    # New client signs up
    assert_difference 'User.count', 1 do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "discoveryclient",
          password: "password123",
          password_confirmation: "password123",
          role: "client",
          name: "Discovery Client",
          location: "Chicago, IL"
        }
      }
    end
    
    new_client = User.find_by(email_address: "<EMAIL>")
    
    # Explore existing content
    get posts_path
    assert_response :success
    assert_select ".post", minimum: 2
    
    # View popular/liked content
    get post_path(post1)
    assert_response :success
    assert_select "span", text: "1 like"
    assert_select "span", text: "1 comment"
    
    # Browse artists
    get artists_path
    assert_response :success
    assert_select ".artist-card", minimum: 2
    
    # Follow multiple artists
    assert_difference 'Follow.count', 2 do
      post follows_path, params: { artist_profile_id: artist1.artist_profile.id }
      post follows_path, params: { artist_profile_id: artist2.artist_profile.id }
    end
    
    # Check following list
    get client_path(new_client.client_profile)
    assert_response :success
    # Note: Following count display would depend on view implementation
  end

  test "incomplete onboarding recovery" do
    # User starts signup but abandons
    get new_registration_path
    assert_response :success
    
    # User returns later and completes
    assert_difference 'User.count', 1 do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "recoveryuser",
          password: "password123",
          password_confirmation: "password123",
          role: "client",
          name: "Recovery User",
          location: "Miami, FL"
        }
      }
    end
    
    recovery_user = User.find_by(email_address: "<EMAIL>")
    assert recovery_user.approved?
    assert recovery_user.client_profile.present?
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end