require "test_helper"

class AuthenticationFlowTest < ActionDispatch::IntegrationTest
  def setup
    @client_user = create(:user, :client, :approved)
    @artist_user = create(:user, :artist, :approved)
    @unapproved_user = create(:user, :client, approved: false)
    @specialty = create(:specialty, :traditional)
  end

  test "complete client signup, login, and logout flow" do
    # Test signup page
    get new_registration_path
    assert_response :success
    assert_select "form"
    assert_select "input[name='user[email_address]']"
    assert_select "input[name='user[username]']"
    assert_select "input[name='user[password]']"
    assert_select "input[type='radio'][value='client']"
    
    # Create new client user
    assert_difference 'User.count', 1 do
      assert_difference 'ClientProfile.count', 1 do
        post registrations_path, params: {
          user: {
            email_address: "<EMAIL>",
            username: "newclient",
            password: "password123",
            password_confirmation: "password123",
            role: "client",
            name: "New Client",
            location: "Portland, OR"
          }
        }
      end
    end
    
    assert_redirected_to root_path
    follow_redirect!
    
    new_user = User.find_by(email_address: "<EMAIL>")
    assert new_user.client?
    assert new_user.client_profile.present?
    assert_equal "New Client", new_user.client_profile.name
    assert_equal "Portland, OR", new_user.client_profile.location
    
    # User should be automatically signed in after registration
    assert_select "a", text: /Sign out/i
    
    # Test logout
    delete session_path
    assert_redirected_to new_session_path
    follow_redirect!
    
    # Should see login form after logout
    assert_select "form"
    assert_select "input[name='email_address']"
    assert_select "input[name='password']"
    
    # Test login with correct credentials
    post session_path, params: {
      email_address: "<EMAIL>",
      password: "password123"
    }
    
    assert_redirected_to client_path(new_user.client_profile)
    follow_redirect!
    assert_select "h1", text: /#{new_user.client_profile.name}/
  end

  test "complete artist signup with specialties flow" do
    get new_registration_path
    assert_response :success
    
    # Create artist with specialties
    assert_difference 'User.count', 1 do
      assert_difference 'ArtistProfile.count', 1 do
        post registrations_path, params: {
          user: {
            email_address: "<EMAIL>",
            username: "newartist",
            password: "password123",
            password_confirmation: "password123",
            role: "artist",
            name: "New Artist",
            biography: "Passionate tattoo artist specializing in traditional work",
            location: "Seattle, WA",
            contact_email: "<EMAIL>",
            instagram_url: "https://instagram.com/newartist",
            website_url: "https://newartist.com",
            booking_link: "https://booking.newartist.com",
            specialty_ids: [@specialty.id.to_s]
          }
        }
      end
    end
    
    assert_redirected_to root_path
    
    new_artist = User.find_by(email_address: "<EMAIL>")
    assert new_artist.artist?
    
    artist_profile = new_artist.artist_profile
    assert_equal "New Artist", artist_profile.name
    assert_equal "Passionate tattoo artist specializing in traditional work", artist_profile.biography
    assert_equal "Seattle, WA", artist_profile.location
    assert_equal "<EMAIL>", artist_profile.contact_email
    assert_equal "https://instagram.com/newartist", artist_profile.instagram_url
    assert_equal "https://newartist.com", artist_profile.website_url
    assert_equal "https://booking.newartist.com", artist_profile.booking_link
    assert_includes artist_profile.specialties, @specialty
  end

  test "existing user login and logout flow" do
    # Test login page
    get new_session_path
    assert_response :success
    assert_select "form"
    assert_select "input[name='email_address']"
    assert_select "input[name='password']"
    
    # Test successful login
    post session_path, params: {
      email_address: @client_user.email_address,
      password: "password123"
    }
    
    assert_redirected_to client_path(@client_user.client_profile)
    follow_redirect!
    
    # Should be on user's profile page
    assert_select "h1", text: /#{@client_user.client_profile.name}/
    assert_select "a", text: /Sign out/i
    
    # Test logout
    delete session_path
    assert_redirected_to new_session_path
    follow_redirect!
    
    # Should see login form
    assert_select "form"
    assert_select "input[name='email_address']"
    
    # Test artist login redirects to artist profile
    post session_path, params: {
      email_address: @artist_user.email_address,
      password: "password123"
    }
    
    assert_redirected_to artist_path(@artist_user.artist_profile)
  end

  test "invalid login attempts" do
    # Test with non-existent email
    post session_path, params: {
      email_address: "<EMAIL>",
      password: "password123"
    }
    
    assert_redirected_to new_session_path
    follow_redirect!
    assert_equal "Try another email address or password.", flash[:alert]
    
    # Test with wrong password
    post session_path, params: {
      email_address: @client_user.email_address,
      password: "wrongpassword"
    }
    
    assert_redirected_to new_session_path
    follow_redirect!
    assert_equal "Try another email address or password.", flash[:alert]
  end

  test "authenticated user redirect behavior" do
    # Login as client
    sign_in_as(@client_user)
    
    # Should redirect from login page to profile
    get new_session_path
    assert_redirected_to client_path(@client_user.client_profile)
    
    # Should redirect from signup page to profile
    get new_registration_path
    assert_redirected_to client_path(@client_user.client_profile)
    
    # Logout and login as artist
    delete session_path
    sign_in_as(@artist_user)
    
    # Should redirect to artist profile
    get new_session_path
    assert_redirected_to artist_path(@artist_user.artist_profile)
  end

  test "signup validation errors" do
    get new_registration_path
    assert_response :success
    
    # Test missing required fields
    assert_no_difference 'User.count' do
      post registrations_path, params: {
        user: {
          email_address: "",
          username: "",
          password: "",
          role: "client"
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select "form"
    
    # Test duplicate email
    assert_no_difference 'User.count' do
      post registrations_path, params: {
        user: {
          email_address: @client_user.email_address,
          username: "uniqueusername",
          password: "password123",
          password_confirmation: "password123",
          role: "client",
          name: "Test User"
        }
      }
    end
    
    assert_response :unprocessable_entity
    
    # Test duplicate username
    assert_no_difference 'User.count' do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: @client_user.username,
          password: "password123",
          password_confirmation: "password123",
          role: "client",
          name: "Test User"
        }
      }
    end
    
    assert_response :unprocessable_entity
    
    # Test password confirmation mismatch
    assert_no_difference 'User.count' do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "uniqueuser",
          password: "password123",
          password_confirmation: "differentpassword",
          role: "client",
          name: "Test User"
        }
      }
    end
    
    assert_response :unprocessable_entity
  end

  test "approval required vs auto approval flow" do
    # Mock site setting to require approval
    site_setting = create(:site_setting, approval_required: true)
    
    # Regular signup should not be approved
    assert_difference 'User.count', 1 do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "pendinguser",
          password: "password123",
          password_confirmation: "password123",
          role: "client",
          name: "Pending User"
        }
      }
    end
    
    assert_redirected_to root_path
    follow_redirect!
    assert_match /pending approval/, flash[:notice]
    
    pending_user = User.find_by(email_address: "<EMAIL>")
    assert_not pending_user.approved?
    
    # Signup with invite code should be auto-approved
    assert_difference 'User.count', 1 do
      post registrations_path, params: {
        user: {
          email_address: "<EMAIL>",
          username: "approveduser",
          password: "password123",
          password_confirmation: "password123",
          role: "artist",
          name: "Auto Approved Artist"
        },
        invite_code: "artist-early-access-2025"
      }
    end
    
    assert_redirected_to root_path
    follow_redirect!
    assert_match /can start using/, flash[:notice]
    
    approved_user = User.find_by(email_address: "<EMAIL>")
    assert approved_user.approved?
  end

  test "unapproved user access restrictions" do
    sign_in_as(@unapproved_user)
    
    # Should be able to access basic pages
    get root_path
    assert_response :success
    
    # Should be redirected from restricted actions
    post = create(:post, artist_profile: @artist_user.artist_profile)
    
    post post_likes_path(post)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    post post_comments_path(post), params: {
      comment: { content: "Test comment" }
    }
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end