require "test_helper"

class ProfileManagementFlowTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @client_user = create(:user, :client, :approved)
    @other_client = create(:user, :client, :approved)
    @specialty_traditional = create(:specialty, :traditional)
    @specialty_japanese = create(:specialty, :japanese)
    @specialty_realism = create(:specialty, title: "Realism", description: "Photorealistic tattoo work")
  end

  test "complete artist profile management flow" do
    sign_in_as(@artist_user)
    
    # View current profile
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    assert_select "h1", text: /#{@artist_user.artist_profile.name}/
    assert_select "a", text: /Edit Profile/i
    
    # Edit profile page
    get edit_artist_path(@artist_user.artist_profile)
    assert_response :success
    assert_select "form"
    assert_select "input[name='artist_profile[name]']"
    assert_select "textarea[name='artist_profile[biography]']"
    assert_select "input[name='artist_profile[location]']"
    assert_select "input[name='artist_profile[contact_email]']"
    assert_select "input[name='artist_profile[instagram_url]']"
    assert_select "input[name='artist_profile[website_url]']"
    assert_select "input[name='artist_profile[booking_link]']"
    assert_select "input[type='checkbox'][name='artist_profile[available]']"
    assert_select "input[type='checkbox'][name='artist_profile[messages_enabled]']"
    
    # Update profile information
    patch artist_path(@artist_user.artist_profile), params: {
      artist_profile: {
        name: "Updated Artist Name",
        biography: "Updated biography with more details about my artistic journey and specializations in traditional and neo-traditional styles.",
        location: "Updated City, ST",
        contact_email: "<EMAIL>",
        instagram_url: "https://instagram.com/updatedartist",
        website_url: "https://updatedartist.com",
        booking_link: "https://booking.updatedartist.com",
        available: false,
        messages_enabled: false,
        specialty_ids: [@specialty_traditional.id, @specialty_japanese.id]
      }
    }
    
    assert_redirected_to artist_path(@artist_user.artist_profile)
    follow_redirect!
    assert_match /successfully updated/, flash[:notice]
    
    # Verify updates
    @artist_user.artist_profile.reload
    assert_equal "Updated Artist Name", @artist_user.artist_profile.name
    assert_equal "Updated biography with more details", @artist_user.artist_profile.biography[0..30]
    assert_equal "Updated City, ST", @artist_user.artist_profile.location
    assert_equal "<EMAIL>", @artist_user.artist_profile.contact_email
    assert_not @artist_user.artist_profile.available?
    assert_not @artist_user.artist_profile.messages_enabled?
    
    # Verify specialty updates
    assert_equal 2, @artist_user.artist_profile.specialties.count
    assert_includes @artist_user.artist_profile.specialties, @specialty_traditional
    assert_includes @artist_user.artist_profile.specialties, @specialty_japanese
    
    # Verify changes appear on profile page
    assert_select "h1", text: /Updated Artist Name/
    assert_select "p", text: /Updated biography with more details/
    assert_select "div", text: /Updated City, ST/
    assert_select "div", text: /Traditional/
    assert_select "div", text: /Japanese/
    assert_select "div", text: /Currently unavailable/i
  end

  test "client profile management flow" do
    sign_in_as(@client_user)
    
    # View current profile
    get client_path(@client_user.client_profile)
    assert_response :success
    assert_select "h1", text: /#{@client_user.client_profile.name}/
    assert_select "a", text: /Edit Profile/i
    
    # Edit profile
    get edit_client_path(@client_user.client_profile)
    assert_response :success
    assert_select "form"
    assert_select "input[name='client_profile[name]']"
    assert_select "input[name='client_profile[location]']"
    
    # Update client profile
    patch client_path(@client_user.client_profile), params: {
      client_profile: {
        name: "Updated Client Name",
        location: "New City, CA"
      }
    }
    
    assert_redirected_to client_path(@client_user.client_profile)
    follow_redirect!
    assert_match /successfully updated/, flash[:notice]
    
    # Verify updates
    @client_user.client_profile.reload
    assert_equal "Updated Client Name", @client_user.client_profile.name
    assert_equal "New City, CA", @client_user.client_profile.location
    
    assert_select "h1", text: /Updated Client Name/
    assert_select "div", text: /New City, CA/
  end

  test "artist portfolio management" do
    sign_in_as(@artist_user)
    
    # View artist profile with portfolio section
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    
    # Create portfolio items through posts
    get new_post_path
    assert_response :success
    
    # Create image post that can be added to portfolio
    assert_difference 'Post.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "image",
          caption: "My latest traditional piece - eagle and roses",
          images: [fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')],
          in_portfolio: true
        }
      }
    end
    
    portfolio_post = Post.last
    assert portfolio_post.in_portfolio?
    
    # View updated profile with portfolio item
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    # Portfolio display would depend on view implementation
    
    # Edit existing post to remove from portfolio
    get edit_post_path(portfolio_post)
    assert_response :success
    assert_select "input[type='checkbox'][name='post[in_portfolio]']"
    
    patch post_path(portfolio_post), params: {
      post: {
        caption: "Updated caption for my traditional piece",
        in_portfolio: false
      }
    }
    
    assert_redirected_to posts_path
    portfolio_post.reload
    assert_not portfolio_post.in_portfolio?
    assert_equal "Updated caption for my traditional piece", portfolio_post.caption
  end

  test "profile visibility and privacy settings" do
    sign_in_as(@artist_user)
    
    # Test availability toggle
    @artist_user.artist_profile.update!(available: false)
    
    # Other users should see unavailable status
    sign_in_as(@client_user)
    
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    assert_select "div", text: /Currently unavailable/i
    
    # Message button should be hidden or disabled when messages are disabled
    @artist_user.artist_profile.update!(messages_enabled: false)
    
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    assert_select "a", text: /Message/i, count: 0
    
    # Trying to message should redirect with error
    get new_message_path(recipient_id: @artist_user.id)
    assert_redirected_to root_path
    assert_equal "You cannot send messages to this user.", flash[:alert]
    
    # Re-enable messages
    @artist_user.artist_profile.update!(messages_enabled: true)
    
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    assert_select "a", text: /Message/i, count: 1
  end

  test "profile validation and error handling" do
    sign_in_as(@artist_user)
    
    # Try to update with invalid data
    patch artist_path(@artist_user.artist_profile), params: {
      artist_profile: {
        name: "",  # Required field
        contact_email: "invalid-email",  # Invalid format
        instagram_url: "not-a-url",  # Invalid URL
        website_url: "also-not-a-url"  # Invalid URL
      }
    }
    
    assert_response :unprocessable_entity
    assert_select "form"
    # Error messages would depend on view implementation
    
    # Profile should not be updated
    @artist_user.artist_profile.reload
    assert_not_equal "", @artist_user.artist_profile.name
  end

  test "specialty management for artists" do
    sign_in_as(@artist_user)
    
    # Initially no specialties
    assert_equal 0, @artist_user.artist_profile.specialties.count
    
    # Add specialties
    patch artist_path(@artist_user.artist_profile), params: {
      artist_profile: {
        specialty_ids: [@specialty_traditional.id, @specialty_realism.id]
      }
    }
    
    assert_redirected_to artist_path(@artist_user.artist_profile)
    
    @artist_user.artist_profile.reload
    assert_equal 2, @artist_user.artist_profile.specialties.count
    assert_includes @artist_user.artist_profile.specialties, @specialty_traditional
    assert_includes @artist_user.artist_profile.specialties, @specialty_realism
    
    # Remove one specialty
    patch artist_path(@artist_user.artist_profile), params: {
      artist_profile: {
        specialty_ids: [@specialty_traditional.id]
      }
    }
    
    @artist_user.artist_profile.reload
    assert_equal 1, @artist_user.artist_profile.specialties.count
    assert_includes @artist_user.artist_profile.specialties, @specialty_traditional
    assert_not_includes @artist_user.artist_profile.specialties, @specialty_realism
    
    # Remove all specialties
    patch artist_path(@artist_user.artist_profile), params: {
      artist_profile: {
        specialty_ids: []
      }
    }
    
    @artist_user.artist_profile.reload
    assert_equal 0, @artist_user.artist_profile.specialties.count
  end

  test "profile access control" do
    # Users can only edit their own profiles
    sign_in_as(@client_user)
    
    # Cannot edit other user's profile
    get edit_artist_path(@artist_user.artist_profile)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    # Cannot update other user's profile
    patch artist_path(@artist_user.artist_profile), params: {
      artist_profile: { name: "Hacked Name" }
    }
    assert_redirected_to root_path
    
    # Artist profile should not be changed
    @artist_user.artist_profile.reload
    assert_not_equal "Hacked Name", @artist_user.artist_profile.name
  end

  test "profile image upload and management" do
    sign_in_as(@artist_user)
    
    # This test is skipped because avatar field doesn't exist in current model
    skip "Avatar field not implemented in current model"
  end

  test "following and follower management" do
    sign_in_as(@client_user)
    
    # Follow an artist
    assert_difference 'Follow.count', 1 do
      post follows_path, params: {
        artist_profile_id: @artist_user.artist_profile.id
      }
    end
    
    assert_redirected_to artist_path(@artist_user.artist_profile)
    follow_redirect!
    assert_match /following/, flash[:notice]
    
    # Verify follow relationship
    assert Follow.exists?(client_profile: @client_user.client_profile, artist_profile: @artist_user.artist_profile)
    
    # View artist profile should show follower count
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    # Follower count display would depend on view implementation
    
    # View client profile should show following count
    get client_path(@client_user.client_profile)
    assert_response :success
    # Following count display would depend on view implementation
    
    # Unfollow
    follow = Follow.find_by(client_profile: @client_user.client_profile, artist_profile: @artist_user.artist_profile)
    
    assert_difference 'Follow.count', -1 do
      delete follow_path(follow)
    end
    
    assert_redirected_to artist_path(@artist_user.artist_profile)
    follow_redirect!
    assert_match /unfollowed/, flash[:notice]
  end

  test "inspiration board integration with profile" do
    sign_in_as(@client_user)
    
    # Create inspiration board
    assert_difference 'InspirationBoard.count', 1 do
      post inspiration_boards_path, params: {
        inspiration_board: {
          name: "My Tattoo Ideas",
          privacy: false  # Public board
        }
      }
    end
    
    inspiration_board = InspirationBoard.last
    
    # Add posts to board
    artist_post = create(:post, artist_profile: @artist_user.artist_profile)
    
    assert_difference 'InspirationBoardItem.count', 1 do
      post inspiration_board_items_path, params: {
        inspiration_board_item: {
          inspiration_board_id: inspiration_board.id,
          post_id: artist_post.id,
          notes: "Love this style for my next piece"
        }
      }
    end
    
    # View client profile should show inspiration boards
    get client_path(@client_user.client_profile)
    assert_response :success
    # Board display would depend on view implementation
    
    # Other users can view public boards
    sign_in_as(@other_client)
    
    get client_path(@client_user.client_profile)
    assert_response :success
    # Public boards should be visible
  end

  test "profile activity and statistics" do
    sign_in_as(@artist_user)
    
    # Create some posts
    3.times do |i|
      create(:post, artist_profile: @artist_user.artist_profile, caption: "Test post #{i + 1}")
    end
    
    # Create some interactions
    artist_post = @artist_user.artist_profile.posts.first
    create(:like, post: artist_post, user: @client_user)
    create(:comment, post: artist_post, user: @client_user, content: "Great work!")
    
    # View profile should show activity stats
    get artist_path(@artist_user.artist_profile)
    assert_response :success
    
    # Stats display would depend on view implementation
    # But we can verify the data exists
    assert_equal 3, @artist_user.artist_profile.posts.count
    assert_equal 1, artist_post.likes.count
    assert_equal 1, artist_post.comments.count
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end