require "test_helper"

class PostsFlowTest < ActionDispatch::IntegrationTest
  def setup
    @artist_user = create(:user, :artist, :approved)
    @artist_profile = @artist_user.artist_profile
    @client_user = create(:user, :client, :approved)
    @unapproved_user = create(:user, :client, approved: false)
  end

  test "full post creation and interaction flow" do
    # Artist logs in and creates a text post
    sign_in_as(@artist_user)
    
    get posts_path
    assert_response :success
    assert_select "a", text: "Create Post"
    
    get new_post_path
    assert_response :success
    assert_select "form"
    assert_select "input[type='radio'][value='text']"
    assert_select "input[type='radio'][value='image']"
    assert_select "input[type='radio'][value='gallery']"
    
    # Create a text post
    assert_difference 'Post.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "text",
          body: "This is my first text post about my latest tattoo work!"
        }
      }
    end
    
    assert_redirected_to posts_path
    follow_redirect!
    
    created_post = Post.last
    assert created_post.text?
    assert_equal "This is my first text post about my latest tattoo work!", created_post.body
    assert_equal @artist_profile, created_post.artist_profile
    
    # Verify post appears on index
    assert_select ".post", count: 1
    assert_select ".post", text: /This is my first text post/
    
    # Visit the individual post
    get post_path(created_post)
    assert_response :success
    assert_select "h1", text: /Portfolio Item/i, count: 0 # Make sure we're on post page
    assert_select "div", text: /This is my first text post/
    
    # Artist can edit their own post
    get edit_post_path(created_post)
    assert_response :success
    assert_select "form"
    assert_select "textarea[name='post[body]']"
    
    # Update the post
    patch post_path(created_post), params: {
      post: {
        body: "Updated: This is my updated text post!"
      }
    }
    
    assert_redirected_to posts_path
    created_post.reload
    assert_equal "Updated: This is my updated text post!", created_post.body
    
    # Client user logs in and interacts with the post
    sign_in_as(@client_user)
    
    get posts_path
    assert_response :success
    assert_select ".post", count: 1
    
    # Client can view post
    get post_path(created_post)
    assert_response :success
    
    # Client can like the post
    assert_difference 'Like.count', 1 do
      post post_likes_path(created_post)
    end
    
    assert_redirected_to post_path(created_post)
    follow_redirect!
    
    like = Like.last
    assert_equal @client_user, like.user
    assert_equal created_post, like.post
    
    # Client can comment on the post
    assert_difference 'Comment.count', 1 do
      post post_comments_path(created_post), params: {
        comment: {
          content: "Amazing work! Love the detail in this piece."
        }
      }
    end
    
    assert_redirected_to post_path(created_post)
    follow_redirect!
    
    comment = Comment.last
    assert_equal @client_user, comment.user
    assert_equal created_post, comment.post
    assert_equal "Amazing work! Love the detail in this piece.", comment.content
    
    # Verify comment appears on post page
    assert_select "div", text: /Amazing work! Love the detail/
    
    # Client can unlike the post
    assert_difference 'Like.count', -1 do
      delete post_like_path(created_post, like)
    end
    
    # Unapproved user cannot interact
    sign_in_as(@unapproved_user)
    
    get posts_path
    assert_response :success
    
    # Cannot like
    post post_likes_path(created_post)
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    # Cannot comment
    post post_comments_path(created_post), params: {
      comment: { content: "Test comment" }
    }
    assert_redirected_to root_path
    
    # Artist can delete their post
    sign_in_as(@artist_user)
    
    assert_difference 'Post.count', -1 do
      delete post_path(created_post)
    end
    
    assert_redirected_to posts_path
  end

  test "image post creation flow" do
    sign_in_as(@artist_user)
    
    get new_post_path
    assert_response :success
    
    # Create an image post
    assert_difference 'Post.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "image",
          caption: "Check out this sleeve I just finished!",
          images: [fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')]
        }
      }
    end
    
    assert_redirected_to posts_path
    
    created_post = Post.last
    assert created_post.image?
    assert_equal "Check out this sleeve I just finished!", created_post.caption
    assert_equal 1, created_post.images.count
    assert created_post.images.attached?
  end

  test "gallery post creation flow" do
    sign_in_as(@artist_user)
    
    get new_post_path
    assert_response :success
    
    # Create a gallery post
    assert_difference 'Post.count', 1 do
      post posts_path, params: {
        post: {
          post_type: "gallery",
          caption: "Before and after shots of this back piece",
          images: [
            fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
            fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg'),
            fixture_file_upload('test/fixtures/files/test.jpg', 'image/jpeg')
          ]
        }
      }
    end
    
    assert_redirected_to posts_path
    
    created_post = Post.last
    assert created_post.gallery?
    assert_equal "Before and after shots of this back piece", created_post.caption
    assert_equal 3, created_post.images.count
  end

  test "unauthorized access restrictions" do
    post = create(:post, artist_profile: @artist_profile)
    
    # Unauthenticated users can view posts
    get posts_path
    assert_response :success
    
    get post_path(post)
    assert_response :success
    
    # But cannot create posts
    get new_post_path
    assert_redirected_to new_session_path
    
    # Clients cannot create posts
    sign_in_as(@client_user)
    
    get new_post_path
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
    
    # Unapproved artists cannot create posts
    unapproved_artist = create(:user, :artist, approved: false)
    sign_in_as(unapproved_artist)
    
    get new_post_path
    assert_redirected_to root_path
    assert_equal "Access denied.", flash[:alert]
  end

  test "post validation errors display correctly" do
    sign_in_as(@artist_user)
    
    # Try to create invalid text post
    assert_no_difference 'Post.count' do
      post posts_path, params: {
        post: {
          post_type: "text",
          body: ""  # Invalid - body required for text posts
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select ".text-red-800", text: /There were 1 error/
    assert_select "li", text: /Body can't be blank for text posts/
    
    # Try to create invalid image post
    assert_no_difference 'Post.count' do
      post posts_path, params: {
        post: {
          post_type: "image",
          caption: "Caption but no image"
          # No images provided
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_select ".text-red-800", text: /There were 1 error/
    assert_select "li", text: /Images must have exactly one image for image posts/
  end

  private

  def sign_in_as(user)
    post session_url, params: { email_address: user.email_address, password: "password123" }
  end
end