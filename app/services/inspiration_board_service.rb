class InspirationBoardService
  def self.add_portfolio_item_to_board(inspiration_board, portfolio_item, notes: nil)
    return if inspiration_board.inspiration_board_items.exists?(portfolio_item: portfolio_item)
    
    inspiration_board.inspiration_board_items.create!(
      portfolio_item: portfolio_item,
      notes: notes
    )
  end

  def self.update_notes(inspiration_board_item, notes)
    inspiration_board_item.update!(notes: notes)
  end
end