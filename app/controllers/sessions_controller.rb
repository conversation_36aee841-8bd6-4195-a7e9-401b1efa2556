class SessionsController < ApplicationController
  allow_unauthenticated_access only: %i[ new create ]
  rate_limit to: 10, within: 3.minutes, only: :create, with: -> { redirect_to new_session_url, alert: "Try again later." }
  before_action :redirect_if_authenticated, only: [:new]

  def new
  end

  def create
    login = params[:login] || params[:email_address]
    password = params[:password]
    
    user = User.find_by_email(login)
    
    if user&.authenticate(password)
      start_new_session_for user
      redirect_to after_authentication_url
    else
      redirect_to new_session_path, alert: "Try another email address or password."
    end
  end

  def destroy
    terminate_session
    redirect_to new_session_path
  end

  private

  def redirect_if_authenticated
    if current_user
      if current_user.artist? && current_user.artist_profile
        redirect_to artist_path(current_user.artist_profile)
      elsif current_user.client? && current_user.client_profile
        redirect_to client_path(current_user.client_profile)
      else
        redirect_to root_path
      end
    end
  end
end
