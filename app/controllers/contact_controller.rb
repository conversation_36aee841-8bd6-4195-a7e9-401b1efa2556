class ContactController < ApplicationController
  optional_authentication
  
  def new
  end

  def create
    @name = params[:name]
    @email = params[:email]
    @subject = params[:subject]
    @message = params[:message]

    if @name.present? && @email.present? && @message.present?
      ContactMailer.new_message(@name, @email, @subject, @message).deliver_now
      redirect_to contact_path, notice: "Thank you for your message! We'll get back to you soon."
    else
      flash.now[:alert] = "Please fill in all required fields."
      render :new
    end
  end

  def show
    # Success page after form submission
  end
end