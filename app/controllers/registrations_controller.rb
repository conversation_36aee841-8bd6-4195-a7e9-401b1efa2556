class RegistrationsController < ApplicationController
  allow_unauthenticated_access
  before_action :redirect_if_authenticated, only: [:new, :new_artist, :new_client]
  
  def new
    @user = User.new
    @specialties = Style.all.order(:title)
    @invite_code = params[:invite_code]
    @auto_approve = invite_code_valid?(@invite_code)
  end

  def new_artist
    @user = User.new
    @specialties = Style.all.order(:title)
    @invite_code = params[:invite_code]
    @auto_approve = invite_code_valid?(@invite_code)
  end

  def new_client
    @user = User.new
    @invite_code = params[:invite_code]
    @auto_approve = invite_code_valid?(@invite_code)
  end

  def create
    @user = User.new(user_params)
    @specialties = Style.all.order(:title)
    @invite_code = params[:invite_code]
    @auto_approve = invite_code_valid?(@invite_code)

    # Set approval status
    @user.approved = @auto_approve || !SiteSetting.approval_required?

    if @user.save
      create_profile
      
      # Send welcome email
      UserNotificationMailer.welcome(@user).deliver_later
      
      Current.session = Session.create!(user: @user)
      redirect_to root_path, notice: approval_message
    else
      render :new, status: :unprocessable_entity
    end
  end

  def create_artist
    @user = User.new(user_params)
    @user.role = "artist"
    @specialties = Style.all.order(:title)
    @invite_code = params[:invite_code]
    @auto_approve = invite_code_valid?(@invite_code)

    # Set approval status
    @user.approved = @auto_approve || !SiteSetting.approval_required?

    if @user.save
      create_profile
      
      # Send welcome email
      UserNotificationMailer.welcome(@user).deliver_later
      
      Current.session = Session.create!(user: @user)
      redirect_to root_path, notice: approval_message
    else
      render :new_artist, status: :unprocessable_entity
    end
  end

  def create_client
    @user = User.new(user_params)
    @user.role = "client"
    @invite_code = params[:invite_code]
    @auto_approve = invite_code_valid?(@invite_code)

    # Set approval status
    @user.approved = @auto_approve || !SiteSetting.approval_required?

    if @user.save
      create_profile
      
      # Send welcome email
      UserNotificationMailer.welcome(@user).deliver_later
      
      Current.session = Session.create!(user: @user)
      redirect_to root_path, notice: approval_message
    else
      render :new_client, status: :unprocessable_entity
    end
  end

  private

  def user_params
    params.require(:user).permit(:email_address, :password, :password_confirmation, :role)
  end

  def profile_params
    params.require(:user).permit(:name, :biography, :location, :contact_email, :instagram_url, 
                                 :website_url, :booking_link, style_ids: [])
  end

  def create_profile
    if @user.artist?
      profile = @user.create_artist_profile!(
        name: profile_params[:name],
        biography: profile_params[:biography],
        location: profile_params[:location],
        contact_email: profile_params[:contact_email],
        instagram_url: profile_params[:instagram_url],
        website_url: profile_params[:website_url],
        booking_link: profile_params[:booking_link]
      )
      
      # Add selected styles
      if profile_params[:style_ids].present?
        style_ids = profile_params[:style_ids].reject(&:blank?)
        profile.style_ids = style_ids
      end
    else
      @user.create_client_profile!(
        name: profile_params[:name],
        location: profile_params[:location]
      )
    end
  end

  def invite_code_valid?(code)
    code.present? && code == "artist-early-access-2025"
  end

  def approval_message
    if @user.approved?
      "Welcome! Your account has been created and you can start using the platform."
    else
      "Your account has been created and is pending approval. You'll receive an email when approved."
    end
  end

  def redirect_if_authenticated
    if current_user
      if current_user.artist? && current_user.artist_profile
        redirect_to artist_path(current_user.artist_profile)
      elsif current_user.client? && current_user.client_profile
        redirect_to client_path(current_user.client_profile)
      else
        redirect_to root_path
      end
    end
  end
end
