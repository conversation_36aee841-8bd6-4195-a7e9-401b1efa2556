class InspirationBoardItemsController < ApplicationController
  before_action :require_approved_user
  before_action :set_profile, except: [:create]
  before_action :set_inspiration_board, only: [:create]
  before_action :set_inspiration_board_item, only: [:update, :destroy]
  before_action :ensure_owner, only: [:update, :destroy]

  def create
    # Handle both nested and global routes
    user = @profile_user || current_user
    @inspiration_board = user.inspiration_boards.find(params[:inspiration_board_id])
    
    # Handle portfolio items only
    if params[:portfolio_item_id].present?
      @source = PortfolioItem.find(params[:portfolio_item_id])
      
      # Check if item already exists in this board
      existing_item = @inspiration_board.inspiration_board_items.find_by(portfolio_item: @source)
      if existing_item
        respond_to do |format|
          format.html { redirect_back(fallback_location: @source, alert: 'Portfolio item is already saved to this board.') }
          format.json { render json: { error: 'Portfolio item is already saved to this board.' }, status: :unprocessable_entity }
        end
        return
      end
      
      @inspiration_board_item = @inspiration_board.inspiration_board_items.build(
        portfolio_item: @source,
        notes: params[:notes]
      )
      item_type = 'Portfolio item'
    else
      respond_to do |format|
        format.html { redirect_back(fallback_location: root_path, alert: 'No portfolio item specified to save.') }
        format.json { render json: { error: 'No portfolio item specified to save.' }, status: :unprocessable_entity }
      end
      return
    end
    
    if @inspiration_board_item.save
      respond_to do |format|
        format.html { redirect_back(fallback_location: @source, notice: "#{item_type} saved to inspiration board.") }
        format.json { render json: { message: "#{item_type} saved to inspiration board." }, status: :created }
      end
    else
      respond_to do |format|
        format.html { redirect_back(fallback_location: @source, alert: @inspiration_board_item.errors.full_messages.join(', ')) }
        format.json { render json: { error: @inspiration_board_item.errors.full_messages.join(', ') }, status: :unprocessable_entity }
      end
    end
  end

  def update
    if @inspiration_board_item.update(inspiration_board_item_params)
      redirect_back(fallback_location: @inspiration_board_item.inspiration_board, notice: 'Notes updated.')
    else
      redirect_back(fallback_location: @inspiration_board_item.inspiration_board, alert: 'Failed to update notes.')
    end
  end

  def destroy
    inspiration_board = @inspiration_board_item.inspiration_board
    @inspiration_board_item.destroy
    redirect_back(fallback_location: inspiration_board, notice: "Portfolio item removed from inspiration board.")
  end


  private

  def set_profile
    if params[:artist_slug]
      @artist_profile = ArtistProfile.friendly.find(params[:artist_slug])
      @profile_user = @artist_profile.user
    elsif params[:client_slug]
      @client_profile = ClientProfile.friendly.find(params[:client_slug])
      @profile_user = @client_profile.user
    end
  end

  def set_inspiration_board
    user = @profile_user || current_user
    @inspiration_board = user.inspiration_boards.find(params[:inspiration_board_id])
  end

  def set_inspiration_board_item
    user = @profile_user || current_user
    @inspiration_board = user.inspiration_boards.find(params[:inspiration_board_id])
    @inspiration_board_item = @inspiration_board.inspiration_board_items.find(params[:id])
  end

  def ensure_owner
    unless @inspiration_board_item.inspiration_board.user == current_user
      if @artist_profile
        redirect_to inspiration_artist_path(@artist_profile), alert: 'Access denied.'
      else
        redirect_to inspiration_client_path(@client_profile), alert: 'Access denied.'
      end
    end
  end

  def inspiration_board_item_params
    params.require(:inspiration_board_item).permit(:notes)
  end

  def require_approved_user
    unless current_user&.approved_or_admin?
      redirect_to root_path, alert: 'Access denied.'
    end
  end
end