class ClientsController < ApplicationController
  optional_authentication only: [:show, :index, :inspiration]
  before_action :set_client_profile, only: [:show, :inspiration]

  def show
  end

  def index
  end


  def inspiration
    @inspiration_boards = paginated_inspiration_boards
  end



  def load_more_inspiration
    @inspiration_boards = paginated_inspiration_boards
    render partial: 'inspiration_boards/inspiration_board_cards', locals: { inspiration_boards: @inspiration_boards }
  end


  private


  def paginated_inspiration_boards
    page = params[:page].to_i
    page = 1 if page <= 0
    per_page = 24
    offset = (page - 1) * per_page

    # Show all boards to owner, only public boards to others
    boards = if current_user == @client_profile.user
      @client_profile.user.inspiration_boards.includes(:inspiration_board_items).order(:name)
    else
      @client_profile.user.inspiration_boards.public_boards.includes(:inspiration_board_items).order(:name)
    end
    
    boards.offset(offset).limit(per_page)
  end


  def set_client_profile
    @client_profile = ClientProfile.friendly.find(params[:slug])
    
    # Check if user is approved
    unless @client_profile.user.approved?
      redirect_to root_path, alert: "Client profile not found."
    end
  rescue ActiveRecord::RecordNotFound
    redirect_to root_path, alert: "Client not found."
  end
end
