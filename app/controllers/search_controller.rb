class SearchController < ApplicationController
  optional_authentication

  def index
    @query = params[:q]&.strip
    @results = {}
    
    if @query.present?
      @results = perform_search(@query)
    end
  end

  private

  def perform_search(query)
    results = {
      artists: search_artists(query),
      portfolio_items: search_portfolio_items(query),
      inspiration_boards: search_inspiration_boards(query)
    }
    
    # Count total results for each category
    results.each do |category, items|
      results[category] = {
        items: items,
        count: items.count
      }
    end
    
    results
  end

  def search_artists(query)
    search_term = "%#{query.downcase}%"
    
    ArtistProfile.includes(:user, :styles, profile_photo_attachment: :blob)
                 .joins(:user)
                 .where(users: { approved: true })
                 .where(
                   "LOWER(COALESCE(artist_profiles.name, '')) LIKE ? OR 
                    LOWER(COALESCE(artist_profiles.biography, '')) LIKE ?",
                   search_term, search_term
                 )
                 .order(:name)
                 .limit(10)
  end

  def search_portfolio_items(query)
    search_term = "%#{query.downcase}%"
    
    PortfolioItem.includes(:artist_profile, image_attachment: :blob)
                 .joins(artist_profile: :user)
                 .where(users: { approved: true })
                 .where(
                   "LOWER(COALESCE(portfolio_items.caption, '')) LIKE ?",
                   search_term
                 )
                 .order(created_at: :desc)
                 .limit(10)
  end

  def search_inspiration_boards(query)
    search_term = "%#{query.downcase}%"
    
    # Only search public inspiration boards (privacy: false means public)
    InspirationBoard.includes(:user)
                   .joins(:user)
                   .where(users: { approved: true })
                   .where(privacy: false)
                   .where("LOWER(COALESCE(inspiration_boards.name, '')) LIKE ?", search_term)
                   .order(created_at: :desc)
                   .limit(10)
  end
end