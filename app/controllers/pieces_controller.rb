class PiecesController < ApplicationController
  optional_authentication

  def index
    @portfolio_items = search_portfolio_items
    @search_query = params[:search]
    
    # For stats
    @total_pieces = PortfolioItem.joins(artist_profile: :user).where(users: { approved: true }).count
  end

  def load_more
    @portfolio_items = search_portfolio_items

    respond_to do |format|
      format.json do
        html = render_to_string(partial: 'portfolio_items/portfolio_item_cards', locals: { portfolio_items: @portfolio_items }, formats: [:html])
        # Only has more if we got exactly the requested amount (24 items)
        # If we got fewer than 24, we've reached the end
        has_more = @portfolio_items.length == 24

        render json: {
          html: html,
          has_more: has_more
        }
      end
    end
  end

  private

  def search_portfolio_items
    scope = PortfolioItem.includes(:artist_profile, :styles, image_attachment: :blob)
                        .joins(artist_profile: :user)
                        .where(users: { approved: true })

    # Text search
    if params[:search].present?
      search_term = "%#{params[:search].downcase}%"
      
      # Search across caption and artist name
      scope = scope.where(
                     "LOWER(portfolio_items.caption) LIKE ? OR 
                      LOWER(artist_profiles.name) LIKE ?",
                     search_term, search_term
                   )
    end

    # Style filter
    if params[:style_filter].present?
      scope = scope.joins(:styles).where(styles: { id: params[:style_filter] })
    end

    # Location filter (from artist profile)
    if params[:location_filter].present?
      scope = scope.joins(:artist_profile).where(artist_profiles: { location: params[:location_filter] })
    end

    # Pagination
    page = params[:page].to_i
    page = 1 if page <= 0
    per_page = 24
    offset = (page - 1) * per_page

    scope.order(created_at: :desc).offset(offset).limit(per_page)
  end
end