class ArtistsController < ApplicationController
  optional_authentication
  before_action :require_artist_owner, only: [:edit, :update]
  before_action :set_artist_profile, only: [:show, :portfolio, :tags, :inspiration, :edit, :update]

  def show
  end

  def index
    @artist_profiles = search_artists
    @search_query = params[:search]
    
    # For stats
    @total_artists = ArtistProfile.joins(:user).where(users: { approved: true }).count
  end

  def load_more
    @artist_profiles = search_artists
    render partial: 'artists/artist_cards', locals: { artist_profiles: @artist_profiles }
  end

  def portfolio
    @portfolio_items = paginated_portfolio_items
  end


  def tags
    # Tags functionality removed
    redirect_to artist_path(@artist_profile)
  end


  def load_more_portfolio
    @portfolio_items = paginated_portfolio_items
    render partial: 'portfolio_items/portfolio_item_cards', locals: { portfolio_items: @portfolio_items }
  end


  def load_more_tags
    # Tags functionality removed
    head :not_found
  end



  def inspiration
    @inspiration_boards = @artist_profile.user.inspiration_boards.includes(:inspiration_board_items).order(created_at: :desc)
  end

  def messages
    # Messages functionality removed
    redirect_to artist_path(@artist_profile)
  end



  def edit
  end

  def update
    if @artist_profile.update(artist_profile_params)
      request.flash[:notice] = 'Profile updated successfully.'
      redirect_to artist_path(@artist_profile)
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def search_artists
    scope = ArtistProfile.includes(:user, :styles, :portfolio_items, profile_photo_attachment: :blob)
                        .joins(:user)
                        .where(users: { approved: true })

    # Text search
    if params[:search].present?
      search_term = "%#{params[:search].downcase}%"
      
      # Search across name and biography only
      scope = scope.where(
                     "LOWER(artist_profiles.name) LIKE ? OR 
                      LOWER(artist_profiles.biography) LIKE ?",
                     search_term, search_term
                   )
    end

    # Style filter
    if params[:style_filter].present?
      scope = scope.joins(:artist_styles).where(artist_styles: { style_id: params[:style_filter] })
    end

    # Location filter
    if params[:location_filter].present?
      scope = scope.where(location: params[:location_filter])
    end


    # Pagination
    page = params[:page].to_i
    page = 1 if page <= 0
    per_page = 24
    offset = (page - 1) * per_page

    scope.order(:name).offset(offset).limit(per_page)
  end

  def paginated_portfolio_items
    page = params[:page].to_i
    page = 1 if page <= 0
    per_page = 24
    offset = (page - 1) * per_page

    @artist_profile.portfolio_items
                   .includes(image_attachment: :blob)
                   .ordered
                   .offset(offset)
                   .limit(per_page)
  end



  def set_artist_profile
    @artist_profile = ArtistProfile.friendly.find(params[:slug])
    
    # Check if user is approved (skip for edit/update if it's the owner)
    if %w[edit update].include?(action_name)
      # Allow owner to edit even if not approved
      unless current_user&.artist_profile == @artist_profile
        redirect_to root_path, alert: "Access denied."
      end
    else
      # For viewing, require approved status
      unless @artist_profile.user.approved?
        redirect_to root_path, alert: "Artist profile not found."
      end
    end
  rescue ActiveRecord::RecordNotFound
    redirect_to root_path, alert: "Artist not found."
  end

  def require_artist_owner
    unless current_user&.artist? && current_user.approved_or_admin?
      redirect_to root_path, alert: 'Access denied.'
    end
  end

  def artist_profile_params
    params.require(:artist_profile).permit(:name, :biography, :location, :contact_email, 
                                          :instagram_url, :website_url, :studio_link, :booking_link, 
                                          :profile_photo,
                                          style_ids: [])
  end
end
