class InspirationBoardsController < ApplicationController
  optional_authentication only: [:browse_public, :show]
  before_action :require_approved_user, except: [:browse_public, :show]
  before_action :set_profile, except: [:browse_public, :index, :new, :create]
  before_action :set_inspiration_board, only: [:show, :edit, :update, :destroy]
  before_action :ensure_owner, only: [:show, :edit, :update, :destroy]

  def index
    # Handle both nested and non-nested routes
    if @profile_user
      @inspiration_boards = @profile_user.inspiration_boards.includes(:inspiration_board_items).ordered
    else
      @inspiration_boards = current_user.inspiration_boards.includes(:inspiration_board_items).ordered
    end
    
    respond_to do |format|
      format.html
      format.json do
        render json: @inspiration_boards.map { |board|
          {
            id: board.id,
            name: board.name,
            description: board.description,
            privacy: board.privacy,
            items_count: board.inspiration_board_items.count,
            created_at: board.created_at
          }
        }
      end
    end
  end

  def show
    @inspiration_board_items = @inspiration_board.inspiration_board_items.includes(:portfolio_item => [:artist_profile]).ordered
  end

  def new
    user = @profile_user || current_user
    @inspiration_board = user.inspiration_boards.build
  end

  def create
    user = @profile_user || current_user
    @inspiration_board = user.inspiration_boards.build(inspiration_board_params)
    
    if @inspiration_board.save
      if @artist_profile
        redirect_to artist_inspiration_board_path(@artist_profile, @inspiration_board), notice: 'Inspiration board was successfully created.'
      elsif @client_profile
        redirect_to client_inspiration_board_path(@client_profile, @inspiration_board), notice: 'Inspiration board was successfully created.'
      else
        # For the global route
        redirect_to my_inspiration_boards_path, notice: 'Inspiration board was successfully created.'
      end
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @inspiration_board.update(inspiration_board_params)
      if @artist_profile
        redirect_to artist_inspiration_board_path(@artist_profile, @inspiration_board), notice: 'Inspiration board was successfully updated.'
      else
        redirect_to client_inspiration_board_path(@client_profile, @inspiration_board), notice: 'Inspiration board was successfully updated.'
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @inspiration_board.destroy
    if @artist_profile
      redirect_to inspiration_artist_path(@artist_profile), notice: 'Inspiration board was successfully deleted.'
    else
      redirect_to inspiration_client_path(@client_profile), notice: 'Inspiration board was successfully deleted.'
    end
  end

  def browse_public
    @public_boards = InspirationBoard.includes(:user, :inspiration_board_items)
                                   .public_boards
                                   .joins(:inspiration_board_items)
                                   .group('inspiration_boards.id')
                                   .having('COUNT(inspiration_board_items.id) > 0')
                                   .order(created_at: :desc)
                                   .limit(50)
  end


  def check_item
    unless current_user
      render json: [], status: :unauthorized
      return
    end

    item_type = params[:type]
    item_id = params[:id]

    if item_type == 'portfolio'
      boards = current_user.inspiration_boards
                          .joins(:inspiration_board_items)
                          .where(inspiration_board_items: { portfolio_item_id: item_id })
    else
      render json: [], status: :bad_request
      return
    end

    render json: boards.map { |board|
      {
        id: board.id,
        name: board.name
      }
    }
  end

  private

  def set_profile
    if params[:artist_slug]
      @artist_profile = ArtistProfile.friendly.find(params[:artist_slug])
      @profile_user = @artist_profile.user
    elsif params[:client_slug]
      @client_profile = ClientProfile.friendly.find(params[:client_slug])
      @profile_user = @client_profile.user
    end
  end

  def set_inspiration_board
    user = @profile_user || current_user
    @inspiration_board = user.inspiration_boards.find(params[:id])
  end

  def ensure_owner
    # For show action, allow if board is public or user is owner
    if action_name == 'show'
      unless @inspiration_board.public? || (current_user && @inspiration_board.user == current_user)
        redirect_to browse_public_inspiration_boards_path, alert: 'This board is private.'
      end
    else
      # For edit/update/destroy, require ownership
      unless current_user && @inspiration_board.user == current_user
        if @artist_profile
          redirect_to inspiration_artist_path(@artist_profile), alert: 'Access denied.'
        else
          redirect_to inspiration_client_path(@client_profile), alert: 'Access denied.'
        end
      end
    end
  end

  def inspiration_board_params
    params.require(:inspiration_board).permit(:name, :privacy, :description)
  end

  def require_approved_user
    unless current_user&.approved_or_admin?
      redirect_to root_path, alert: 'Access denied.'
    end
  end
end