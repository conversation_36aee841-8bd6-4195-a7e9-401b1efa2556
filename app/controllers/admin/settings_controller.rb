class Admin::SettingsController < Admin::BaseController
  before_action :set_settings

  def show
  end

  def edit
  end

  def update
    if @settings.update(settings_params)
      redirect_to admin_settings_path, notice: 'Settings were successfully updated.'
    else
      render :show, status: :unprocessable_entity
    end
  end

  private

  def set_settings
    @settings = SiteSetting.current
  end

  def settings_params
    params.require(:site_setting).permit(
      :require_approval, :allow_public_registration, :auto_approve_posts,
      :enable_comments, :enable_messaging, :enable_following,
      :enable_inspiration_boards, :enable_public_profiles,
      :site_name, :contact_email, :site_description
    )
  end
end