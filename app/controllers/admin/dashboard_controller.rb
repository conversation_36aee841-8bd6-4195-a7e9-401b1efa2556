class Admin::DashboardController < Admin::BaseController
  def index
    @stats = {
      total_users: User.count,
      pending_users: User.where(approved: false).count,
      artists: User.where(role: 'artist').count,
      clients: User.where(role: 'client').count,
      total_portfolio_items: PortfolioItem.count,
      total_inspiration_boards: InspirationBoard.count,
      total_styles: Style.count
    }
    
    @recent_users = User.order(created_at: :desc).limit(5)
    @pending_approvals = User.where(approved: false).order(created_at: :desc).limit(10)
    @recent_portfolio_items = PortfolioItem.includes(:artist_profile).order(created_at: :desc).limit(5)
  end
end