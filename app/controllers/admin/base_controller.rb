class Admin::BaseController < ApplicationController
  before_action :require_admin
  layout 'admin'

  private

  def require_admin
    unless current_user&.admin?
      redirect_to root_path, alert: 'Access denied. Admin privileges required.'
    end
  end

  def nav_link_class(path)
    base_classes = "text-indigo-200 hover:text-white px-3 py-2 rounded-md text-sm font-medium flex items-center"
    if request.path == path || (path == admin_dashboard_path && request.path == admin_root_path)
      "#{base_classes} bg-indigo-700 text-white"
    else
      base_classes
    end
  end
  helper_method :nav_link_class
end