class Admin::UsersController < Admin::BaseController
  before_action :set_user, only: [:show, :edit, :update, :destroy, :approve, :reject]

  def index
    @users = User.includes(:artist_profile, :client_profile)
    
    # Filter by role
    @users = @users.where(role: params[:role]) if params[:role].present?
    
    # Filter by approval status
    case params[:status]
    when 'pending'
      @users = @users.where(approved: false)
    when 'approved'
      @users = @users.where(approved: true)
    when 'rejected'
      @users = @users.where(approved: false)
    end
    
    # Search by email
    if params[:search].present?
      @users = @users.where(
        "email_address LIKE ?", 
        "%#{params[:search]}%"
      )
    end
    
    @users = @users.order(created_at: :desc)
    
    @pending_count = User.where(approved: false).count
    @total_count = User.count
  end

  def show
  end

  def edit
  end

  def update
    if @user.update(user_params)
      redirect_to admin_user_path(@user), notice: 'User was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @user.admin?
      redirect_to admin_users_path, alert: 'Cannot delete admin users.'
      return
    end
    
    @user.destroy
    redirect_to admin_users_path, notice: 'User was successfully deleted.'
  end

  def approve
    was_unapproved = !@user.approved?
    @user.update!(approved: true)
    
    # Send approval email if user was previously unapproved
    if was_unapproved
      UserNotificationMailer.approval_granted(@user).deliver_later
    end
    
    redirect_to admin_users_path, notice: "#{@user.display_name} has been approved."
  end

  def reject
    @user.update!(approved: false)
    redirect_to admin_users_path, notice: "#{@user.display_name} has been rejected."
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def user_params
    params.require(:user).permit(:email_address, :role, :approved, :admin)
  end
end