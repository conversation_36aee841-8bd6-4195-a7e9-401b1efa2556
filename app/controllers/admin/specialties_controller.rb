class Admin::SpecialtiesController < Admin::BaseController
  before_action :set_specialty, only: [:show, :edit, :update, :destroy]

  def index
    @specialties = Specialty.includes(:artist_profiles)
    
    # Search by title
    if params[:search].present?
      @specialties = @specialties.where("title LIKE ?", "%#{params[:search]}%")
    end
    
    @specialties = @specialties.order(:title)
  end

  def show
  end

  def new
    @specialty = Specialty.new
  end

  def create
    @specialty = Specialty.new(specialty_params)
    
    if @specialty.save
      redirect_to admin_specialties_path, notice: 'Specialty was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @specialty.update(specialty_params)
      redirect_to admin_specialties_path, notice: 'Specialty was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @specialty.artist_profiles.any?
      redirect_to admin_specialties_path, alert: 'Cannot delete specialty that is assigned to artists.'
      return
    end
    
    @specialty.destroy
    redirect_to admin_specialties_path, notice: 'Specialty was successfully deleted.'
  end

  private

  def set_specialty
    @specialty = Specialty.find(params[:id])
  end

  def specialty_params
    params.require(:specialty).permit(:title, :description)
  end
end