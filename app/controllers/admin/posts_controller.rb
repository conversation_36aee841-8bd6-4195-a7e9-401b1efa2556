class Admin::PostsController < Admin::BaseController
  before_action :set_portfolio_item, only: [:show, :destroy]

  def index
    @portfolio_items = PortfolioItem.includes(:artist_profile, image_attachment: :blob)
                                   .joins(artist_profile: :user)
                                   .where(users: { approved: true })
    
    # Search by description or artist name
    if params[:search].present?
      @portfolio_items = @portfolio_items.where(
        "portfolio_items.description LIKE ? OR users.username LIKE ?",
        "%#{params[:search]}%", "%#{params[:search]}%"
      )
    end
    
    @portfolio_items = @portfolio_items.order(created_at: :desc)
    @total_count = PortfolioItem.joins(artist_profile: :user).where(users: { approved: true }).count
  end

  def show
  end

  def destroy
    @portfolio_item.destroy
    redirect_to admin_posts_path, notice: 'Portfolio item was successfully deleted.'
  end

  private

  def set_portfolio_item
    @portfolio_item = PortfolioItem.find(params[:id])
  end
end