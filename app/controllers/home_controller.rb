class HomeController < ApplicationController
  optional_authentication

  def index
    # Show recent portfolio items from approved artists
    @portfolio_items = recent_portfolio_items.limit(24)
    @featured_artists = featured_artists.limit(8)
  end

  private

  def recent_portfolio_items
    PortfolioItem.joins(artist_profile: :user)
                 .where(users: { approved: true })
                 .includes(:artist_profile, image_attachment: :blob)
                 .order(created_at: :desc)
  end

  def featured_artists
    ArtistProfile.joins(:user)
                 .where(users: { approved: true })
                 .includes(:user, :portfolio_items, profile_photo_attachment: :blob)
                 .order(created_at: :desc)
  end

end
