class PortfolioItemsController < ApplicationController
  before_action :require_approved_artist
  before_action :set_artist_profile
  before_action :set_portfolio_item, only: [:show, :edit, :update, :destroy]

  def index
    @portfolio_items = @artist_profile.portfolio_items.ordered
  end

  def show
  end

  def new
    @portfolio_item = @artist_profile.portfolio_items.build
  end

  def create
    @portfolio_item = @artist_profile.portfolio_items.build(portfolio_item_params)
    
    if @portfolio_item.save
      redirect_to portfolio_items_path, notice: 'Portfolio item was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @portfolio_item.update(portfolio_item_params)
      redirect_to portfolio_items_path, notice: 'Portfolio item was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @portfolio_item.destroy
    
    respond_to do |format|
      format.html { redirect_to portfolio_items_path, notice: 'Portfolio item was successfully deleted.' }
      format.json { render json: { status: 'success', message: 'Portfolio item was successfully deleted.' } }
    end
  end


  private

  def set_artist_profile
    @artist_profile = current_user.artist_profile
    redirect_to root_path, alert: 'Access denied.' unless @artist_profile
  end

  def set_portfolio_item
    @portfolio_item = @artist_profile.portfolio_items.find(params[:id])
  end

  def portfolio_item_params
    params.require(:portfolio_item).permit(:caption, :image, style_ids: [])
  end


  def require_approved_artist
    unless current_user&.artist? && current_user.approved_or_admin?
      redirect_to root_path, alert: 'Access denied.'
    end
  end
end