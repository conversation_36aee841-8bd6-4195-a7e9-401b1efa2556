<% content_for :title, "Admin Dashboard" %>

<div class="mb-8">
  <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
  <p class="mt-2 text-gray-600 dark:text-gray-400">Welcome back, <%= current_user.display_name %>! Here's what's happening in your tattoo marketplace.</p>
</div>

<!-- Site Statistics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <div class="card-style overflow-hidden rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold">👥</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Users</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-white"><%= @stats[:total_users] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="card-style overflow-hidden rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold">⏳</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Pending Approval</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-white">
              <%= @stats[:pending_users] %>
              <% if @stats[:pending_users] > 0 %>
                <span class="text-sm text-yellow-600 dark:text-yellow-400">(Action Needed)</span>
              <% end %>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="card-style overflow-hidden rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold">🎨</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Posts</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-white"><%= @stats[:total_posts] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="card-style overflow-hidden rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold">💌</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Messages</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-white"><%= @stats[:total_messages] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
  <!-- Recent Users -->
  <div class="card-style rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Recent Users</h3>
      <div class="flow-root">
        <ul class="-my-5 divide-y divide-gray-200 dark:divide-gray-700">
          <% @recent_users.each do |user| %>
            <li class="py-4">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <div class="h-6 w-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      <%= user.display_name.first.upcase %>
                    </span>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    <%= user.display_name %>
                  </p>
                  <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                    <%= user.role.humanize %> • 
                    <span class="<%= user.approved? ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400' %>">
                      <%= user.approved? ? 'Approved' : 'Pending' %>
                    </span>
                  </p>
                </div>
                <div class="flex-shrink-0 text-sm text-gray-500 dark:text-gray-400">
                  <%= time_ago_in_words(user.created_at) %> ago
                </div>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
      <div class="mt-6">
        <%= link_to admin_users_path, class: "w-full flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600" do %>
          View all users
        <% end %>
      </div>
    </div>
  </div>

  <!-- Pending Approvals -->
  <div class="card-style rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
        Pending Approvals
        <% if @pending_approvals.any? %>
          <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300">
            <%= @pending_approvals.count %>
          </span>
        <% end %>
      </h3>
      
      <% if @pending_approvals.any? %>
        <div class="flow-root">
          <ul class="-my-5 divide-y divide-gray-200 dark:divide-gray-700">
            <% @pending_approvals.each do |user| %>
              <li class="py-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                      <div class="h-6 w-6 bg-yellow-300 dark:bg-yellow-600 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-yellow-800 dark:text-yellow-100">
                          <%= user.display_name.first.upcase %>
                        </span>
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                        <%= user.display_name %>
                      </p>
                      <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                        <%= user.role.humanize %> • <%= time_ago_in_words(user.created_at) %> ago
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <%= link_to approve_admin_user_path(user), method: :patch, 
                        class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700" do %>
                      ✓ Approve
                    <% end %>
                    <%= link_to reject_admin_user_path(user), method: :patch,
                        class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700" do %>
                      ✗ Reject
                    <% end %>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
        <div class="mt-6">
          <%= link_to admin_users_path(filter: 'pending'), class: "w-full flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600" do %>
            View all pending approvals
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-6">
          <div class="text-gray-400 dark:text-gray-500">
            <span class="text-2xl">🎉</span>
          </div>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No pending approvals!</p>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Quick Actions -->
<div class="mt-8">
  <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h3>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
    <%= link_to admin_users_path, class: "relative block w-full border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg p-6 text-center hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
      <span class="text-2xl">👥</span>
      <span class="mt-2 block text-sm font-medium text-gray-900 dark:text-white">Manage Users</span>
    <% end %>
    
    <%= link_to admin_posts_path, class: "relative block w-full border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg p-6 text-center hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
      <span class="text-2xl">🎨</span>
      <span class="mt-2 block text-sm font-medium text-gray-900 dark:text-white">Moderate Posts</span>
    <% end %>
    
    <%= link_to admin_specialties_path, class: "relative block w-full border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg p-6 text-center hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
      <span class="text-2xl">🏷️</span>
      <span class="mt-2 block text-sm font-medium text-gray-900 dark:text-white">Manage Specialties</span>
    <% end %>
    
    <%= link_to admin_settings_path, class: "relative block w-full border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg p-6 text-center hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
      <span class="text-2xl">⚙️</span>
      <span class="mt-2 block text-sm font-medium text-gray-900 dark:text-white">Site Settings</span>
    <% end %>
  </div>
</div>