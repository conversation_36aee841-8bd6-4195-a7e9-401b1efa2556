<% content_for :title, "Site Settings" %>

<div class="bg-white shadow">
  <div class="px-4 py-5 sm:p-6">
    <h1 class="text-lg font-medium text-gray-900 mb-6">Site Settings</h1>

    <%= form_with model: [:admin, @settings], url: admin_settings_path, local: true, class: "space-y-6" do |form| %>
      <% if @settings.errors.any? %>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <h4><%= pluralize(@settings.errors.count, "error") %> prohibited settings from being saved:</h4>
          <ul class="mt-2 ml-4 list-disc">
            <% @settings.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="grid grid-cols-1 gap-6">
        <div>
          <%= form.label :site_name, class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :site_name, class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 sm:text-sm" %>
        </div>

        <div>
          <%= form.label :contact_email, class: "block text-sm font-medium text-gray-700" %>
          <%= form.email_field :contact_email, class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 sm:text-sm" %>
        </div>

        <div>
          <%= form.label :site_description, class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_area :site_description, rows: 3, class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 sm:text-sm" %>
        </div>

        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">Feature Settings</h3>
          
          <div class="flex items-center">
            <%= form.check_box :require_approval, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :require_approval, "Require approval for new users", class: "ml-2 block text-sm text-gray-900" %>
          </div>

          <div class="flex items-center">
            <%= form.check_box :allow_public_registration, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :allow_public_registration, "Allow public registration", class: "ml-2 block text-sm text-gray-900" %>
          </div>

          <div class="flex items-center">
            <%= form.check_box :auto_approve_posts, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :auto_approve_posts, "Auto-approve posts", class: "ml-2 block text-sm text-gray-900" %>
          </div>

          <div class="flex items-center">
            <%= form.check_box :enable_comments, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :enable_comments, "Enable comments", class: "ml-2 block text-sm text-gray-900" %>
          </div>

          <div class="flex items-center">
            <%= form.check_box :enable_messaging, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :enable_messaging, "Enable messaging", class: "ml-2 block text-sm text-gray-900" %>
          </div>

          <div class="flex items-center">
            <%= form.check_box :enable_following, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :enable_following, "Enable following", class: "ml-2 block text-sm text-gray-900" %>
          </div>

          <div class="flex items-center">
            <%= form.check_box :enable_inspiration_boards, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :enable_inspiration_boards, "Enable inspiration boards", class: "ml-2 block text-sm text-gray-900" %>
          </div>

          <div class="flex items-center">
            <%= form.check_box :enable_public_profiles, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
            <%= form.label :enable_public_profiles, "Enable public profiles", class: "ml-2 block text-sm text-gray-900" %>
          </div>
        </div>
      </div>

      <div class="flex justify-end space-x-3">
        <%= link_to "Cancel", admin_settings_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500" %>
        <%= form.submit "Update Settings", class: "bg-black dark:bg-white600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500" %>
      </div>
    <% end %>
  </div>
</div>