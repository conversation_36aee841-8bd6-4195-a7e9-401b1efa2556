<% content_for :title, "Site Settings" %>

<div class="mb-8">
  <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Site Settings</h1>
  <p class="mt-2 text-gray-600 dark:text-gray-400">Configure site-wide settings and preferences.</p>
</div>

<div class="card-style rounded-lg">
  <%= form_with model: @settings, url: admin_settings_path, method: :patch, local: true, class: "space-y-6" do |form| %>
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">User Registration</h3>
      
      <div class="space-y-4">
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :require_approval, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :require_approval, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Require admin approval for new users
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">When enabled, new users must be approved by an admin before they can access the platform.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :allow_public_registration, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :allow_public_registration, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Allow public registration
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">When disabled, only invited users or admin-created accounts can register.</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="px-4 py-5 sm:p-6 border-t border-gray-200 dark:border-gray-700">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Content Moderation</h3>
      
      <div class="space-y-4">
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :auto_approve_posts, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :auto_approve_posts, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Auto-approve new posts
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">When disabled, new posts require admin approval before becoming visible.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :enable_comments, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :enable_comments, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Enable comments on posts
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">Allow users to comment on posts.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :enable_messaging, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :enable_messaging, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Enable private messaging
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">Allow users to send private messages to each other.</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="px-4 py-5 sm:p-6 border-t border-gray-200 dark:border-gray-700">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Platform Features</h3>
      
      <div class="space-y-4">
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :enable_following, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :enable_following, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Enable user following
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">Allow clients to follow artists for personalized feeds.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :enable_inspiration_boards, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :enable_inspiration_boards, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Enable inspiration boards
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">Allow users to create and share inspiration boards.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :enable_public_profiles, 
                class: "focus:ring-black dark:bg-white500 h-4 w-4 text-black dark:bg-white600 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :enable_public_profiles, class: "font-medium text-gray-700 dark:text-gray-300" do %>
              Enable public profiles
            <% end %>
            <p class="text-gray-500 dark:text-gray-400">Allow unauthenticated users to view artist and client profiles.</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="px-4 py-5 sm:p-6 border-t border-gray-200 dark:border-gray-700">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Site Information</h3>
      
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <%= form.label :site_name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :site_name, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white" %>
        </div>
        
        <div>
          <%= form.label :contact_email, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.email_field :contact_email, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white" %>
        </div>
      </div>
      
      <div class="mt-6">
        <%= form.label :site_description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
        <%= form.text_area :site_description, rows: 3,
            class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white" %>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Brief description that appears in search results and social media.</p>
      </div>
    </div>
    
    <div class="px-4 py-3 bg-white dark:bg-gray-700 text-right sm:px-6">
      <%= form.submit "Save Settings", 
          class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black dark:bg-white600 hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" %>
    </div>
  <% end %>
</div>

<!-- Current Statistics -->
<div class="mt-8 card-style rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Current Statistics</h3>
    
    <dl class="grid grid-cols-1 gap-5 sm:grid-cols-3">
      <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Users</dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= User.count %></dd>
      </div>
      
      <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Artists</dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= User.where(role: 'artist').count %></dd>
      </div>
      
      <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Posts</dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= Post.count %></dd>
      </div>
    </dl>
  </div>
</div>