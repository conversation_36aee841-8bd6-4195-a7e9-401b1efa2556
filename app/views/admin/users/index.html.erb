<% content_for :title, "User Management" %>

<div class="mb-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">User Management</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Manage user accounts, approvals, and permissions.</p>
    </div>
  </div>
</div>

<!-- Filters -->
<div class="mb-6 card-style rounded-lg p-6">
  <%= form_with url: admin_users_path, method: :get, local: true, class: "space-y-4 sm:space-y-0 sm:flex sm:items-center sm:space-x-4" do |form| %>
    <div class="flex-1">
      <%= form.text_field :search, placeholder: "Search by username or email...", 
          value: params[:search], 
          class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white" %>
    </div>
    
    <div>
      <el-select name="role" value="<%= params[:role] %>" class="block">
        <button type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white dark:bg-black py-1.5 pr-2 pl-3 text-left text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-black dark:bg-white600 sm:text-sm/6">
          <el-selectedcontent class="col-start-1 row-start-1 truncate pr-6">
            <% case params[:role] %>
            <% when 'artist' %>
              Artists
            <% when 'client' %>
              Clients
            <% else %>
              All Roles
            <% end %>
          </el-selectedcontent>
          <svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4">
            <path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />
          </svg>
        </button>

        <el-options anchor="bottom start" popover class="max-h-60 w-(--button-width) overflow-auto rounded-md bg-white dark:bg-black py-1 text-base shadow-lg ring-1 ring-black/5 dark:ring-white/5 [--anchor-gap:--spacing(1)] focus:outline-hidden data-leave:transition data-leave:transition-discrete data-leave:duration-100 data-leave:ease-in data-closed:data-leave:opacity-0 sm:text-sm">
          <el-option value="" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">All Roles</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="artist" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Artists</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="client" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Clients</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
        </el-options>
      </el-select>
    </div>
    
    <div>
      <el-select name="status" value="<%= params[:status] %>" class="block">
        <button type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white dark:bg-black py-1.5 pr-2 pl-3 text-left text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-black dark:bg-white600 sm:text-sm/6">
          <el-selectedcontent class="col-start-1 row-start-1 truncate pr-6">
            <% case params[:status] %>
            <% when 'approved' %>
              Approved
            <% when 'pending' %>
              Pending
            <% when 'rejected' %>
              Rejected
            <% else %>
              All Status
            <% end %>
          </el-selectedcontent>
          <svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4">
            <path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />
          </svg>
        </button>

        <el-options anchor="bottom start" popover class="max-h-60 w-(--button-width) overflow-auto rounded-md bg-white dark:bg-black py-1 text-base shadow-lg ring-1 ring-black/5 dark:ring-white/5 [--anchor-gap:--spacing(1)] focus:outline-hidden data-leave:transition data-leave:transition-discrete data-leave:duration-100 data-leave:ease-in data-closed:data-leave:opacity-0 sm:text-sm">
          <el-option value="" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">All Status</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="approved" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Approved</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="pending" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Pending</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="rejected" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Rejected</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
        </el-options>
      </el-select>
    </div>
    
    <div class="flex space-x-2">
      <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black dark:bg-white600 hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" %>
      <%= link_to admin_users_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
        Clear
      <% end %>
    </div>
  <% end %>
</div>

<!-- Users Table -->
<div class="bg-white dark:bg-black shadow overflow-hidden sm:rounded-md">
  <% if @users.any? %>
    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
      <% @users.each do |user| %>
        <li>
          <div class="px-4 py-4 flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-10 w-10">
                <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    <%= user.display_name.first.upcase %>
                  </span>
                </div>
              </div>
              <div class="ml-4">
                <div class="flex items-center">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    <%= user.display_name %>
                  </div>
                  <% if user.admin? %>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Admin
                    </span>
                  <% end %>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  <%= user.email_address %> • <%= user.role.humanize %>
                </div>
                <div class="text-xs text-gray-400 dark:text-gray-500">
                  Joined <%= time_ago_in_words(user.created_at) %> ago
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <!-- Status Badge -->
              <div>
                <% if user.approved? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    ✓ Approved
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    ⏳ Pending
                  </span>
                <% end %>
              </div>
              
              <!-- Action Buttons -->
              <div class="flex space-x-2">
                <% unless user.admin? %>
                  <% unless user.approved? %>
                    <%= link_to approve_admin_user_path(user), method: :patch,
                        class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800" do %>
                      ✓ Approve
                    <% end %>
                    <%= link_to reject_admin_user_path(user), method: :patch,
                        class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800" do %>
                      ✗ Reject
                    <% end %>
                  <% else %>
                    <%= link_to reject_admin_user_path(user), method: :patch,
                        class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600" do %>
                      Revoke
                    <% end %>
                  <% end %>
                <% end %>
                
                <%= link_to admin_user_path(user), class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600" do %>
                  View Details
                <% end %>
              </div>
            </div>
          </div>
        </li>
      <% end %>
    </ul>
    
    <!-- Pagination would go here -->
    <% if @users.respond_to?(:total_pages) && @users.total_pages > 1 %>
      <div class="bg-white dark:bg-black px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
        <!-- Add pagination here if using Kaminari or similar -->
      </div>
    <% end %>
  <% else %>
    <div class="text-center py-12">
      <div class="text-gray-400 dark:text-gray-500">
        <span class="text-4xl">👥</span>
      </div>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No users found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        <% if params[:search].present? || params[:role].present? || params[:status].present? %>
          Try adjusting your filters.
        <% else %>
          No users have been created yet.
        <% end %>
      </p>
    </div>
  <% end %>
</div>