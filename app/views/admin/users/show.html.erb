<% content_for :title, "User Details - #{@user.display_name}" %>

<div class="mb-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">User Details</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">View and manage user account information.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-2">
      <%= link_to admin_users_path, class: "inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 shadow-sm hover:bg-white dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 sm:w-auto dark:bg-gray-700" do %>
        ← Back to Users
      <% end %>
      <%= link_to edit_admin_user_path(@user), class: "inline-flex items-center justify-center rounded-md border border-transparent bg-black dark:bg-white600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 sm:w-auto" do %>
        Edit User
      <% end %>
    </div>
  </div>
</div>

<!-- User Profile Card -->
<div class="card-style rounded-lg mb-8">
  <div class="px-4 py-5 sm:p-6">
    <div class="flex items-center space-x-5">
      <div class="flex-shrink-0">
        <div class="h-20 w-20 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
          <span class="text-xl font-medium text-gray-700 dark:text-gray-300">
            <%= @user.display_name.first.upcase %>
          </span>
        </div>
      </div>
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white truncate">
          <%= @user.display_name %>
          <% if @user.admin? %>
            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Admin
            </span>
          <% end %>
        </h2>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          <%= @user.email_address %> • <%= @user.role.humanize %>
        </p>
        <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span class="<%= @user.approved? ? 'text-green-600' : 'text-yellow-600' %>">
              <%= @user.approved? ? '✓ Approved' : '⏳ Pending Approval' %>
            </span>
          </div>
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            Joined <%= time_ago_in_words(@user.created_at) %> ago
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Account Information -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
  <!-- Basic Information -->
  <div class="card-style rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Account Information</h3>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Username</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-white"><%= @user.name %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email Address</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-white"><%= @user.email_address %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">User Type</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-white"><%= @user.role.humanize %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Status</dt>
          <dd class="mt-1">
            <% if @user.approved? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Approved
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Pending Approval
              </span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Admin Privileges</dt>
          <dd class="mt-1">
            <% if @user.admin? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Admin
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                Regular User
              </span>
            <% end %>
          </dd>
        </div>
      </dl>
    </div>
  </div>

  <!-- Profile Information -->
  <div class="card-style rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Profile Information</h3>
      
      <% if @user.artist? && @user.artist_profile %>
        <dl class="space-y-4">
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Artist Name</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white"><%= @user.artist_profile.name %></dd>
          </div>
          <% if @user.artist_profile.biography.present? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Bio</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white"><%= truncate(@user.artist_profile.biography, length: 200) %></dd>
            </div>
          <% end %>
          <% if @user.artist_profile.location.present? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white"><%= @user.artist_profile.location %></dd>
            </div>
          <% end %>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Specialties</dt>
            <dd class="mt-1">
              <% if @user.artist_profile.specialties.any? %>
                <div class="flex flex-wrap gap-2">
                  <% @user.artist_profile.specialties.each do |specialty| %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      <%= specialty.title %>
                    </span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-sm text-gray-500 dark:text-gray-400">No specialties selected</span>
              <% end %>
            </dd>
          </div>
        </dl>
      <% elsif @user.client? && @user.client_profile %>
        <dl class="space-y-4">
          <% if @user.client_profile.location.present? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white"><%= @user.client_profile.location %></dd>
            </div>
          <% end %>
        </dl>
      <% else %>
        <p class="text-sm text-gray-500 dark:text-gray-400">Profile not yet created</p>
      <% end %>
    </div>
  </div>
</div>

<!-- Activity Statistics -->
<div class="mt-8 card-style rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Activity Statistics</h3>
    
    <dl class="grid grid-cols-1 gap-5 sm:grid-cols-4">
      <% if @user.artist? && @user.artist_profile %>
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Posts</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= @user.artist_profile.posts.count %></dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Portfolio Items</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= @user.artist_profile.posts.in_portfolio.count %></dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Followers</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= @user.artist_profile.followers.count %></dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Likes</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
            <%= @user.artist_profile.posts.joins(:likes).count %>
          </dd>
        </div>
      <% elsif @user.client? && @user.client_profile %>
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Following</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= @user.client_profile.followed_artists.count %></dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Inspiration Boards</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= @user.inspiration_boards.count %></dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Saved Items</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
            <%= @user.inspiration_boards.joins(:inspiration_board_items).count %>
          </dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Comments</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= Comment.where(user: @user).count %></dd>
        </div>
      <% end %>
    </dl>
  </div>
</div>

<!-- Action Buttons -->
<% unless @user.admin? %>
  <div class="mt-8 flex justify-end space-x-3">
    <% unless @user.approved? %>
      <%= link_to approve_admin_user_path(@user), method: :patch,
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800" do %>
        ✓ Approve User
      <% end %>
      <%= link_to reject_admin_user_path(@user), method: :patch,
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800" do %>
        ✗ Reject User
      <% end %>
    <% else %>
      <%= link_to reject_admin_user_path(@user), method: :patch,
          class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600" do %>
        Revoke Approval
      <% end %>
    <% end %>
    
    <%= link_to admin_user_path(@user), method: :delete,
        confirm: "Are you sure you want to delete this user? This action cannot be undone.",
        class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800" do %>
      🗑️ Delete User
    <% end %>
  </div>
<% end %>