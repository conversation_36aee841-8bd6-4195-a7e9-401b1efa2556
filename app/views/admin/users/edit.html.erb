<% content_for :title, "Edit User - #{@user.name}" %>

<div class="bg-white shadow">
  <div class="px-4 py-5 sm:p-6">
    <h1 class="text-lg font-medium text-gray-900 mb-6">Edit User</h1>

    <%= form_with model: [:admin, @user], local: true, class: "space-y-6" do |form| %>
      <% if @user.errors.any? %>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <h4><%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h4>
          <ul class="mt-2 ml-4 list-disc">
            <% @user.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div>
        <%= form.label :username, class: "block text-sm font-medium text-gray-700" %>
        <%= form.text_field :username, class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 sm:text-sm" %>
      </div>

      <div>
        <%= form.label :email_address, class: "block text-sm font-medium text-gray-700" %>
        <%= form.email_field :email_address, class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 sm:text-sm" %>
      </div>

      <div>
        <label for="user_role" class="block text-sm/6 font-medium text-gray-700">Role</label>
        <el-select id="user_role" name="user[role]" value="<%= @user.role %>" class="mt-1 block">
          <button type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white dark:bg-black py-1.5 pr-2 pl-3 text-left text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-black dark:bg-white600 sm:text-sm/6">
            <el-selectedcontent class="col-start-1 row-start-1 truncate pr-6">
              <% case @user.role %>
              <% when 'artist' %>
                Artist
              <% when 'client' %>
                Client
              <% end %>
            </el-selectedcontent>
            <svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4">
              <path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />
            </svg>
          </button>

          <el-options anchor="bottom start" popover class="max-h-60 w-(--button-width) overflow-auto rounded-md bg-white dark:bg-black py-1 text-base shadow-lg ring-1 ring-black/5 dark:ring-white/5 [--anchor-gap:--spacing(1)] focus:outline-hidden data-leave:transition data-leave:transition-discrete data-leave:duration-100 data-leave:ease-in data-closed:data-leave:opacity-0 sm:text-sm">
            <el-option value="client" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
              <span class="block truncate font-normal group-aria-selected/option:font-semibold">Client</span>
              <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
                <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                  <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
                </svg>
              </span>
            </el-option>
            <el-option value="artist" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
              <span class="block truncate font-normal group-aria-selected/option:font-semibold">Artist</span>
              <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
                <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                  <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
                </svg>
              </span>
            </el-option>
          </el-options>
        </el-select>
      </div>

      <div class="flex items-center">
        <%= form.check_box :approved, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
        <%= form.label :approved, "Approved", class: "ml-2 block text-sm text-gray-900" %>
      </div>

      <div class="flex items-center">
        <%= form.check_box :admin, class: "h-4 w-4 text-black dark:bg-white600 focus:ring-black dark:bg-white500 border-gray-300 rounded" %>
        <%= form.label :admin, "Admin", class: "ml-2 block text-sm text-gray-900" %>
      </div>

      <div class="flex justify-end space-x-3">
        <%= link_to "Cancel", admin_user_path(@user), class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500" %>
        <%= form.submit "Update User", class: "bg-black dark:bg-white600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500" %>
      </div>
    <% end %>
  </div>
</div>