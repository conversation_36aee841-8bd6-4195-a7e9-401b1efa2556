<% content_for :title, "Content Moderation" %>

<div class="mb-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Content Moderation</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Review and moderate posts from artists.</p>
    </div>
  </div>
</div>

<!-- Filters -->
<div class="mb-6 card-style rounded-lg p-6">
  <%= form_with url: admin_posts_path, method: :get, local: true, class: "space-y-4 sm:space-y-0 sm:flex sm:items-center sm:space-x-4" do |form| %>
    <div class="flex-1">
      <%= form.text_field :search, placeholder: "Search by caption or artist...", 
          value: params[:search], 
          class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white" %>
    </div>
    
    <div>
      <el-select name="status" value="<%= params[:status] %>" class="block">
        <button type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white dark:bg-black py-1.5 pr-2 pl-3 text-left text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-black dark:bg-white600 sm:text-sm/6">
          <el-selectedcontent class="col-start-1 row-start-1 truncate pr-6">
            <% case params[:status] %>
            <% when 'visible' %>
              Visible
            <% when 'hidden' %>
              Hidden
            <% else %>
              All Posts
            <% end %>
          </el-selectedcontent>
          <svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4">
            <path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />
          </svg>
        </button>

        <el-options anchor="bottom start" popover class="max-h-60 w-(--button-width) overflow-auto rounded-md bg-white dark:bg-black py-1 text-base shadow-lg ring-1 ring-black/5 dark:ring-white/5 [--anchor-gap:--spacing(1)] focus:outline-hidden data-leave:transition data-leave:transition-discrete data-leave:duration-100 data-leave:ease-in data-closed:data-leave:opacity-0 sm:text-sm">
          <el-option value="" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">All Posts</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="visible" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Visible</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="hidden" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Hidden</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
        </el-options>
      </el-select>
    </div>
    
    <div>
      <el-select name="portfolio" value="<%= params[:portfolio] %>" class="block">
        <button type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white dark:bg-black py-1.5 pr-2 pl-3 text-left text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-black dark:bg-white600 sm:text-sm/6">
          <el-selectedcontent class="col-start-1 row-start-1 truncate pr-6">
            <% case params[:portfolio] %>
            <% when 'true' %>
              Portfolio Items
            <% when 'false' %>
              Feed Posts
            <% else %>
              All Posts
            <% end %>
          </el-selectedcontent>
          <svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4">
            <path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />
          </svg>
        </button>

        <el-options anchor="bottom start" popover class="max-h-60 w-(--button-width) overflow-auto rounded-md bg-white dark:bg-black py-1 text-base shadow-lg ring-1 ring-black/5 dark:ring-white/5 [--anchor-gap:--spacing(1)] focus:outline-hidden data-leave:transition data-leave:transition-discrete data-leave:duration-100 data-leave:ease-in data-closed:data-leave:opacity-0 sm:text-sm">
          <el-option value="" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">All Posts</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="true" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Portfolio Items</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
          <el-option value="false" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
            <span class="block truncate font-normal group-aria-selected/option:font-semibold">Feed Posts</span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
              <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </span>
          </el-option>
        </el-options>
      </el-select>
    </div>
    
    <div class="flex space-x-2">
      <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black dark:bg-white600 hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" %>
      <%= link_to admin_posts_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
        Clear
      <% end %>
    </div>
  <% end %>
</div>

<!-- Posts Grid -->
<% if @posts.any? %>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
    <% @posts.each do |post| %>
      <div class="card-style overflow-hidden rounded-lg">
        <!-- Post Image -->
        <div class="relative">
          <% if post.image.attached? %>
            <%= image_tag post.image, class: "w-full h-48 object-cover" %>
          <% else %>
            <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <span class="text-gray-400 dark:text-gray-500">No Image</span>
            </div>
          <% end %>
          
          <!-- Status Badge -->
          <div class="absolute top-2 right-2">
            <% if post.hidden? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Hidden
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Visible
              </span>
            <% end %>
          </div>
          
          <!-- Portfolio Badge -->
          <% if post.in_portfolio? %>
            <div class="absolute top-2 left-2">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Portfolio
              </span>
            </div>
          <% end %>
        </div>
        
        <!-- Post Details -->
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center mb-3">
            <div class="flex-shrink-0 h-6 w-6">
              <div class="h-6 w-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                  <%= post.artist_profile.user.display_name.first.upcase %>
                </span>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                <%= post.artist_profile.user.display_name %>
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                <%= time_ago_in_words(post.published_at) %> ago
              </p>
            </div>
          </div>
          
          <% if post.caption.present? %>
            <p class="text-sm text-gray-700 dark:text-gray-300 mb-3 line-clamp-3">
              <%= truncate(post.caption, length: 100) %>
            </p>
          <% end %>
          
          
          <!-- Action Buttons -->
          <div class="flex space-x-2">
            <% if post.hidden? %>
              <%= link_to unhide_admin_post_path(post), method: :patch,
                  class: "flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800" do %>
                👁️ Unhide
              <% end %>
            <% else %>
              <%= link_to hide_admin_post_path(post), method: :patch,
                  class: "flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800" do %>
                🙈 Hide
              <% end %>
            <% end %>
            
            <%= link_to post_path(post), target: "_blank",
                class: "flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600" do %>
              👀 View
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
  
  <!-- Pagination would go here -->
  <% if @posts.respond_to?(:total_pages) && @posts.total_pages > 1 %>
    <div class="mt-8">
      <!-- Add pagination here if using Kaminari or similar -->
    </div>
  <% end %>
<% else %>
  <div class="text-center py-12">
    <div class="text-gray-400 dark:text-gray-500">
      <span class="text-4xl">🎨</span>
    </div>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No posts found</h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      <% if params[:search].present? || params[:status].present? || params[:portfolio].present? %>
        Try adjusting your filters.
      <% else %>
        No posts have been created yet.
      <% end %>
    </p>
  </div>
<% end %>