<% content_for :title, "New Specialty" %>

<div class="mb-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">New Specialty</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Create a new tattoo specialty for artists to choose from.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to admin_specialties_path, class: "inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 shadow-sm hover:bg-white dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 sm:w-auto dark:bg-gray-700" do %>
        ← Back to Specialties
      <% end %>
    </div>
  </div>
</div>

<div class="card-style rounded-lg">
  <%= form_with model: [:admin, @specialty], local: true, class: "space-y-6" do |form| %>
    <% if @specialty.errors.any? %>
      <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <span class="text-red-400 dark:text-red-300">⚠️</span>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
              There were <%= pluralize(@specialty.errors.count, "error") %> with your submission:
            </h3>
            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
              <ul class="list-disc pl-5 space-y-1">
                <% @specialty.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <div class="px-4 py-5 sm:p-6">
      <div class="grid grid-cols-1 gap-6">
        <div>
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :name, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white",
              placeholder: "e.g., Traditional, Realistic, Neo-Traditional..." %>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">The name of the tattoo specialty (must be unique).</p>
        </div>

        <div>
          <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_area :description, rows: 4,
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white",
              placeholder: "Describe this tattoo style and what makes it unique..." %>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional description to help artists understand this specialty.</p>
        </div>
      </div>
    </div>

    <div class="px-4 py-3 bg-white dark:bg-gray-700 text-right sm:px-6">
      <%= link_to admin_specialties_path, class: "inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 hover:bg-white dark:hover:bg-white0 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800 mr-3" do %>
        Cancel
      <% end %>
      <%= form.submit "Create Specialty", 
          class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black dark:bg-white600 hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" %>
    </div>
  <% end %>
</div>