<% content_for :title, "Specialty Management" %>

<div class="mb-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Specialty Management</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Manage tattoo specialties that artists can choose from.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to new_admin_specialty_path, class: "inline-flex items-center justify-center rounded-md border border-transparent bg-black dark:bg-white600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 sm:w-auto" do %>
        Add Specialty
      <% end %>
    </div>
  </div>
</div>

<!-- Search -->
<div class="mb-6 card-style rounded-lg p-6">
  <%= form_with url: admin_specialties_path, method: :get, local: true, class: "flex space-x-4" do |form| %>
    <div class="flex-1">
      <%= form.text_field :search, placeholder: "Search specialties...", 
          value: params[:search], 
          class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 dark:focus:border-black dark:bg-white400 sm:text-sm dark:bg-gray-700 dark:text-white" %>
    </div>
    
    <div class="flex space-x-2">
      <%= form.submit "Search", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black dark:bg-white600 hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" %>
      <%= link_to admin_specialties_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
        Clear
      <% end %>
    </div>
  <% end %>
</div>

<!-- Specialties Table -->
<div class="bg-white dark:bg-black shadow overflow-hidden sm:rounded-md">
  <% if @specialties.any? %>
    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
      <% @specialties.each do |specialty| %>
        <li>
          <div class="px-4 py-4 flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-black dark:bg-white100 dark:bg-black dark:bg-white900 flex items-center justify-center">
                  <span class="text-sm font-medium text-black dark:bg-white800 dark:text-black dark:bg-white200">
                    <%= specialty.name.first.upcase %>
                  </span>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  <%= specialty.name %>
                </div>
                <% if specialty.description.present? %>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    <%= truncate(specialty.description, length: 100) %>
                  </div>
                <% end %>
                <div class="text-xs text-gray-400 dark:text-gray-500">
                  <%= pluralize(specialty.artist_profiles.count, 'artist') %> •
                  Created <%= time_ago_in_words(specialty.created_at) %> ago
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <%= link_to edit_admin_specialty_path(specialty), 
                  class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600" do %>
                ✏️ Edit
              <% end %>
              
              <%= link_to admin_specialty_path(specialty), method: :delete,
                  confirm: "Are you sure? This will remove the specialty from all artists.",
                  class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800" do %>
                🗑️ Delete
              <% end %>
            </div>
          </div>
        </li>
      <% end %>
    </ul>
  <% else %>
    <div class="text-center py-12">
      <div class="text-gray-400 dark:text-gray-500">
        <span class="text-4xl">🏷️</span>
      </div>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No specialties found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        <% if params[:search].present? %>
          Try adjusting your search.
        <% else %>
          Get started by creating your first specialty.
        <% end %>
      </p>
      <% unless params[:search].present? %>
        <div class="mt-6">
          <%= link_to new_admin_specialty_path, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black dark:bg-white600 hover:bg-black dark:bg-white700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" do %>
            Add Specialty
          <% end %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>

<!-- Summary Stats -->
<% if @specialties.any? %>
  <div class="mt-8 card-style rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Summary</h3>
      
      <dl class="grid grid-cols-1 gap-5 sm:grid-cols-3">
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Specialties</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white"><%= @specialties.count %></dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Most Popular</dt>
          <dd class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
            <% most_popular = @specialties.joins(:artist_profiles).group('specialties.name').order('COUNT(artist_profiles.id) DESC').limit(1).first %>
            <%= most_popular&.name || 'None' %>
          </dd>
        </div>
        
        <div class="px-4 py-5 bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden sm:p-6">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Unused Specialties</dt>
          <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
            <%= @specialties.left_joins(:artist_profiles).where(artist_profiles: { id: nil }).count %>
          </dd>
        </div>
      </dl>
    </div>
  </div>
<% end %>