<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'clients/shared/profile_header' %>
  <%= render 'clients/shared/tab_navigation' %>

  <!-- Section Header -->
  <div class="flex items-center justify-between mb-8">
    <div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Inspiration Boards</h2>
      <p class="mt-1 text-gray-600 dark:text-gray-400"><%= @client_profile.name %>'s curated inspiration collections</p>
    </div>
    <% if current_user == @client_profile.user %>
      <%= link_to new_client_inspiration_board_path(@client_profile), class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
        Create Board
      <% end %>
    <% end %>
  </div>

  <!-- Inspiration Boards Grid -->
  <% if @inspiration_boards.any? %>
    <div data-controller="infinite-scroll" 
         data-infinite-scroll-url-value="<%= load_more_inspiration_client_path(@client_profile) %>"
         data-infinite-scroll-page-value="1"
         data-infinite-scroll-per-page-value="24">
      
      <div data-infinite-scroll-target="container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <%= render 'inspiration_boards/inspiration_board_cards', inspiration_boards: @inspiration_boards %>
      </div>

      <!-- Loading Spinner -->
      <div data-infinite-scroll-target="loading" class="hidden">
        <%= render 'shared/loading_spinner' %>
      </div>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No inspiration boards yet</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= @client_profile.name %> hasn't created any inspiration boards yet.</p>
      <% if current_user == @client_profile.user %>
        <div class="mt-6">
          <%= link_to new_client_inspiration_board_path(@client_profile), class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600" do %>
            Create Your First Board
          <% end %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>