<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'clients/shared/profile_header' %>
  <%= render 'clients/shared/tab_navigation' %>

  <!-- Section Header -->
  <div class="flex items-center justify-between mb-8">
    <div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Profile Overview</h2>
      <p class="mt-1 text-gray-600 dark:text-gray-400">Client information and interests</p>
    </div>
  </div>

  <!-- Profile Content -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2">
      <!-- About Section -->
      <div class="card-style rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">About</h3>
        <p class="text-gray-600 dark:text-gray-400">
          Tattoo enthusiast exploring designs and connecting with talented artists.
        </p>
      </div>

      <!-- Following Artists -->
      <% if @client_profile.followed_artists.any? %>
        <div class="card-style rounded-lg p-6 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Following</h3>
            <%= link_to "View All", following_client_path(@client_profile), class: "text-sm text-black dark:bg-white600 dark:text-black dark:bg-white400 hover:text-black dark:bg-white800 dark:hover:text-black dark:bg-white300" %>
          </div>
          <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <% @client_profile.followed_artists.includes(:user).limit(6).each do |artist| %>
              <div class="text-center">
                <%= link_to artist_path(artist), class: "block hover:opacity-80 transition-opacity" do %>
                  <div class="w-16 h-16 mx-auto bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center mb-2">
                    <% if artist.profile_photo.attached? %>
                      <%= image_tag artist.profile_photo.variant(resize_to_fill: [64, 64]), class: "w-full h-full object-cover rounded-full" %>
                    <% else %>
                      <span class="text-lg font-medium text-gray-700 dark:text-gray-300">
                        <%= artist.user.name&.first&.upcase || "A" %>
                      </span>
                    <% end %>
                  </div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">@<%= artist.user.name %></p>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <!-- Contact Information -->
      <div class="card-style rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Contact Info</h3>
        
        <% if @client_profile.location.present? %>
          <div class="mb-3">
            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</span>
            <p class="text-gray-900 dark:text-white">
              📍 <%= @client_profile.location %>
            </p>
          </div>
        <% end %>

        <div class="mb-3">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Username</span>
          <p class="text-gray-900 dark:text-white">
            @<%= @client_profile.user.name %>
          </p>
        </div>
      </div>

    </div>
  </div>
</div>
