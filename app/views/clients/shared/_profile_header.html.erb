<!-- Profile Header -->
<div class="card-style rounded-lg p-8 mb-8">
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <div class="w-20 h-20 rounded-full bg-gray-300 dark:bg-gray-600 flex-shrink-0 overflow-hidden">
        <% if @client_profile.profile_photo.attached? %>
          <%= image_tag @client_profile.profile_photo.variant(resize_to_fill: [80, 80]), class: "w-full h-full object-cover" %>
        <% else %>
          <div class="w-full h-full flex items-center justify-center text-2xl font-bold text-gray-500 dark:text-gray-400">
            <%= @client_profile.user.name&.first&.upcase || "C" %>
          </div>
        <% end %>
      </div>
      <div class="ml-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= @client_profile.name %></h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">@<%= @client_profile.user.name %></p>
        <% if @client_profile.location.present? %>
          <p class="text-gray-500 dark:text-gray-500 mt-1">📍 <%= @client_profile.location %></p>
        <% end %>
      </div>
    </div>

    <div class="flex items-center space-x-4">
      <!-- Follow button for other users viewing this profile -->
      <% if current_user && current_user != @client_profile.user %>
        <% if current_user.artist? %>
          <!-- Artists can't follow clients, so don't show follow button -->
        <% end %>
      <% end %>

      <!-- Message button -->
      <% if current_user && current_user != @client_profile.user && current_user.can_message?(@client_profile.user) %>
        <%= link_to new_message_path(recipient_id: @client_profile.user.id), class: "px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-black hover:bg-white dark:hover:bg-gray-700" do %>
          Message
        <% end %>
      <% end %>
    </div>
  </div>
</div>