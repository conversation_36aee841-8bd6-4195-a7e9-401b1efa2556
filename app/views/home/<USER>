<div class="min-h-screen bg-white dark:bg-black">
  <!-- Hero Section -->
  <div class="bg-white dark:bg-black shadow dark:shadow-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="text-center">
        <h1 class="text-4xl font-extrabold text-gray-900 dark:text-white sm:text-5xl">
          Find Your Perfect Tattoo Artist
        </h1>
        <p class="mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Connect with talented tattoo artists, explore their portfolios, and find inspiration for your next piece.
        </p>
        <div class="mt-8 flex justify-center space-x-4">
          <%= link_to "Join as Client", new_registration_path, class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white" %>
          <%= link_to "Join as Artist", new_registration_path, class: "inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-black hover:bg-white dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Stats or Featured Artists -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <% if current_user %>
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
          Welcome back, <%= current_user.display_name %>!
        </h2>
        <% if current_user.client? %>
          <p class="text-gray-600 dark:text-gray-300 mt-2">Discover new artists and find inspiration for your next tattoo.</p>
        <% else %>
          <p class="text-gray-600 dark:text-gray-300 mt-2">Manage your profile and connect with potential clients.</p>
        <% end %>
      </div>
    <% else %>
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Ready to get started?</h2>
        <p class="text-gray-600 dark:text-gray-300 mt-2">Join our community of artists and tattoo enthusiasts.</p>
      </div>
    <% end %>

    <!-- Feed or Quick Actions -->
    <% if current_user&.approved_or_admin? && @showing_feed %>
      <!-- Personalized Feed for Clients -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8 mt-8">
        <!-- Main Feed -->
        <div class="lg:col-span-3">
          <div class="mb-6">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Your Feed</h2>
            <% if current_user.client_profile&.followed_artists&.any? %>
              <p class="text-gray-600 dark:text-gray-300 mt-1">Latest posts from artists you follow</p>
            <% else %>
              <p class="text-gray-600 dark:text-gray-300 mt-1">Discover amazing tattoo artists and start following them!</p>
            <% end %>
          </div>

          <% if @posts.any? %>
            <div class="space-y-8">
              <% @posts.each do |post| %>
                <article class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:bg-white dark:hover:bg-gray-800/50 transition-colors duration-300">
                  <!-- Post Header -->
                  <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-3">
                      <%= link_to artist_path(post.artist_profile), class: "flex items-center space-x-3 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" do %>
                        <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            <%= post.artist_profile.name&.first&.upcase || "A" %>
                          </span>
                        </div>
                        <div>
                          <p class="font-medium text-gray-900 dark:text-white"><%= post.artist_profile.name %></p>
                          <p class="text-sm text-gray-500 dark:text-gray-400"><%= time_ago_in_words(post.published_at) %> ago</p>
                        </div>
                      <% end %>
                    </div>
                  </div>

                  <!-- Post Content -->
                  <div class="p-6">
                    <% if post.image.attached? %>
                      <div class="mb-4">
                        <%= image_tag post.image, 
                            class: "w-full h-96 object-cover rounded-lg",
                            alt: post.caption.presence || "Post image" %>
                      </div>
                    <% end %>
                    
                    <% if post.caption.present? %>
                      <div class="text-gray-900 dark:text-white">
                        <%= simple_format(post.caption) %>
                      </div>
                    <% end %>
                  </div>

                  <!-- Post Actions -->
                  <div class="px-6 py-4 bg-white dark:bg-black border-t border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-end">
                      <div class="flex items-center space-x-3">
                        <%= link_to "View Artist", artist_path(post.artist_profile), class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" %>
                      </div>
                    </div>
                  </div>
                </article>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12 border border-gray-200 dark:border-gray-700 rounded-lg">
              <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No posts yet</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Follow some artists to see their posts in your feed.</p>
              <div class="mt-6">
                <%= link_to "Browse Artists", artists_path, 
                    class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-700 dark:bg-black dark:hover:bg-gray-700" %>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <%= link_to "Browse Artists", artists_path, class: "block w-full text-center px-4 py-2 border border-black dark:border-gray-600 rounded-md text-sm font-medium text-black dark:text-white bg-white dark:bg-black hover:bg-gray-100 dark:hover:bg-gray-700" %>
              <% 
                # Determine correct boards path based on user type
                boards_path = if current_user&.artist? && current_user.artist_profile
                  inspiration_artist_path(current_user.artist_profile)
                elsif current_user&.client? && current_user.client_profile
                  inspiration_client_path(current_user.client_profile)
                else
                  my_inspiration_boards_path
                end
              %>
              <%= link_to "My Boards", my_inspiration_boards_path, class: "block w-full text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-black hover:bg-white dark:hover:bg-gray-700" %>
              <%= link_to "Browse Artists", artists_path, class: "block w-full text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-black hover:bg-white dark:hover:bg-gray-700" %>
            </div>
          </div>

          <% if current_user.client_profile&.followed_artists&.any? %>
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Following</h3>
              <div class="space-y-3">
                <% current_user.client_profile.followed_artists.limit(5).each do |artist| %>
                  <%= link_to artist_path(artist), class: "flex items-center space-x-3 hover:bg-white dark:hover:bg-gray-700 p-2 rounded" do %>
                    <div class="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                        <%= artist.name&.first&.upcase || "A" %>
                      </span>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 dark:text-white truncate"><%= artist.name %></p>
                      <p class="text-xs text-gray-500 dark:text-gray-400 truncate"><%= artist.location %></p>
                    </div>
                  <% end %>
                <% end %>
                <% if current_user.client_profile.followed_artists.count > 5 %>
                  <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
                    <%= link_to "View All", "#", class: "text-sm text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300" %>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% else %>
      <!-- Quick Actions for Non-Feed Users -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
          <div class="text-black dark:text-white text-3xl mb-4">🎨</div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Browse Artists</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Explore portfolios from talented tattoo artists</p>
          <%= link_to "View Artists", artists_path, class: "text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 font-medium" %>
        </div>

        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
          <div class="text-black dark:text-white text-3xl mb-4">💡</div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Get Inspired</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Save designs and create inspiration boards</p>
          <% if current_user&.approved_or_admin? %>
            <% 
              # Determine correct boards path based on user type
              boards_path = if current_user&.artist? && current_user.artist_profile
                inspiration_artist_path(current_user.artist_profile)
              elsif current_user&.client? && current_user.client_profile
                inspiration_client_path(current_user.client_profile)
              else
                my_inspiration_boards_path
              end
            %>
            <%= link_to "My Boards", my_inspiration_boards_path, class: "text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 font-medium" %>
          <% else %>
            <%= link_to "Browse Artists", artists_path, class: "text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 font-medium" %>
          <% end %>
        </div>

        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
          <div class="text-black dark:text-white text-3xl mb-4">📅</div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Book Sessions</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Connect directly with artists for consultations</p>
          <span class="text-gray-400 dark:text-gray-500">Coming Soon</span>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Feed Section Header - Constrained -->
  <div class="border-t border-gray-200 dark:border-gray-700 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="mb-8">
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-2">Discover Amazing Work</h2>
        <p class="text-gray-600 dark:text-gray-300 text-center">Explore the latest tattoo art from talented artists</p>
      </div>
    </div>
    
    <!-- Portfolio Items Grid - Full Width -->
    <div class="w-full px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Recent Work</h2>
        <p class="text-gray-600 dark:text-gray-400">Discover the latest tattoo artistry from our community</p>
      </div>
      
      <div id="home-masonry-container" class="masonry-grid mb-8" data-masonry>
        <% @portfolio_items.each do |item| %>
          <%= render 'portfolio_items/portfolio_item_card', portfolio_item: item %>
        <% end %>
      </div>
      
      <!-- View All Artists Button -->
      <div class="text-center">
        <%= link_to "View All Artists", artists_path, 
            class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black" %>
      </div>
    </div>
  </div>
</div>
