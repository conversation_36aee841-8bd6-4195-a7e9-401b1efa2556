<div class="min-h-screen bg-white dark:bg-black flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
      Forgot your password?
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
      Enter your email to receive reset instructions
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="border border-gray-300 dark:border-gray-600 py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
      <!-- Flash Messages -->
      <% if alert = flash[:alert] %>
        <div class="mb-4 bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 px-4 py-3 rounded">
          <%= alert %>
        </div>
      <% end %>

      <% if notice = flash[:notice] %>
        <div class="mb-4 bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 px-4 py-3 rounded">
          <%= notice %>
        </div>
      <% end %>

      <%= form_with url: passwords_path, local: true, class: "form space-y-6" do |form| %>
        <div class="grid gap-3">
          <%= form.label :email_address, "Email Address", class: "block text-xs font-medium text-gray-900 dark:text-white" %>
          <%= form.email_field :email_address, required: true, autofocus: true, autocomplete: "username", placeholder: "Enter your email address", value: params[:email_address], class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" %>
        </div>

        <div>
          <%= form.submit "Email reset instructions", class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400" %>
        </div>

        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Remember your password?
            <%= link_to "Sign in", new_session_path, class: "font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" %>
          </p>
        </div>
      <% end %>
    </div>
  </div>
</div>
