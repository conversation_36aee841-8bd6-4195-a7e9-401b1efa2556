<% content_for :title, "Contact Support" %>

<div class="min-h-screen bg-white dark:bg-gray-900 py-12">
  <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="card-style rounded-lg">
      <div class="px-6 py-8">
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Contact Support</h1>
          <p class="text-gray-600 dark:text-gray-400">Have a question or need help? We're here to assist you!</p>
        </div>

        <% if flash[:notice] %>
          <div class="mb-6 p-4 bg-green-100 dark:bg-green-900/20 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 rounded">
            <%= flash[:notice] %>
          </div>
        <% end %>

        <% if flash[:alert] %>
          <div class="mb-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 rounded">
            <%= flash[:alert] %>
          </div>
        <% end %>

        <%= form_with url: contact_path, method: :post, local: true, class: "space-y-6" do |form| %>
          <div>
            <%= form.label :name, "Full Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
            <%= form.text_field :name, 
                value: params[:name],
                required: true,
                class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-gray-900 focus:border-gray-500" %>
          </div>

          <div>
            <%= form.label :email, "Email Address", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
            <%= form.email_field :email, 
                value: params[:email],
                required: true,
                class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-gray-900 focus:border-gray-500" %>
          </div>

          <div>
            <%= form.label :subject, "Subject (Optional)", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
            <%= form.text_field :subject, 
                value: params[:subject],
                placeholder: "Brief description of your inquiry",
                class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-gray-900 focus:border-gray-500" %>
          </div>

          <div>
            <%= form.label :message, "Message", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
            <%= form.text_area :message, 
                value: params[:message],
                required: true,
                rows: 6,
                placeholder: "Please describe your question or issue in detail...",
                class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-gray-900 focus:border-gray-500" %>
          </div>

          <div>
            <%= form.submit "Send Message", 
                class: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2 transition duration-200" %>
          </div>
        <% end %>

        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div class="text-center text-sm text-gray-600 dark:text-gray-400">
            <p class="mb-2">You can also reach us through:</p>
            <div class="space-y-1">
              <p><span class="font-medium">Response Time:</span> We typically respond within 24 hours</p>
              <p><span class="font-medium">Business Hours:</span> Monday - Friday, 9AM - 6PM EST</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>