<% content_for :title, "Message Sent" %>

<div class="min-h-screen bg-white py-12">
  <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-8 text-center">
        <div class="mb-6">
          <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Message Sent Successfully!</h1>
          <p class="text-gray-600 mb-6">Thank you for contacting us. We've received your message and will get back to you soon.</p>
        </div>

        <div class="bg-white rounded-lg p-6 mb-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-3">What happens next?</h2>
          <div class="space-y-2 text-sm text-gray-600">
            <p>• We'll review your message within 24 hours</p>
            <p>• Our support team will respond via email</p>
            <p>• For urgent issues, we'll prioritize your request</p>
          </div>
        </div>

        <div class="space-y-3">
          <%= link_to "Back to Home", root_path, 
              class: "inline-block bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 transition duration-200" %>
          
          <div class="text-sm text-gray-500">
            or
            <%= link_to "Send Another Message", new_contact_path, 
                class: "text-blue-600 hover:text-blue-700 underline" %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>