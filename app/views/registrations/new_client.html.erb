<div class="min-h-screen bg-white dark:bg-black flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
      Join as a Tattoo Client
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
      Find your perfect artist and plan your next tattoo
    </p>
    <% if @auto_approve %>
      <p class="mt-2 text-center text-sm text-green-600 dark:text-green-400">
        🎉 Special invite - your account will be approved immediately!
      </p>
    <% end %>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="border border-gray-300 dark:border-gray-600 py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
      <%= form_with model: @user, url: client_registrations_path, local: true, class: "form space-y-6" do |form| %>
        <% if @user.errors.any? %>
          <div class="mb-4 bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 px-4 py-3 rounded">
            <h4 class="font-bold">Please fix the following errors:</h4>
            <ul class="list-disc list-inside">
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>

        <% if @invite_code.present? %>
          <%= hidden_field_tag :invite_code, @invite_code %>
        <% end %>

        <%= form.hidden_field :role, value: "client" %>

        <!-- Account Details -->
        <div class="space-y-6">
          <div class="grid gap-3">
            <%= form.label :email_address, class: "block text-xs font-medium text-gray-900 dark:text-white" %>
            <%= form.email_field :email_address, required: true, class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400", placeholder: "<EMAIL>" %>
          </div>


          <div class="grid gap-3">
            <%= form.label :password, class: "block text-xs font-medium text-gray-900 dark:text-white" %>
            <%= form.password_field :password, required: true, class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" %>
          </div>

          <div class="grid gap-3">
            <%= form.label :password_confirmation, class: "block text-xs font-medium text-gray-900 dark:text-white" %>
            <%= form.password_field :password_confirmation, required: true, class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" %>
          </div>

          <!-- Profile Details -->
          <div class="grid gap-3">
            <%= form.label :name, "Full Name", class: "block text-xs font-medium text-gray-900 dark:text-white" %>
            <%= form.text_field :name, required: true, class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400", placeholder: "John Doe" %>
          </div>

          <div class="grid gap-3">
            <%= form.label :location, class: "block text-xs font-medium text-gray-900 dark:text-white" %>
            <%= form.text_field :location, class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400", placeholder: "Los Angeles, CA" %>
          </div>
        </div>

        <div class="mt-6">
          <%= form.submit "Create Client Account", class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400" %>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Are you a tattoo artist?
            <%= link_to "Join as an artist", new_artist_registration_path, class: "font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" %>
          </p>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Already have an account?
            <%= link_to "Sign in", new_session_path, class: "font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" %>
          </p>
        </div>
      <% end %>
    </div>
  </div>
</div>