<!-- Header -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="mb-8 text-center">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Discover Pieces</h1>
    <p class="mt-2 text-gray-600 dark:text-gray-400">Explore portfolio pieces from talented artists</p>
  </div>

  <!-- Search and Filters -->
  <div class="mb-8">
    <%= form_with url: pieces_path, method: :get, local: true, class: "max-w-4xl mx-auto" do |f| %>
      <!-- Filters -->
      <div class="flex flex-col md:flex-row gap-4 items-end justify-center">
        <!-- Style Filter -->
        <div>
          <el-select id="style_filter" name="style_filter" value="<%= params[:style_filter] %>" class="block min-w-48">
            <button type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white dark:bg-black py-1.5 pr-2 pl-3 text-left text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-black dark:bg-white600 sm:text-sm/6">
              <el-selectedcontent class="col-start-1 row-start-1 truncate pr-6">
                <% if params[:style_filter].present? %>
                  <%= Style.find(params[:style_filter]).title %>
                <% else %>
                  All Styles
                <% end %>
              </el-selectedcontent>
              <svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4">
                <path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </button>

            <el-options anchor="bottom start" popover class="max-h-60 w-(--button-width) overflow-auto rounded-md bg-white dark:bg-black py-1 text-base shadow-lg ring-1 ring-black/5 dark:ring-white/5 [--anchor-gap:--spacing(1)] focus:outline-hidden data-leave:transition data-leave:transition-discrete data-leave:duration-100 data-leave:ease-in data-closed:data-leave:opacity-0 sm:text-sm">
              <el-option value="" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
                <span class="block truncate font-normal group-aria-selected/option:font-semibold">All Styles</span>
                <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
                  <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                    <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
                  </svg>
                </span>
              </el-option>
              <% Style.order(:title).each do |style| %>
                <el-option value="<%= style.id %>" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
                  <span class="block truncate font-normal group-aria-selected/option:font-semibold"><%= style.title %></span>
                  <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
                    <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                      <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
                    </svg>
                  </span>
                </el-option>
              <% end %>
            </el-options>
          </el-select>
        </div>

        <!-- Location Filter -->
        <div>
          <el-select id="location_filter" name="location_filter" value="<%= params[:location_filter] %>" class="block min-w-48">
            <button type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white dark:bg-black py-1.5 pr-2 pl-3 text-left text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-black dark:bg-white600 sm:text-sm/6">
              <el-selectedcontent class="col-start-1 row-start-1 truncate pr-6">
                <% if params[:location_filter].present? %>
                  <%= params[:location_filter] %>
                <% else %>
                  All Locations
                <% end %>
              </el-selectedcontent>
              <svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4">
                <path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />
              </svg>
            </button>

            <el-options anchor="bottom start" popover class="max-h-60 w-(--button-width) overflow-auto rounded-md bg-white dark:bg-black py-1 text-base shadow-lg ring-1 ring-black/5 dark:ring-white/5 [--anchor-gap:--spacing(1)] focus:outline-hidden data-leave:transition data-leave:transition-discrete data-leave:duration-100 data-leave:ease-in data-closed:data-leave:opacity-0 sm:text-sm">
              <el-option value="" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
                <span class="block truncate font-normal group-aria-selected/option:font-semibold">All Locations</span>
                <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
                  <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                    <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
                  </svg>
                </span>
              </el-option>
              <% ArtistProfile.joins(:user).where(users: { approved: true }).where.not(location: [nil, '']).distinct.order(:location).pluck(:location).each do |location| %>
                <el-option value="<%= location %>" class="group/option relative block cursor-default py-2 pr-9 pl-3 text-gray-900 dark:text-white select-none focus:bg-black dark:bg-white600 focus:text-white focus:outline-hidden">
                  <span class="block truncate font-normal group-aria-selected/option:font-semibold"><%= location %></span>
                  <span class="absolute inset-y-0 right-0 flex items-center pr-4 text-black dark:bg-white600 group-not-aria-selected/option:hidden group-focus/option:text-white in-[el-selectedcontent]:hidden">
                    <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="size-5">
                      <path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />
                    </svg>
                  </span>
                </el-option>
              <% end %>
            </el-options>
          </el-select>
        </div>

        <!-- Search Button -->
        <div class="flex items-end">
          <%= f.submit "Search", class: "px-4 py-1.5 bg-black dark:bg-white text-white dark:text-black rounded-md hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors font-medium sm:text-sm/6" %>
        </div>

      </div>

    <% end %>
  </div>

  </div>
</div>

<!-- Results Summary - Full Width -->
<div class="w-full px-4 sm:px-6 lg:px-8 mb-4">
  <div class="flex items-center justify-between">
    <div>
      <% if @search_query.present? %>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Found <strong class="text-gray-900 dark:text-white"><%= @portfolio_items.length %></strong> 
          <%= 'piece'.pluralize(@portfolio_items.length) %> 
          matching "<strong class="text-gray-900 dark:text-white"><%= @search_query %></strong>"
        </p>
      <% else %>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Showing <strong class="text-gray-900 dark:text-white"><%= @portfolio_items.length %></strong> of 
          <strong class="text-gray-900 dark:text-white"><%= @total_pieces %></strong> pieces
        </p>
      <% end %>
    </div>
    
    <% if @search_query.present? %>
      <%= link_to "Clear Search", pieces_path, class: "text-sm text-black dark:bg-white600 dark:text-black dark:bg-white400 hover:text-black dark:bg-white800 dark:hover:text-black dark:bg-white300" %>
    <% end %>
  </div>
</div>

<!-- Portfolio Items Grid - Full Width -->
<% if @portfolio_items.any? %>
  <div class="w-full px-4 sm:px-6 lg:px-8 pb-12">
    <div data-controller="infinite-scroll" 
         data-infinite-scroll-url-value="<%= load_more_pieces_path %>"
         data-infinite-scroll-page-value="1"
         data-infinite-scroll-per-page-value="24">
      
      <%= render 'shared/masonry_grid', portfolio_items: @portfolio_items %>

      <!-- Loading Spinner -->
      <div data-infinite-scroll-target="loading" class="hidden">
        <%= render 'shared/loading_spinner' %>
      </div>
    </div>
  </div>
<% else %>
  <!-- Empty State -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
        <% if @search_query.present? %>
          No pieces found
        <% else %>
          No pieces available
        <% end %>
      </h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        <% if @search_query.present? %>
          Try adjusting your search terms or 
          <%= link_to "browse all pieces", pieces_path, class: "text-black dark:bg-white600 dark:text-black dark:bg-white400 hover:text-black dark:bg-white800 dark:hover:text-black dark:bg-white300" %>.
        <% else %>
          Check back later for new portfolio pieces.
        <% end %>
      </p>
    </div>
  </div>
<% end %>