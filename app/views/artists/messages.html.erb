<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'artists/shared/profile_header' %>
  <%= render 'artists/shared/tab_navigation' %>
  <%= render 'artists/shared/action_buttons' %>

  <!-- Messages Content -->
  <div class="card-style rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Messages</h2>
    </div>

    <% if @conversations.any? %>
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <% @conversations.each do |conversation| %>
          <% other_user = conversation.other_participant(current_user) %>
          <% latest_message = conversation.latest_message %>
          <% unread_count = conversation.unread_count(current_user) %>
          
          <%= link_to conversation, class: "block px-6 py-4 hover:bg-white dark:hover:bg-gray-700 transition-colors" do %>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 rounded-full bg-gray-300 dark:bg-gray-600 flex-shrink-0 overflow-hidden">
                  <% if other_user.profile&.profile_photo&.attached? %>
                    <%= image_tag other_user.profile.profile_photo.variant(resize_to_fill: [48, 48]), class: "w-full h-full object-cover" %>
                  <% else %>
                    <div class="w-full h-full flex items-center justify-center text-lg font-bold text-gray-500 dark:text-gray-300">
                      <%= other_user.display_name.first.upcase %>
                    </div>
                  <% end %>
                </div>
                
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                      <%= other_user.display_name %>
                    </p>
                    <% if latest_message %>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        <%= time_ago_in_words(latest_message.created_at) %> ago
                      </p>
                    <% end %>
                  </div>
                  
                  <% if latest_message %>
                    <p class="text-sm text-gray-600 dark:text-gray-400 truncate mt-1">
                      <% if latest_message.image_attached? %>
                        <span class="inline-flex items-center">
                          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                          </svg>
                          Image
                        </span>
                        <% if latest_message.content.present? %>
                          • <%= latest_message.content %>
                        <% end %>
                      <% else %>
                        <%= latest_message.content %>
                      <% end %>
                    </p>
                  <% else %>
                    <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">No messages yet</p>
                  <% end %>
                </div>
              </div>
              
              <% if unread_count > 0 %>
                <div class="flex items-center">
                  <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 rounded-full">
                    <%= unread_count %>
                  </span>
                </div>
              <% end %>
            </div>
          <% end %>
        <% end %>
      </div>
    <% else %>
      <div class="px-6 py-12 text-center">
        <svg class="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No conversations yet</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-4">
          <% if @artist_profile.messages_enabled? %>
            When people message you, their conversations will appear here.
          <% else %>
            Messages are currently disabled. Enable them in your profile settings to start receiving messages.
          <% end %>
        </p>
        
        <% unless @artist_profile.messages_enabled? %>
          <%= link_to "Enable Messages", edit_artist_path(@artist_profile), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>