<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Profile</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">Update your artist profile information</p>
      </div>
      <div>
        <%= link_to "Back to Profile", artist_path(@artist_profile), class: "text-black dark:bg-white600 dark:text-black dark:bg-white400 hover:text-black dark:bg-white800 dark:hover:text-black dark:bg-white300 text-sm font-medium" %>
      </div>
    </div>
  </div>

  <!-- Edit Form -->
  <div class="card-style rounded-lg">
    <%= form_with model: @artist_profile, url: artist_path(@artist_profile), method: :patch, local: true, class: "space-y-6" do |f| %>
      <div class="p-6">
        <!-- Profile Photo -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Profile Photo</label>
          <div class="flex items-center space-x-6">
            <div class="w-20 h-20 rounded-full bg-gray-300 dark:bg-gray-600 flex-shrink-0 overflow-hidden">
              <% if @artist_profile.profile_photo.attached? %>
                <%= image_tag @artist_profile.profile_photo.variant(resize_to_fill: [80, 80]), class: "w-full h-full object-cover" %>
              <% else %>
                <div class="w-full h-full flex items-center justify-center text-2xl font-bold text-gray-500 dark:text-gray-300">
                  <%= @artist_profile.name&.first&.upcase || "A" %>
                </div>
              <% end %>
            </div>
            <div>
              <%= f.file_field :profile_photo, class: "block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-black dark:bg-white50 dark:file:bg-black dark:bg-white900/20 file:text-black dark:bg-white700 dark:file:text-black dark:bg-white300 hover:file:bg-black dark:bg-white100 dark:hover:file:bg-black dark:bg-white900/30", accept: "image/*" %>
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">JPG, PNG up to 10MB</p>
            </div>
          </div>
        </div>

        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <%= f.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
            <%= f.text_field :name, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400" %>
            <% if @artist_profile.errors[:name].any? %>
              <p class="mt-1 text-sm text-red-600 dark:text-red-400"><%= @artist_profile.errors[:name].first %></p>
            <% end %>
          </div>

          <div>
            <%= f.label :location, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
            <%= f.text_field :location, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400", placeholder: "City, State/Country" %>
          </div>
        </div>

        <!-- Biography -->
        <div class="mb-6">
          <%= f.label :biography, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= f.text_area :biography, rows: 4, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400", placeholder: "Tell people about your experience, style, and what makes your work unique..." %>
        </div>

        <!-- Contact Information -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Contact Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= f.label :contact_email, "Contact Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= f.email_field :contact_email, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400", placeholder: "<EMAIL>" %>
            </div>

            <div>
              <%= f.label :instagram_url, "Instagram URL", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= f.url_field :instagram_url, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400", placeholder: "https://instagram.com/yourusername" %>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            <div>
              <%= f.label :website_url, "Website URL", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= f.url_field :website_url, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400", placeholder: "https://yourwebsite.com" %>
            </div>

            <div>
              <%= f.label :studio_link, "Studio Link", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
              <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">Link to Studio Website/Social</p>
              <%= f.url_field :studio_link, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400", placeholder: "https://mystudio.com or https://instagram.com/mystudio" %>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            <div>
              <%= f.label :booking_link, "Booking Link", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= f.url_field :booking_link, class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400 focus:border-black dark:bg-white500 dark:focus:border-black dark:bg-white400", placeholder: "https://bookings.yoursite.com" %>
            </div>
          </div>
        </div>

        <!-- Styles and Specialties -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Styles & Specialties</h3>
          
          <!-- Styles Selection -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Styles <span class="text-gray-500 dark:text-gray-400">(Select all styles you work with)</span>
            </label>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3" id="styles-selection">
              <% Style.order(:title).each do |style| %>
                <label class="relative flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-white dark:hover:bg-gray-700 transition-colors">
                  <%= check_box_tag "artist_profile[style_ids][]", style.id, 
                      @artist_profile.styles.include?(style), 
                      class: "sr-only peer style-checkbox", 
                      data: { style_id: style.id } %>
                  <div class="flex items-center">
                    <div class="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 rounded peer-checked:bg-black dark:bg-white600 peer-checked:border-black dark:bg-white600 peer-checked:after:content-['✓'] peer-checked:after:text-white peer-checked:after:text-xs peer-checked:after:font-bold peer-checked:after:flex peer-checked:after:items-center peer-checked:after:justify-center"></div>
                    <span class="ml-2 text-sm text-gray-900 dark:text-white"><%= style.title %></span>
                  </div>
                </label>
              <% end %>
            </div>
          </div>

          <!-- Specialties Selection -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Specialties <span class="text-gray-500 dark:text-gray-400">(Choose up to 3 from your selected styles)</span>
            </label>
            <div class="bg-white dark:bg-gray-700 rounded-lg p-4">
              <div id="specialty-selection" class="grid grid-cols-2 md:grid-cols-3 gap-3">
                <!-- Specialties will be populated by JavaScript based on selected styles -->
              </div>
              <div id="no-styles-message" class="text-center text-gray-500 dark:text-gray-400 text-sm py-4">
                Select styles above to choose your specialties
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- Form Actions -->
      <div class="px-6 py-4 bg-white dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-b-lg">
        <div class="flex items-center justify-between">
          <div>
            <%= link_to artist_path(@artist_profile), class: "text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm font-medium" do %>
              Cancel
            <% end %>
          </div>
          <div class="flex space-x-3">
            <%= f.submit "Save Changes", class: "px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-white500 dark:focus:ring-offset-gray-800" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Available styles data
  const styles = <%= raw Style.order(:title).to_json(only: [:id, :title]) %>;
  const currentSpecialties = []; // Removed specialties feature
  
  const styleCheckboxes = document.querySelectorAll('.style-checkbox');
  const specialtyContainer = document.getElementById('specialty-selection');
  const noStylesMessage = document.getElementById('no-styles-message');
  
  function updateSpecialtyOptions() {
    const selectedStyleIds = Array.from(styleCheckboxes)
      .filter(cb => cb.checked)
      .map(cb => parseInt(cb.dataset.styleId));
    
    // Clear existing specialty options
    specialtyContainer.innerHTML = '';
    
    if (selectedStyleIds.length === 0) {
      noStylesMessage.style.display = 'block';
      return;
    }
    
    noStylesMessage.style.display = 'none';
    
    // Create specialty options for selected styles
    selectedStyleIds.forEach(styleId => {
      const style = styles.find(s => s.id === styleId);
      if (!style) return;
      
      const isCurrentSpecialty = currentSpecialties.includes(styleId);
      
      const label = document.createElement('label');
      label.className = 'relative flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-white dark:hover:bg-gray-700 transition-colors';
      
      label.innerHTML = `
        <input type="checkbox" 
               name="artist_profile[specialty_ids][]" 
               value="${styleId}"
               class="sr-only peer specialty-checkbox"
               ${isCurrentSpecialty ? 'checked' : ''}
               data-style-id="${styleId}">
        <div class="flex items-center">
          <div class="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 rounded peer-checked:bg-black dark:bg-white600 peer-checked:border-black dark:bg-white600 peer-checked:after:content-['✓'] peer-checked:after:text-white peer-checked:after:text-xs peer-checked:after:font-bold peer-checked:after:flex peer-checked:after:items-center peer-checked:after:justify-center"></div>
          <span class="ml-2 text-sm text-gray-900 dark:text-white">${style.title}</span>
        </div>
      `;
      
      specialtyContainer.appendChild(label);
    });
    
    // Add event listeners to specialty checkboxes
    const specialtyCheckboxes = document.querySelectorAll('.specialty-checkbox');
    specialtyCheckboxes.forEach(cb => {
      cb.addEventListener('change', function() {
        const checkedCount = Array.from(specialtyCheckboxes).filter(cb => cb.checked).length;
        if (checkedCount > 3) {
          this.checked = false;
          alert('You can select a maximum of 3 specialties.');
        }
      });
    });
  }
  
  // Listen for style checkbox changes
  styleCheckboxes.forEach(cb => {
    cb.addEventListener('change', function() {
      // If unchecking a style, remove it from specialties
      if (!this.checked) {
        const styleId = parseInt(this.dataset.styleId);
        const specialtyCheckboxes = document.querySelectorAll('.specialty-checkbox');
        specialtyCheckboxes.forEach(scb => {
          if (parseInt(scb.dataset.styleId) === styleId) {
            scb.checked = false;
          }
        });
      }
      updateSpecialtyOptions();
    });
  });
  
  // Initialize specialty options
  updateSpecialtyOptions();
});
</script>