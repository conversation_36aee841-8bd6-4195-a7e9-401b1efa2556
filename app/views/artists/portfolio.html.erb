<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'artists/shared/profile_header' %>
  <%= render 'artists/shared/tab_navigation' %>

  <!-- Section Header -->
  <div class="flex items-center justify-between mb-8">
    <div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Portfolio</h2>
      <p class="mt-1 text-gray-600 dark:text-gray-400">Curated collection of <%= @artist_profile.name %>'s best work</p>
    </div>
    <% if current_user&.artist? && current_user.artist_profile == @artist_profile %>
      <div>
        <%= link_to portfolio_items_path, class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
          Manage Portfolio
        <% end %>
      </div>
    <% end %>
  </div>

  <!-- Portfolio Grid -->
  <% if @portfolio_items.any? %>
    <div data-controller="infinite-scroll" 
         data-infinite-scroll-url-value="<%= load_more_portfolio_artist_path(@artist_profile) %>"
         data-infinite-scroll-page-value="1"
         data-infinite-scroll-per-page-value="24">
      
      <div data-infinite-scroll-target="container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <%= render 'portfolio_items/portfolio_item_cards', portfolio_items: @portfolio_items %>
      </div>

      <!-- Loading Spinner -->
      <div data-infinite-scroll-target="loading" class="hidden">
        <%= render 'shared/loading_spinner' %>
      </div>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No portfolio items yet</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= @artist_profile.name %> hasn't added any items to their portfolio yet.</p>
    </div>
  <% end %>
</div>
