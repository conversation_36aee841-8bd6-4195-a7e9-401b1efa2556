<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'artists/shared/profile_header' %>
  <%= render 'artists/shared/tab_navigation' %>

  <!-- Section Header -->
  <div class="flex items-center justify-between mb-8">
    <div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Profile Overview</h2>
      <p class="mt-1 text-gray-600 dark:text-gray-400">Artist information and background</p>
    </div>
    <% if current_user&.artist? && current_user.artist_profile == @artist_profile %>
      <div class="flex space-x-3">
        <%= link_to edit_artist_path(@artist_profile), class: "px-4 py-2 border border-black dark:bg-white600 dark:border-black dark:bg-white400 rounded-md text-sm font-medium text-black dark:bg-white600 dark:text-black dark:bg-white400 bg-white dark:bg-black hover:bg-black dark:bg-white50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
          Edit Profile
        <% end %>
      </div>
    <% end %>
  </div>

  <!-- Profile Content -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2">
      <!-- Biography -->
      <% if @artist_profile.biography.present? %>
        <div class="card-style rounded-lg p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">About</h3>
          <p class="text-gray-700 dark:text-white leading-relaxed"><%= @artist_profile.biography %></p>
        </div>
      <% end %>

      <!-- Styles -->
      <% if @artist_profile.styles.any? %>
        <div class="card-style rounded-lg p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Styles</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Tattoo styles this artist specializes in</p>
          <div class="flex flex-wrap gap-2">
            <% @artist_profile.styles.each do |style| %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                <%= style.title %>
              </span>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Styles -->
      <% if @artist_profile.styles.any? %>
        <div class="card-style rounded-lg p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Styles</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">All styles I work with</p>
          <div class="flex flex-wrap gap-2">
            <% @artist_profile.styles.each do |style| %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                <%= style.title %>
              </span>
            <% end %>
          </div>
        </div>
      <% end %>

    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <!-- Links -->
      <div class="card-style rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Links</h3>
        
        <% if @artist_profile.contact_email.present? %>
          <div class="mb-4">
            <%= mail_to "mailto:#{@artist_profile.contact_email}", target: "_blank", class: "flex items-center text-gray-900 dark:text-white group" do %>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5" />
              </svg>
              <span class="relative group-hover:text-black dark:bg-white600 dark:group-hover:text-black dark:bg-white400 transition-colors duration-200">
                Email me
                <span class="absolute left-0 bottom-0 w-0 h-0.5 bg-black dark:bg-white600 dark:bg-black dark:bg-white400 group-hover:w-full transition-all duration-300 ease-out"></span>
              </span>
            <% end %>
          </div>
        <% end %>

        <% if @artist_profile.instagram_url.present? %>
          <div class="mb-4">
            <%= link_to @artist_profile.instagram_url, target: "_blank", class: "flex items-center text-gray-900 dark:text-white group" do %>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z" />
              </svg>
              <span class="relative group-hover:text-black dark:bg-white600 dark:group-hover:text-black dark:bg-white400 transition-colors duration-200">
                Visit my Instagram
                <span class="absolute left-0 bottom-0 w-0 h-0.5 bg-black dark:bg-white600 dark:bg-black dark:bg-white400 group-hover:w-full transition-all duration-300 ease-out"></span>
              </span>
            <% end %>
          </div>
        <% end %>
        <% if @artist_profile.website_url.present? %>
          <div class="mb-4">
            <%= link_to @artist_profile.website_url, target: "_blank", class: "flex items-center text-gray-900 dark:text-white group" do %>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418" />
              </svg>
              <span class="relative group-hover:text-black dark:bg-white600 dark:group-hover:text-black dark:bg-white400 transition-colors duration-200">
                Visit my website
                <span class="absolute left-0 bottom-0 w-0 h-0.5 bg-black dark:bg-white600 dark:bg-black dark:bg-white400 group-hover:w-full transition-all duration-300 ease-out"></span>
              </span>
            <% end %>
          </div>
        <% end %>

        <% if @artist_profile.studio_link.present? %>
          <div class="mb-4">
            <%= link_to @artist_profile.studio_link, target: "_blank", class: "flex items-center text-gray-900 dark:text-white group" do %>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h10.5M6.75 12h10.5m-10.5 4.5h10.5m-10.5-9h10.5M3.375 4.875c0-.621.504-1.125 1.125-1.125h15c.621 0 1.125.504 1.125 1.125v.75c0 .621-.504 1.125-1.125 1.125h-15A1.125 1.125 0 0 1 3.375 5.625v-.75Z" />
              </svg>
              <span class="relative group-hover:text-black dark:bg-white600 dark:group-hover:text-black dark:bg-white400 transition-colors duration-200">
                Check out my studio
                <span class="absolute left-0 bottom-0 w-0 h-0.5 bg-black dark:bg-white600 dark:bg-black dark:bg-white400 group-hover:w-full transition-all duration-300 ease-out"></span>
              </span>
            <% end %>
          </div>
        <% end %>

      </div>

      <!-- Availability Status -->
      <div class="card-style rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Availability</h3>
        <% if true # Always show as available since we removed the available field %>
          <div class="mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300">
              ✓ Available for bookings
            </span>
          </div>
          
          <% if @artist_profile.booking_link.present? %>
            <%= link_to @artist_profile.booking_link, target: "_blank", class: "w-full inline-flex justify-center items-center py-3 px-4 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 dark:bg-green-500 hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-green-500 group transition-colors duration-200" do %>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
              </svg>
              <span class="relative">
                Book Appointment
                <span class="absolute left-0 bottom-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300 ease-out"></span>
              </span>
            <% end %>
          <% end %>
        <% else %>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300">
            ✗ Not currently taking bookings
          </span>
        <% end %>
        
        <!-- Waitlist Button -->
        <% if @artist_profile.waitlist_enabled? %>
          <div class="mt-4">
            <%= link_to new_artist_waitlist_entry_path(@artist_profile), 
                class: "w-full inline-flex justify-center items-center py-3 px-4 border border-black dark:bg-white600 dark:border-black dark:bg-white400 rounded-md text-sm font-medium text-black dark:bg-white600 dark:text-black dark:bg-white400 bg-white dark:bg-black hover:bg-black dark:bg-white50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500 transition-colors duration-200" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              Join Waitlist
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
