<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Artist Profile Header -->
  <%= render 'artists/shared/profile_header' %>
  <%= render 'artists/shared/tab_navigation' %>

  <!-- Section Header -->
  <div class="flex items-center justify-between mb-8">
    <div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Tagged Tattoos</h2>
      <p class="mt-1 text-gray-600 dark:text-gray-400">
        Tattoos where <%= @artist_profile.name %> has been tagged as the artist
      </p>
    </div>
  </div>

  <!-- Tattoo Grid -->
  <% if @tattoo_items.any? %>
    <div data-controller="infinite-scroll" 
         data-infinite-scroll-url-value="<%= load_more_tags_artist_path(@artist_profile) %>"
         data-infinite-scroll-page-value="1"
         data-infinite-scroll-per-page-value="24">
      
      <div data-infinite-scroll-target="container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <%= render 'tattoo_items/tattoo_item_cards', tattoo_items: @tattoo_items %>
      </div>

      <!-- Loading Spinner -->
      <div data-infinite-scroll-target="loading" class="hidden">
        <%= render 'shared/loading_spinner' %>
      </div>
    </div>
  <% else %>
    <!-- Empty state -->
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No tagged tattoos yet</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        When clients add tattoos to their collections and tag <%= @artist_profile.name %> as the artist, they'll appear here.
      </p>
    </div>
  <% end %>
</div>