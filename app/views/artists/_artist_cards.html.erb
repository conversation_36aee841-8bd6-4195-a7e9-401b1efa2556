<% artist_profiles.each do |artist| %>
  <div class="group bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-xl overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-500 ease-in-out">
    <%= link_to artist_path(artist), class: "block" do %>
      <article class="">
        <!-- Header with Avatar and Username -->
        <div class="p-4">
          <div class="flex items-center space-x-3">
            <!-- Artist Avatar -->
            <div class="flex-shrink-0">
              <% if artist.user.profile&.profile_photo&.attached? %>
                <%= image_tag artist.user.profile.profile_photo.variant(resize_to_fill: [24, 24]), 
                    class: "w-6 h-6 rounded-full object-cover" %>
              <% else %>
                <div class="flex items-center justify-center w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full">
                  <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                    <%= artist.name.first.upcase %>
                  </span>
                </div>
              <% end %>
            </div>
            
            <!-- Artist Info -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                <%= artist.name %>
              </p>
            </div>
          </div>
        </div>

        <!-- Full-width Portfolio Image -->
        <div class="aspect-[4/3] overflow-hidden bg-gray-200 dark:bg-gray-700">
          <% portfolio_item = artist.portfolio_items.with_attached_image.sample %>
          <% if portfolio_item&.image&.attached? %>
            <%= image_tag portfolio_item.image, 
                class: "w-full h-full object-cover", 
                alt: "#{artist.name}'s artwork",
                loading: "lazy" %>
          <% elsif artist.profile_photo.attached? %>
            <%= image_tag artist.profile_photo, 
                class: "w-full h-full object-cover", 
                alt: "#{artist.name}'s profile",
                loading: "lazy" %>
          <% else %>
            <div class="w-full h-full flex items-center justify-center">
              <div class="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                <span class="text-xl font-bold text-gray-600 dark:text-gray-300">
                  <%= artist.name&.first&.upcase || "A" %>
                </span>
              </div>
            </div>
          <% end %>
        </div>
        
        <!-- Badge Section -->
        <div class="p-4 space-y-3">
          <!-- Posts and Location Row -->
          <div class="flex items-center space-x-3">
            <!-- Posts Badge -->
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
              <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
              </svg>
              <%= artist.portfolio_items.count %>
            </span>

            <!-- Location Badge -->
            <% if artist.location.present? %>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                </svg>
                <%= artist.location %>
              </span>
            <% end %>
          </div>

          <!-- Style Badges -->
          <% display_styles = artist.styles.limit(3) %>
          <% if display_styles.any? %>
            <div class="flex flex-wrap gap-2">
              <% display_styles.each do |style| %>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                  <%= style.title %>
                </span>
              <% end %>
            </div>
          <% end %>
        </div>
      </article>
    <% end %>

    <!-- Follow Button -->
    <% if current_user&.client? && current_user.approved_or_admin? %>
      <div class="p-4 pt-0">
        <% following = current_user.client_profile&.follows&.exists?(artist_profile: artist) %>
        <% if following %>
          <%= button_to follows_path, method: :delete, params: { artist_profile_id: artist.id }, 
              class: "w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors text-sm font-medium" do %>
            Following
          <% end %>
        <% else %>
          <%= button_to follows_path, params: { artist_profile_id: artist.id }, 
              class: "w-full px-4 py-2 bg-gray-900 dark:bg-gray-700 text-white dark:text-gray-100 rounded-md hover:bg-gray-800 dark:hover:bg-gray-600 transition-colors text-sm font-medium" do %>
            Follow
          <% end %>
        <% end %>
      </div>
    <% end %>
  </div>
<% end %>