<%
  # Helper to check if current path matches or is a nested route under a base path
  def active_tab?(base_path)
    current_page?(base_path) || request.path.start_with?(base_path)
  end
  
  # Define base paths for nested route checking
  overview_path = artist_path(@artist_profile)
  portfolio_base_path = portfolio_artist_path(@artist_profile)
  tags_base_path = tags_artist_path(@artist_profile)
  inspiration_base_path = inspiration_artist_path(@artist_profile)
%>

<!-- Navigation Tabs -->
<div class="border-b border-gray-200 dark:border-gray-700 mb-8">
  <nav class="-mb-px flex space-x-8">
    <%= link_to overview_path, class: "#{active_tab?(overview_path) && !active_tab?(portfolio_base_path) && !active_tab?(tags_base_path) && !active_tab?(inspiration_base_path) ? 'border-black dark:bg-white500 dark:border-black dark:bg-white400 text-black dark:bg-white600 dark:text-black dark:bg-white400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
      Overview
    <% end %>
    <%= link_to portfolio_base_path, class: "#{active_tab?(portfolio_base_path) ? 'border-black dark:bg-white500 dark:border-black dark:bg-white400 text-black dark:bg-white600 dark:text-black dark:bg-white400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
      Portfolio
    <% end %>
    <%= link_to tags_base_path, class: "#{active_tab?(tags_base_path) ? 'border-black dark:bg-white500 dark:border-black dark:bg-white400 text-black dark:bg-white600 dark:text-black dark:bg-white400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
      Tags
    <% end %>
    <%= link_to inspiration_base_path, class: "#{active_tab?(inspiration_base_path) ? 'border-black dark:bg-white500 dark:border-black dark:bg-white400 text-black dark:bg-white600 dark:text-black dark:bg-white400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
      Inspiration
    <% end %>
  </nav>
</div>