<!-- Action Buttons -->
<div class="mb-8">
  <div class="flex flex-wrap gap-3 justify-end">
    <% if current_user&.artist? && current_user.artist_profile == @artist_profile %>
      <!-- Page-specific buttons for artist owner -->
      <% if current_page?(artist_path(@artist_profile)) %>
        <!-- Show page: Only Edit Profile -->
        <%= link_to edit_artist_path(@artist_profile), class: "px-4 py-2 border border-black dark:bg-white600 dark:border-black dark:bg-white400 rounded-md text-sm font-medium text-black dark:bg-white600 dark:text-black dark:bg-white400 bg-white dark:bg-black hover:bg-black dark:bg-white50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
          Edit Profile
        <% end %>
      <% elsif current_page?(portfolio_artist_path(@artist_profile)) || current_page?(posts_artist_path(@artist_profile)) %>
        <!-- Portfolio and Posts pages: Only Add Post -->
        <%= link_to new_post_path, class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
          Add Post
        <% end %>
      <% elsif current_page?(inspiration_artist_path(@artist_profile)) %>
        <!-- Inspiration page: New Board -->
        <%= link_to "New Board", new_artist_inspiration_board_path(@artist_profile), class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 dark:bg-green-500 hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-green-500" %>
      <% end %>
      
    <% elsif current_user&.client? %>
      <!-- Client buttons for viewing artist profiles -->
      <!-- Message Button -->
      <% if @artist_profile.messages_enabled? && current_user.can_message?(@artist_profile.user) %>
        <%= link_to new_message_path(recipient_id: @artist_profile.user.id), class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
          Message
        <% end %>
      <% end %>
    <% end %>
  </div>
</div>