<!-- Profile Header -->
<div class="card-style rounded-lg p-8 mb-8">
  <div class="flex flex-col md:flex-row items-start md:items-center justify-between">
    <div class="flex items-center mb-4 md:mb-0">
      <div class="w-20 h-20 rounded-full bg-gray-300 dark:bg-gray-600 flex-shrink-0 overflow-hidden">
        <% if @artist_profile.profile_photo.attached? %>
          <%= image_tag @artist_profile.profile_photo.variant(resize_to_fill: [80, 80]), class: "w-full h-full object-cover" %>
        <% else %>
          <div class="w-full h-full flex items-center justify-center text-2xl font-bold text-gray-500 dark:text-gray-300">
            <%= @artist_profile.name.first.upcase %>
          </div>
        <% end %>
      </div>
      <div class="ml-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= @artist_profile.name %></h1>
        <% if @artist_profile.location.present? %>
          <p class="text-gray-600 dark:text-gray-400 mt-1">📍 <%= @artist_profile.location %></p>
        <% end %>
      </div>
    </div>
    
    <!-- Artist Badge -->
    <div class="flex items-center">
      <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-black dark:bg-white100 dark:bg-black dark:bg-white900/30 text-black dark:bg-white800 dark:text-black dark:bg-white200 border border-black dark:bg-white200 dark:border-black dark:bg-white700">
        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
        </svg>
        Artist
      </span>
    </div>
  </div>
</div>