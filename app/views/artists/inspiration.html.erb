<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'artists/shared/profile_header' %>
  <%= render 'artists/shared/tab_navigation' %>

  <!-- Section Header -->
  <div class="flex items-center justify-between mb-8">
    <div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Inspiration</h2>
      <p class="mt-1 text-gray-600 dark:text-gray-400">Curated inspiration boards by <%= @artist_profile.name %></p>
    </div>
    <% if current_user&.artist? && current_user.artist_profile == @artist_profile %>
      <%= link_to artist_inspiration_boards_path(@artist_profile), class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
        Manage Boards
      <% end %>
    <% end %>
  </div>

  <!-- Inspiration Boards Content -->
  <div class="space-y-6">
    <% if @inspiration_boards.any? %>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @inspiration_boards.each do |board| %>
          <div class="card-style rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
            <%= link_to artist_inspiration_board_path(@artist_profile, board), class: "block" do %>
              <div class="p-6">
                <div class="flex items-center justify-between mb-3">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><%= board.name %></h3>
                  <span class="text-xs px-2 py-1 rounded-full <%= board.public? ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
                    <%= board.public? ? 'Public' : 'Private' %>
                  </span>
                </div>
                
                <% if board.description.present? %>
                  <p class="text-gray-600 dark:text-gray-400 text-sm mb-4"><%= truncate(board.description, length: 100) %></p>
                <% end %>
                
                <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span><%= pluralize(board.inspiration_board_items.count, 'item') %></span>
                  <span><%= time_ago_in_words(board.updated_at) %> ago</span>
                </div>
              </div>
              
              <% 
                # Get images from both posts and portfolio items
                preview_items = []
                board.inspiration_board_items.includes(:post, :portfolio_item).limit(4).each do |item|
                  if item.post&.image&.attached?
                    preview_items << { image: item.post.image, type: 'post' }
                  elsif item.portfolio_item&.image&.attached?
                    preview_items << { image: item.portfolio_item.image, type: 'portfolio' }
                  end
                end
              %>
              <% if preview_items.any? %>
                <div class="grid grid-cols-2 gap-1 p-2">
                  <% preview_items.each do |item| %>
                    <div class="aspect-square bg-gray-200 dark:bg-gray-600 rounded overflow-hidden relative">
                      <%= image_tag item[:image], class: "w-full h-full object-cover" %>
                      <% if item[:type] == 'portfolio' %>
                        <div class="absolute top-1 right-1">
                          <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300">
                            P
                          </span>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                  <div class="text-center text-gray-400 dark:text-gray-500">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                    <p class="text-sm">Empty board</p>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No inspiration boards yet</h3>
        <% if current_user == @artist_profile.user %>
          <p class="text-gray-500 dark:text-gray-400 mb-4">Start creating inspiration boards to organize your ideas and references.</p>
          <%= link_to "Manage Boards", artist_inspiration_boards_path(@artist_profile), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" %>
        <% else %>
          <p class="text-gray-500 dark:text-gray-400">This artist hasn't created any public inspiration boards yet.</p>
        <% end %>
      </div>
    <% end %>
  </div>
</div>