<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
  <%= render 'artists/shared/profile_header' %>
  <%= render 'artists/shared/tab_navigation' %>

  <!-- Breadcrumb -->
  <nav class="flex mb-8" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <%= link_to portfolio_artist_path(current_user.artist_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
          Portfolio
        <% end %>
      </li>
      <li class="inline-flex items-center">
        <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
        </svg>
        <%= link_to portfolio_items_path, class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
          Manage
        <% end %>
      </li>
      <li aria-current="page">
        <div class="flex items-center">
          <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
          </svg>
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Add Item</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Page Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Add Portfolio Item</h1>
    <p class="mt-1 text-gray-600 dark:text-gray-400">Upload a new piece to showcase in your portfolio</p>
  </div>

  <!-- Form Card -->
  <div class="max-w-2xl card-style rounded-lg p-6">
    <%= render 'form', portfolio_item: @portfolio_item %>
  </div>
</div>