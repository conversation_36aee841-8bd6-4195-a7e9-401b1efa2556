<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Portfolio Item Display -->
  <div class="card-style rounded-lg overflow-hidden">
    <!-- Image -->
    <% if @portfolio_item.image.attached? %>
      <div class="aspect-w-16 aspect-h-12">
        <%= image_tag @portfolio_item.image, 
            class: "w-full max-h-96 object-contain bg-gray-200 dark:bg-gray-900",
            alt: @portfolio_item.caption.presence || "Portfolio item" %>
      </div>
    <% end %>
    
    <!-- Content -->
    <div class="p-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <% if @portfolio_item.caption.present? %>
            <p class="text-lg text-gray-900 dark:text-white mb-4">
              <%= simple_format(@portfolio_item.caption) %>
            </p>
          <% end %>
          
          <div class="text-sm text-gray-500 dark:text-gray-400">
            <p>Added <%= time_ago_in_words(@portfolio_item.created_at) %> ago</p>
            <p>Portfolio position: <%= @portfolio_item.position + 1 %></p>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="ml-6 flex space-x-3">
          <%= link_to "Edit", edit_portfolio_item_path(@portfolio_item), 
              class: "px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-black hover:bg-white dark:hover:bg-gray-700" %>
          <%= link_to "Delete", portfolio_item_path(@portfolio_item), 
              method: :delete,
              class: "px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-black hover:bg-red-50 dark:hover:bg-red-900/20",
              data: { 
                turbo_method: :delete,
                turbo_confirm: "Are you sure you want to delete this portfolio item?" 
              } %>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Back Link -->
  <div class="mt-6">
    <%= link_to "← Back to Portfolio", portfolio_items_path, 
        class: "text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium" %>
  </div>
</div>