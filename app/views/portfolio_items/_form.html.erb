<%= form_with model: portfolio_item, local: true, class: "space-y-6" do |form| %>
  <% if portfolio_item.errors.any? %>
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(portfolio_item.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul class="list-disc pl-5 space-y-1">
              <% portfolio_item.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Image Upload -->
  <div>
    <%= form.label :image, "Image", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
    <%= form.file_field :image, 
        accept: "image/*",
        class: "block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-900 dark:focus:ring-white focus:border-gray-500 dark:focus:border-gray-400",
        data: { action: "change->image-preview#preview" } %>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Upload a high-quality image of your tattoo work. JPG, PNG, or GIF formats accepted.
    </p>
    
    <!-- Image Preview -->
    <% if portfolio_item.image.attached? %>
      <div class="mt-3">
        <%= image_tag portfolio_item.image, class: "w-48 h-48 object-cover rounded-lg", alt: "Current image" %>
      </div>
    <% end %>
  </div>

  <!-- Caption -->
  <div>
    <%= form.label :caption, "Caption (Optional)", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
    <%= form.text_area :caption, 
        rows: 3,
        placeholder: "Describe this piece, technique used, inspiration, etc.",
        class: "block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-900 dark:focus:ring-white focus:border-gray-500 dark:focus:border-gray-400" %>
  </div>

  <!-- Styles Selection -->
  <div>
    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
      Styles <span class="text-gray-500 dark:text-gray-400">(Select all styles that apply to this piece)</span>
    </label>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
      <% Style.order(:title).each do |style| %>
        <label class="relative flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          <%= check_box_tag "portfolio_item[style_ids][]", style.id, 
              portfolio_item.styles.include?(style), 
              class: "sr-only peer" %>
          <div class="flex items-center">
            <div class="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 rounded peer-checked:bg-gray-900 dark:peer-checked:bg-white peer-checked:border-gray-900 dark:peer-checked:border-white peer-checked:after:content-['✓'] peer-checked:after:text-white dark:peer-checked:after:text-gray-900 peer-checked:after:text-xs peer-checked:after:font-bold peer-checked:after:flex peer-checked:after:items-center peer-checked:after:justify-center"></div>
            <span class="ml-2 text-sm text-gray-900 dark:text-white"><%= style.title %></span>
          </div>
        </label>
      <% end %>
    </div>
    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      Select the tattoo styles that best describe this piece of work.
    </p>
  </div>

  <!-- Form Actions -->
  <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
    <div>
      <%= link_to "Cancel", portfolio_items_path, class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" %>
      <% if portfolio_item.persisted? %>
        <%= link_to "Delete", portfolio_item_path(portfolio_item), 
            method: :delete,
            class: "ml-4 text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 font-medium",
            data: { 
              turbo_method: :delete,
              turbo_confirm: "Are you sure you want to delete this portfolio item?" 
            } %>
      <% end %>
    </div>
    <%= form.submit portfolio_item.persisted? ? "Update Item" : "Add to Portfolio", 
        class: "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-md transition duration-150 ease-in-out disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 dark:focus:ring-offset-gray-800" %>
  </div>
<% end %>