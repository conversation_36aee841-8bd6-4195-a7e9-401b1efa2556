<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
  <%= render 'artists/shared/profile_header' %>
  <%= render 'artists/shared/tab_navigation' %>

  <!-- Breadcrumb -->
  <nav class="flex mb-8" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <%= link_to portfolio_artist_path(current_user.artist_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
          Portfolio
        <% end %>
      </li>
      <li aria-current="page">
        <div class="flex items-center">
          <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
          </svg>
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Manage</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Page Header -->
  <div class="flex items-center justify-between mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Portfolio Management</h1>
      <p class="mt-1 text-gray-600 dark:text-gray-400">Manage your portfolio items</p>
    </div>
    <%= link_to new_portfolio_item_path, class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" do %>
      Add Item
    <% end %>
  </div>

  <!-- Flash Messages -->
  <% if notice %>
    <div class="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 px-4 py-3 rounded">
      <%= notice %>
    </div>
  <% end %>

  <% if alert %>
    <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded">
      <%= alert %>
    </div>
  <% end %>

  <!-- Portfolio Items Grid -->
  <% if @portfolio_items.any? %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @portfolio_items.each do |portfolio_item| %>
        <div class="card-style rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200">
          <!-- Image -->
          <% if portfolio_item.image.attached? %>
            <div class="aspect-w-16 aspect-h-12">
              <%= image_tag portfolio_item.image, 
                  class: "w-full h-48 object-cover",
                  alt: portfolio_item.description.presence || "Portfolio item" %>
            </div>
          <% end %>
          
          <!-- Content -->
          <div class="p-4">
            <% if portfolio_item.description.present? %>
              <p class="text-sm text-gray-900 dark:text-white mb-4">
                <%= truncate(portfolio_item.description, length: 80) %>
              </p>
            <% end %>
          </div>
          
          <!-- Bottom Actions -->
          <div class="px-4 pb-4 flex items-center justify-end">
            <!-- Edit -->
            <%= link_to "Edit", edit_portfolio_item_path(portfolio_item), 
                class: "text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium" %>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No portfolio items yet</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first portfolio item.</p>
      <div class="mt-6">
        <%= link_to new_portfolio_item_path, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600" do %>
          Add Item
        <% end %>
      </div>
    </div>
  <% end %>
</div>