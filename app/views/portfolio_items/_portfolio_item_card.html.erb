<div class="masonry-item bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-xl overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-500 ease-in-out mb-4" data-controller="hover-animation">
  <!-- Artist Header -->
  <div class="p-4">
    <%= link_to artist_path(portfolio_item.artist_profile), class: "block" do %>
      <div class="flex items-center space-x-3">
        <!-- Artist Avatar -->
        <div class="flex-shrink-0">
          <% if portfolio_item.artist_profile.user.profile&.profile_photo&.attached? %>
            <%= image_tag portfolio_item.artist_profile.user.profile.profile_photo.variant(resize_to_fill: [24, 24]), 
                class: "w-6 h-6 rounded-full object-cover" %>
          <% else %>
            <div class="flex items-center justify-center w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full">
              <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                <%= portfolio_item.artist_profile.name.first.upcase %>
              </span>
            </div>
          <% end %>
        </div>
        
        <!-- Artist Info -->
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 dark:text-white">
            <%= portfolio_item.artist_profile.name %>
          </p>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Image -->
  <% if portfolio_item.image.attached? %>
    <div class="relative">
      <%= image_tag portfolio_item.image, 
          class: "w-full object-cover",
          alt: portfolio_item.caption.presence || "Portfolio item",
          loading: "lazy" %>
    </div>
  <% end %>
  
  <!-- Footer with Save Button and Styles -->
  <div class="p-4 space-y-3">
    <!-- Save Button -->
    <% if current_user&.approved_or_admin? && current_user != portfolio_item.artist_profile.user %>
      <div class="flex justify-end">
        <button type="button" 
                data-portfolio-item-id="<%= portfolio_item.id %>"
                data-action="click->save-to-board#showForPortfolio" 
                class="inline-flex items-center gap-1 px-2 py-1 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors save-to-board-btn">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
          </svg>
          Save
        </button>
      </div>
    <% end %>
    
    <!-- Style Badges -->
    <% display_styles = portfolio_item.styles.limit(3) %>
    <% if display_styles.any? %>
      <div class="flex flex-wrap gap-2">
        <% display_styles.each do |style| %>
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
            <%= style.title %>
          </span>
        <% end %>
      </div>
    <% end %>
  </div>
</div>