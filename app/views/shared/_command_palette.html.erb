<!-- Command Palette -->
<div data-controller="command-palette" class="relative z-50">
  <dialog data-command-palette-target="dialog" class="backdrop:bg-transparent fixed inset-0 z-50 overflow-y-auto">
    <div class="fixed inset-0 bg-black/25 transition-opacity dark:bg-black/50"></div>
    
    <div tabindex="0" class="fixed inset-0 w-screen overflow-y-auto p-4 focus:outline-none sm:p-6 md:p-20">
      <div class="mx-auto block max-w-xl transform overflow-hidden rounded-xl bg-white shadow-2xl outline-1 outline-black/5 transition-all dark:bg-black dark:-outline-offset-1 dark:outline-white/10">
        <div>
          <div class="grid grid-cols-1 border-b border-gray-300 dark:border-gray-600">
            <input 
              type="text" 
              autofocus 
              placeholder="Search for actions, pages, or features..." 
              data-command-palette-target="input"
              data-action="input->command-palette#search keydown->command-palette#handleListKeyDown"
              class="col-start-1 row-start-1 h-12 w-full pr-4 pl-11 text-base text-black outline-hidden placeholder:text-gray-400 sm:text-sm dark:bg-black dark:text-white dark:placeholder:text-gray-500" 
            />
            <svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="pointer-events-none col-start-1 row-start-1 ml-4 size-5 self-center text-gray-400 dark:text-gray-400">
              <path d="M9 3.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11ZM2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Z" clip-rule="evenodd" fill-rule="evenodd" />
            </svg>
          </div>

          <div 
            data-command-palette-target="list" 
            class="block max-h-72 scroll-py-2 overflow-y-auto py-2 text-sm text-black dark:text-white"
            hidden
          >
            <!-- Actions will be dynamically inserted here -->
          </div>

          <div 
            data-command-palette-target="noResults" 
            class="block p-4 text-center text-sm text-gray-600 dark:text-gray-400" 
            hidden
          >
            <div class="flex flex-col items-center justify-center py-6">
              <svg class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
              </svg>
              <p class="text-black dark:text-white font-medium">No results found</p>
              <p class="text-gray-600 dark:text-gray-400 text-xs mt-1">Try searching with different keywords</p>
            </div>
          </div>

          <!-- Help text at bottom -->
          <div class="border-t border-gray-300 dark:border-gray-600 px-4 py-2 text-xs text-gray-600 dark:text-gray-400">
            <div class="flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <span>Press <kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">↑↓</kbd> to navigate</span>
                <span><kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">↵</kbd> to select</span>
                <span><kbd class="inline-flex items-center rounded border border-gray-300 dark:border-gray-600 px-1 font-sans text-xs text-gray-500 dark:text-gray-400">esc</kbd> twice to close</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </dialog>
</div>