<el-dialog>
  <dialog id="save-to-board-modal" aria-labelledby="save-to-board-title" 
    class="fixed inset-0 size-auto max-h-none max-w-none overflow-y-auto bg-transparent backdrop:bg-transparent">
    <el-dialog-backdrop
      class="fixed inset-0 bg-black/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"></el-dialog-backdrop>

    <div tabindex="0"
      class="flex min-h-full items-end justify-center p-4 text-center focus:outline-none sm:items-center sm:p-0">
      <el-dialog-panel
        class="relative transform overflow-hidden rounded-lg bg-white dark:bg-black border border-gray-200 dark:border-gray-800 text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg data-closed:sm:translate-y-0 data-closed:sm:scale-95">
        
        <form id="save-to-board-form" method="post" action="">
          <div class="bg-white dark:bg-black px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div
                class="mx-auto flex size-12 shrink-0 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 sm:mx-0 sm:size-10">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
                  aria-hidden="true" class="size-6 text-gray-600 dark:text-gray-400">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 id="save-to-board-title" class="text-base font-semibold text-gray-900 dark:text-white">Save to Inspiration Board</h3>
                <div class="mt-4">
                  <div class="space-y-4">
                    <!-- Board Selection -->
                    <div id="boards-section">
                      <label for="board-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Choose a board
                      </label>
                      <select id="board-select" name="inspiration_board_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400">
                        <option value="">Select a board...</option>
                        <!-- Options will be populated by JavaScript -->
                      </select>
                    </div>

                    <!-- No Boards Message -->
                    <div id="no-boards-section" class="hidden">
                      <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                        You don't have any inspiration boards yet. Create your first board to start saving items!
                      </p>
                    </div>

                    <!-- Actions -->
                    <div class="space-y-2">
                      <div class="flex items-center justify-start">
                        <a href="<%= my_inspiration_boards_path %>" 
                           class="text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 font-medium">
                          Manage Boards
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Hidden fields that will be populated by JavaScript -->
          <input type="hidden" id="modal-post-id" name="post_id" value="">
          <input type="hidden" id="modal-portfolio-item-id" name="portfolio_item_id" value="">
          
          <div class="bg-gray-50 dark:bg-gray-900 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button type="submit" id="save-button" 
                    class="inline-flex w-full justify-center rounded-md bg-gray-900 hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-100 px-3 py-2 text-sm font-semibold text-white dark:text-gray-900 shadow-xs sm:ml-3 sm:w-auto transition-colors">
              Save to Board
            </button>
            <button type="button" data-action="click->save-to-board#close" 
                    class="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-black border border-gray-300 dark:border-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-900 sm:mt-0 sm:w-auto transition-colors">
              Cancel
            </button>
          </div>
        </form>
      </el-dialog-panel>
    </div>
  </dialog>
</el-dialog>