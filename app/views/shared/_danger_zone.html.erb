<%#
  Shared danger zone for delete functionality
  Usage: render 'shared/danger_zone', item: @forum_thread, item_type: 'thread'
         render 'shared/danger_zone', item: @post, item_type: 'post'
%>

<div class="mt-8">
  <div class="rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex items-start">
      <div class="flex-shrink-0">
        <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
          Danger Zone
        </h3>
        <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          <p>Once you delete 
            <% if item_type == 'thread' %>
              a thread, there is no going back. This will permanently delete the thread and all its comments.
            <% else %>
              a post, there is no going back. This will permanently delete the post and all its comments.
            <% end %>
          </p>
        </div>
        <div class="mt-4">
          <button type="button" 
                  command="show-modal" 
                  commandfor="delete-<%= item_type %>-<%= item.id %>"
                  onclick="console.log('Delete button clicked for <%= item_type %> <%= item.id %>');"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-white dark:text-black bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
            Delete <%= item_type.capitalize %>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<%= render 'shared/delete_item_modal', item: item, item_type: item_type %>