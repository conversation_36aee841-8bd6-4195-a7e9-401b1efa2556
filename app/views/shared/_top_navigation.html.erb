<!-- Top Navigation Bar -->
<div class="lg:pl-64 transition-all duration-300 ease-in-out" id="top-nav">
  <div class="sticky top-0 z-40 bg-white dark:bg-black border-b border-gray-300 dark:border-gray-600 flex items-center justify-between h-12 px-4">
    
    <!-- Left Side: Sidebar Toggle (Mobile + Desktop) -->
    <div class="flex items-center">
      <!-- Mobile sidebar toggle -->
      <button type="button" 
              class="lg:hidden -m-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" 
              id="mobile-menu-toggle">
        <span class="sr-only">Open sidebar</span>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
        </svg>
      </button>
      
      <!-- Desktop sidebar toggle -->
      <button type="button" 
              class="hidden lg:flex -m-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors"
              id="desktop-sidebar-toggle"
              title="Toggle Sidebar">
        <span class="sr-only">Toggle sidebar</span>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5" />
        </svg>
      </button>
    </div>

    <!-- Right Side: Command Palette + Theme Toggle + User Avatar -->
    <div class="flex items-center gap-x-4">
      
      <!-- Command Palette Shortcut -->
      <button type="button" 
              onclick="document.querySelector('[data-controller*=\"command-palette\"] dialog').showModal(); document.querySelector('[data-controller*=\"command-palette\"] input').focus()"
              class="flex items-center gap-x-2 rounded-md border border-gray-200 dark:border-gray-700 px-2 py-1 text-xs text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-900 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
              title="Open command palette (⌘K)">
        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
        </svg>
        <span>⌘K</span>
      </button>

      <!-- Theme Toggle -->
      <button type="button" 
              class="-m-2 p-2.5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" 
              data-dark-mode-toggle
              title="Toggle theme">
        <span class="sr-only">Toggle theme</span>
        <!-- Moon icon (shown when light mode is active) -->
        <svg data-dark-icon class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: block;">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />
        </svg>
        <!-- Sun icon (shown when dark mode is active) -->
        <svg data-light-icon class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="display: none;">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" />
        </svg>
      </button>
      
      <!-- User Profile Dropdown (when logged in) -->
      <% if current_user %>
        <div class="relative" data-dropdown>
          <button type="button" class="-m-2 p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors" data-dropdown-toggle>
            <% if current_user.artist? && current_user.artist_profile&.profile_photo&.attached? %>
              <%= image_tag current_user.artist_profile.profile_photo.variant(resize_to_fill: [24, 24]), 
                  class: "h-6 w-6 rounded-full bg-white" %>
            <% elsif current_user.client? && current_user.client_profile&.profile_photo&.attached? %>
              <%= image_tag current_user.client_profile.profile_photo.variant(resize_to_fill: [24, 24]), 
                  class: "h-6 w-6 rounded-full bg-white" %>
            <% else %>
              <div class="h-6 w-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                  <%= current_user.display_name.first.upcase %>
                </span>
              </div>
            <% end %>
          </button>

          <!-- Dropdown menu -->
          <div class="absolute top-full right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white dark:bg-gray-900 py-2 shadow-lg ring-1 ring-gray-900/5 dark:ring-gray-100/10 focus:outline-none border border-gray-200 dark:border-gray-700" 
               data-dropdown-menu style="display: none;">
            <% if current_user.artist? && current_user.artist_profile %>
              <%= link_to "Your profile", artist_path(current_user.artist_profile), 
                  class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800" %>
            <% elsif current_user.client? && current_user.client_profile %>
              <%= link_to "Your profile", client_path(current_user.client_profile), 
                  class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800" %>
            <% end %>
            
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800">Your settings</a>
            
            <%= link_to "Sign out", destroy_session_path, 
                data: { turbo_method: :delete }, 
                class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800" %>
          </div>
        </div>
      <% end %>
      
    </div>
  </div>
</div>