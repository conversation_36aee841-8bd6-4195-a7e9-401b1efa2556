<%# 
  Reusable masonry grid component
  
  Parameters:
  - posts: Collection of posts to display
  - portfolio_items: Collection of portfolio items to display (alternative to posts)
  - container_id: ID for the masonry container (default: 'masonry-container')
  - grid_class: Additional CSS classes for the grid (default: 'masonry-grid')
  - empty_message: Custom message when no items (optional)
  - empty_title: Custom title when no items (optional)
  - show_view_all: Whether to show "View All Posts" button (default: false)
%>

<%
  container_id = local_assigns[:container_id] || 'masonry-container'
  grid_class = local_assigns[:grid_class] || 'masonry-grid'
  empty_title = local_assigns[:empty_title] || 'No items found'
  empty_message = local_assigns[:empty_message] || 'Check back later for new content.'
  show_view_all = local_assigns[:show_view_all] || false
%>

<% 
  items = local_assigns[:posts] || local_assigns[:portfolio_items] || []
%>

<% if items.any? %>
  <div id="<%= container_id %>" class="<%= grid_class %> mb-8" data-masonry data-infinite-scroll-target="container">
    <% items.each do |item| %>
      <% if item.is_a?(PortfolioItem) %>
        <%= render 'portfolio_items/portfolio_item_card', portfolio_item: item %>
      <% else %>
        <%= render 'posts/masonry_post_card', post: item %>
      <% end %>
    <% end %>
  </div>
  
  <% if show_view_all %>
    <!-- View More Button -->
    <div class="text-center">
      <%= link_to "View All Artists", artists_path, 
          class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-900 focus:ring-black dark:bg-white500 dark:focus:ring-black dark:bg-white400" %>
    </div>
  <% end %>
<% else %>
  <div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white"><%= empty_title %></h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= empty_message %></p>
  </div>
<% end %>