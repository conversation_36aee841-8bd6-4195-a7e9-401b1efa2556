<%#
  Delete confirmation modal for comments
  Usage: render 'shared/delete_confirmation_modal', comment: comment
%>

<el-dialog>
  <dialog id="delete-comment-<%= comment.id %>" aria-labelledby="delete-comment-title-<%= comment.id %>"
    class="fixed inset-0 size-auto max-h-none max-w-none overflow-y-auto bg-transparent backdrop:bg-transparent">
    <el-dialog-backdrop
      class="fixed inset-0 bg-black/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"></el-dialog-backdrop>

    <div tabindex="0"
      class="flex min-h-full items-end justify-center p-4 text-center focus:outline-none sm:items-center sm:p-0">
      <el-dialog-panel
        class="relative transform overflow-hidden rounded-lg bg-white dark:bg-black border border-gray-200 dark:border-gray-800 text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg data-closed:sm:translate-y-0 data-closed:sm:scale-95">
        <div class="bg-white dark:bg-black px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div
              class="mx-auto flex size-12 shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 sm:mx-0 sm:size-10">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" data-slot="icon"
                aria-hidden="true" class="size-6 text-red-600 dark:text-red-400">
                <path
                  d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
                  stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 id="delete-comment-title-<%= comment.id %>" class="text-base font-semibold text-gray-900 dark:text-white">Delete Comment</h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">Are you sure you want to delete this comment? This action cannot be undone.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-900 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
          <%= form_with url: (comment.commentable.is_a?(ForumThread) ? forum_thread_comment_path(comment.commentable, comment) : post_comment_path(comment.commentable, comment)), 
                        method: :delete, 
                        local: true, 
                        class: "inline-flex", 
                        data: { "delete-form": comment.id } do |form| %>
            <button type="submit" 
                    data-delete-button="<%= comment.id %>"
                    class="inline-flex w-full justify-center rounded-md bg-red-600 hover:bg-red-500 dark:bg-red-700 dark:hover:bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-xs sm:ml-3 sm:w-auto transition-colors">
              <span data-delete-text="<%= comment.id %>">Delete Comment</span>
              <span data-delete-loading="<%= comment.id %>" class="hidden">
                Deleting<span class="animate-dots">...</span>
              </span>
            </button>
          <% end %>
          <button type="button" command="close" commandfor="delete-comment-<%= comment.id %>"
            class="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-black border border-gray-300 dark:border-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-900 sm:mt-0 sm:w-auto transition-colors">Cancel</button>
        </div>
      </el-dialog-panel>
    </div>
  </dialog>
</el-dialog>