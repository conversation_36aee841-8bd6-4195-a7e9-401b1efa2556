<!DOCTYPE html>
<html class="h-full">
  <head>
    <title><%= content_for(:title) || "Glyph" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    
    
    <%= javascript_importmap_tags %>
    
    <!-- Dark mode initialization -->
    <script>
      // Initialize dark mode based on system preference or stored preference
      if (localStorage.getItem('tattoo-marketplace-theme') === 'dark' || (!localStorage.getItem('tattoo-marketplace-theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    </script>
    <%= Sentry.get_trace_propagation_meta.html_safe %>
  </head>

  <body class="h-full bg-white dark:bg-black flex flex-col" 
        data-controller="navigation save-to-board"
        data-authenticated="<%= current_user.present? %>"
        data-user-type="<%= current_user&.artist? ? 'artist' : (current_user&.client? ? 'client' : '') %>"
        data-user-approved="<%= current_user&.approved_or_admin? %>"
        data-user-profile-slug="<%= current_user&.artist? ? current_user.artist_profile&.slug : current_user&.client_profile&.slug %>">
    <!-- Sidebar Navigation -->
    <%= render 'shared/sidebar_navigation' %>

    <!-- Top Navigation -->
    <%= render 'shared/top_navigation' %>

    <!-- Main Content -->
    <main class="lg:pl-64 flex-1 transition-all duration-300 ease-in-out" id="main-content">
      <div class="px-4 sm:px-6 lg:px-8 py-8">
        <%= yield %>
      </div>
    </main>
    
    <!-- Footer -->
    <footer class="lg:pl-64 bg-white dark:bg-black border-t border-gray-300 dark:border-gray-600 mt-auto transition-all duration-300 ease-in-out" id="footer">
      <div class="px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            &copy; <%= Date.current.year %> Glyph. All rights reserved.
          </p>
          <div class="mt-4 sm:mt-0 flex space-x-6">
            <%= link_to "Terms of Service", terms_path, class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" %>
            <%= link_to "Privacy Policy", privacy_path, class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" %>
          </div>
        </div>
      </div>
    </footer>
    

    <!-- Command Palette -->
    <%= render 'shared/command_palette' %>

    <!-- Save to Board Modal -->
    <% if current_user&.approved_or_admin? %>
      <%= render 'shared/save_to_board_modal' %>
    <% end %>
  </body>
</html>
