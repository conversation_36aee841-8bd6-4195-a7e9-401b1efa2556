<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Admin - Tattoo Marketplace" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-200">
    <!-- Admin Navigation -->
    <nav class="bg-black dark:bg-white600 shadow-lg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <%= link_to admin_root_path, class: "flex items-center text-white font-bold text-xl" do %>
              🎨 Admin Dashboard
            <% end %>
          </div>
          
          <div class="flex items-center space-x-4">
            <%= link_to admin_dashboard_path, class: nav_link_class(admin_dashboard_path) do %>
              Dashboard
            <% end %>
            
            <%= link_to admin_users_path, class: nav_link_class(admin_users_path) do %>
              Users
              <% if (pending = User.where(approved: false).count) > 0 %>
                <span class="ml-1 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                  <%= pending %>
                </span>
              <% end %>
            <% end %>
            
            <%= link_to admin_posts_path, class: nav_link_class(admin_posts_path) do %>
              Posts
            <% end %>
            
            <%= link_to admin_specialties_path, class: nav_link_class(admin_specialties_path) do %>
              Specialties
            <% end %>
            
            <%= link_to admin_settings_path, class: nav_link_class(admin_settings_path) do %>
              Settings
            <% end %>
            
            <div class="border-l border-black dark:bg-white500 pl-4 ml-4">
              <span class="text-black dark:bg-white200 text-sm">
                <%= current_user.display_name %>
              </span>
              <%= link_to root_path, class: "ml-3 text-black dark:bg-white200 hover:text-white text-sm" do %>
                ← Back to Site
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Flash Messages -->
    <% if notice %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 mb-4" role="alert">
        <span class="block sm:inline"><%= notice %></span>
      </div>
    <% end %>

    <% if alert %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4" role="alert">
        <span class="block sm:inline"><%= alert %></span>
      </div>
    <% end %>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <%= yield %>
    </main>
  </body>
</html>

<% content_for :head do %>
  <style>
    .admin-nav-link {
      @apply text-black dark:bg-white200 hover:text-white px-3 py-2 rounded-md text-sm font-medium;
    }
    .admin-nav-link.active {
      @apply bg-black dark:bg-white700 text-white;
    }
  </style>
<% end %>