<div class="min-h-screen bg-white dark:bg-black flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
      Sign in to your account
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
      Welcome back to Tattoo Marketplace
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="border border-gray-300 dark:border-gray-600 py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
      <!-- Flash Messages -->
      <% if alert = flash[:alert] %>
        <div class="mb-4 bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 px-4 py-3 rounded">
          <%= alert %>
        </div>
      <% end %>

      <% if notice = flash[:notice] %>
        <div class="mb-4 bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 px-4 py-3 rounded">
          <%= notice %>
        </div>
      <% end %>

      <%= form_with url: session_path, local: true, class: "form space-y-6" do |form| %>
        <div class="grid gap-3">
          <%= form.label :login, "Email", class: "block text-xs font-medium text-gray-900 dark:text-white" %>
          <%= form.text_field :login, required: true, autofocus: true, autocomplete: "email", placeholder: "Enter your email", value: params[:login], class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" %>
        </div>

        <div class="grid gap-3">
          <%= form.label :password, class: "block text-xs font-medium text-gray-900 dark:text-white" %>
          <%= form.password_field :password, required: true, autocomplete: "current-password", placeholder: "Enter your password", maxlength: 72, class: "w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400" %>
        </div>

        <div class="flex items-center justify-between">
          <div class="text-sm">
            <%= link_to "Forgot your password?", new_password_path, class: "font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" %>
          </div>
        </div>

        <div>
          <%= form.submit "Sign in", class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400" %>
        </div>

        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Don't have an account?
            <%= link_to "Join now", new_registration_path, class: "font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100" %>
          </p>
        </div>
      <% end %>
    </div>
  </div>
</div>
