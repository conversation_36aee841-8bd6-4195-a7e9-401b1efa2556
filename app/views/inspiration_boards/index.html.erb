<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
  <% if @artist_profile || @client_profile %>
    <!-- Profile-based management view -->
    <% profile = @artist_profile || @client_profile %>
    <%= render "#{@artist_profile ? 'artists' : 'clients'}/shared/profile_header" %>
    <%= render "#{@artist_profile ? 'artists' : 'clients'}/shared/tab_navigation" %>

    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <%= link_to @artist_profile ? inspiration_artist_path(@artist_profile) : inspiration_client_path(@client_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Inspiration
          <% end %>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
            </svg>
            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Manage</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Page Header -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Inspiration Management</h1>
        <p class="mt-1 text-gray-600 dark:text-gray-400">Manage your inspiration boards and their order</p>
      </div>
      <% 
        # Determine the correct route based on context
        if @artist_profile
          new_board_path = new_artist_inspiration_board_path(@artist_profile)
        elsif @client_profile
          new_board_path = new_client_inspiration_board_path(@client_profile)
        end
      %>
      <%= link_to "Create Board", new_board_path, 
          class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" %>
    </div>
  <% else %>
    <!-- Global view -->
    <% if current_user&.artist? && current_user.artist_profile %>
      <% @artist_profile = current_user.artist_profile %>
      <%= render 'artists/shared/profile_header' %>
      <%= render 'artists/shared/tab_navigation' %>
      
      <!-- Breadcrumb -->
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <%= link_to inspiration_artist_path(@artist_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
              Inspiration
            <% end %>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
              </svg>
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Manage</span>
            </div>
          </li>
        </ol>
      </nav>
      
      <!-- Page Header -->
      <div class="flex items-center justify-between mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Inspiration Management</h1>
          <p class="mt-1 text-gray-600 dark:text-gray-400">Manage your inspiration boards and their order</p>
        </div>
        <%= link_to "Create Board", new_artist_inspiration_board_path(@artist_profile), 
            class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" %>
      </div>
    <% elsif current_user&.client? && current_user.client_profile %>
      <% @client_profile = current_user.client_profile %>
      <%= render 'clients/shared/profile_header' %>
      <%= render 'clients/shared/tab_navigation' %>
      
      <!-- Breadcrumb -->
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <%= link_to inspiration_client_path(@client_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
              Inspiration
            <% end %>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
              </svg>
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Manage</span>
            </div>
          </li>
        </ol>
      </nav>
      
      <!-- Page Header -->
      <div class="flex items-center justify-between mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Inspiration Management</h1>
          <p class="mt-1 text-gray-600 dark:text-gray-400">Manage your inspiration boards and their order</p>
        </div>
        <%= link_to "Create Board", new_client_inspiration_board_path(@client_profile), 
            class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" %>
      </div>
    <% else %>
      <!-- Fallback for users without profiles -->
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">My Inspiration Boards</h1>
          <p class="mt-2 text-gray-600 dark:text-gray-400">Organize and save posts that inspire you</p>
        </div>
        <%= link_to "Create Board", new_my_inspiration_board_path, 
            class: "px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-black dark:bg-white500" %>
      </div>
    <% end %>
  <% end %>

  <% if @inspiration_boards.any? %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pb-16">
      <% @inspiration_boards.each do |board| %>
        <div class="card-style rounded-lg dark:shadow-gray-900/20 overflow-hidden">
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                <% 
                  # Determine the correct board show path
                  if @artist_profile
                    board_path = artist_inspiration_board_path(@artist_profile, board)
                  elsif @client_profile
                    board_path = client_inspiration_board_path(@client_profile, board)
                  else
                    # For global context, route through user's profile
                    if board.user.artist? && board.user.artist_profile
                      board_path = artist_inspiration_board_path(board.user.artist_profile, board)
                    elsif board.user.client? && board.user.client_profile
                      board_path = client_inspiration_board_path(board.user.client_profile, board)
                    else
                      board_path = "#" # No view available
                    end
                  end
                %>
                <%= link_to board.name, board_path, class: "hover:text-blue-600 dark:hover:text-blue-400" %>
              </h3>
              <div class="flex items-center space-x-2">
                <% if board.private? %>
                  <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                  </svg>
                <% else %>
                  <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                <% end %>
              </div>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              <%= pluralize(board.inspiration_board_items.count, 'item') %>
            </p>
            
            <!-- Preview Images -->
            <% 
              # Get images from both posts and portfolio items
              preview_items = []
              board.inspiration_board_items.includes(:post, :portfolio_item).limit(4).each do |item|
                if item.post&.image&.attached?
                  preview_items << { image: item.post.image, type: 'post' }
                elsif item.portfolio_item&.image&.attached?
                  preview_items << { image: item.portfolio_item.image, type: 'portfolio' }
                end
              end
            %>
            <% if preview_items.any? %>
              <div class="grid grid-cols-2 gap-1 mb-4">
                <% preview_items.each do |item| %>
                  <div class="aspect-square bg-gray-200 rounded overflow-hidden relative">
                    <%= image_tag item[:image], class: "w-full h-full object-cover" %>
                    <% if item[:type] == 'portfolio' %>
                      <div class="absolute top-1 right-1">
                        <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                          P
                        </span>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="h-32 bg-gray-200 rounded-lg flex items-center justify-center mb-4">
                <div class="text-center">
                  <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                  <p class="text-sm text-gray-500 mt-1">Empty board</p>
                </div>
              </div>
            <% end %>
            
            <!-- Bottom Action Bar -->
            <div class="flex items-center justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
              <!-- Actions -->
              <div class="flex items-center space-x-3">
                <%= link_to "View", board_path, class: "text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 font-medium" %>
                <% 
                  # Determine the correct edit path
                  if @artist_profile
                    edit_board_path = edit_artist_inspiration_board_path(@artist_profile, board)
                  elsif @client_profile
                    edit_board_path = edit_client_inspiration_board_path(@client_profile, board)
                  else
                    # For global context, we still need to route through user's profile
                    if board.user.artist? && board.user.artist_profile
                      edit_board_path = edit_artist_inspiration_board_path(board.user.artist_profile, board)
                    elsif board.user.client? && board.user.client_profile
                      edit_board_path = edit_client_inspiration_board_path(board.user.client_profile, board)
                    else
                      edit_board_path = "#" # No edit available
                    end
                  end
                %>
                <%= link_to "Edit", edit_board_path, 
                    class: "text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium" %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No inspiration boards</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating your first inspiration board.</p>
      <div class="mt-6">
        <% 
          if @artist_profile
            new_board_path = new_artist_inspiration_board_path(@artist_profile)
          elsif @client_profile
            new_board_path = new_client_inspiration_board_path(@client_profile)
          else
            new_board_path = new_my_inspiration_board_path
          end
        %>
        <%= link_to "Create Board", new_board_path, 
            class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black dark:bg-white600 dark:bg-black dark:bg-white500 hover:bg-black dark:bg-white700 dark:hover:bg-black dark:bg-white600" %>
      </div>
    </div>
  <% end %>
  
</div>