<!-- Board Header -->
<div class="flex justify-between items-center mb-8">
  <div>
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= @inspiration_board.name %></h1>
    <div class="flex items-center space-x-4 mt-2">
      <span class="text-sm text-gray-600 dark:text-gray-400">
        <%= pluralize(@inspiration_board.inspiration_board_items.count, 'item') %>
      </span>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @inspiration_board.private? ? 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300' : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300' %>">
        <% if @inspiration_board.private? %>
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          Private
        <% else %>
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Public
        <% end %>
      </span>
      <% if @inspiration_board.description.present? %>
        <span class="text-sm text-gray-500 dark:text-gray-400">•</span>
        <span class="text-sm text-gray-600 dark:text-gray-400"><%= @inspiration_board.description %></span>
      <% end %>
    </div>
  </div>
  
  <% if @inspiration_board.user == current_user %>
    <% if @inspiration_board.user.artist_profile %>
      <%= link_to "Edit Board", edit_artist_inspiration_board_path(@inspiration_board.user.artist_profile, @inspiration_board), 
          class: "bg-black dark:bg-white600 hover:bg-black dark:bg-white700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out" %>
    <% elsif @inspiration_board.user.client? && @inspiration_board.user.client_profile %>
      <%= link_to "Edit Board", edit_client_inspiration_board_path(@inspiration_board.user.client_profile, @inspiration_board), 
          class: "bg-black dark:bg-white600 hover:bg-black dark:bg-white700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out" %>
    <% end %>
  <% end %>
</div>