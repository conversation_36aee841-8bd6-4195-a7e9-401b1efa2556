<div class="card-style rounded-lg dark:shadow-gray-900/20 overflow-hidden hover:shadow-lg dark:hover:shadow-gray-900/30 transition-shadow duration-300 flex flex-col h-fit">
  
  <!-- Item Type Badge -->
  <div class="p-3 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between">
      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        Portfolio
      </span>
      <span class="text-xs text-gray-500 dark:text-gray-400">
        Saved <%= time_ago_in_words(item.created_at) %> ago
      </span>
    </div>
  </div>

  <!-- Artist Info -->
  <div class="p-4 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center space-x-3">
      <% source_item = item.source %>
      <% artist_profile = item.source_artist %>
      <%= link_to artist_path(artist_profile), class: "flex items-center space-x-3 hover:text-black dark:bg-white600 dark:hover:text-black dark:bg-white400 transition-colors" do %>
        <div class="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
            <%= artist_profile.user.name&.first&.upcase || "A" %>
          </span>
        </div>
        <div>
          <p class="font-medium text-gray-900 dark:text-white text-sm">@<%= artist_profile.user.name %></p>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            <%= time_ago_in_words(item.portfolio_item.created_at) %> ago
          </p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Item Image -->
  <% if item.image&.attached? %>
    <div class="relative aspect-square overflow-hidden">
      <%= image_tag item.image, 
          class: "w-full h-full object-cover",
          alt: source_item.description.presence || "Saved item",
          loading: "lazy" %>
    </div>
  <% end %>

  <!-- Item Content -->
  <div class="p-4 flex-grow">
    <!-- Description -->
    <% if source_item&.description&.present? %>
      <div class="text-gray-900 dark:text-white text-sm mb-3">
        <%= simple_format(truncate(source_item.description, length: 150)) %>
      </div>
    <% end %>
    
    <!-- Notes Section -->
    <% if item.notes.present? %>
      <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-md p-3 mb-3">
        <div class="flex items-start">
          <svg class="w-4 h-4 text-yellow-400 dark:text-yellow-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
          <div class="flex-1">
            <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-300">Your Notes</h4>
            <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-200">
              <%= simple_format(item.notes) %>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Actions -->
  <%= render 'inspiration_boards/board_item_actions', item: item %>
</div>