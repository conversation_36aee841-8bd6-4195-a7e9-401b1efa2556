<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Profile Header and Navigation -->
  <% if @artist_profile %>
    <%= render 'artists/shared/profile_header' %>
    <%= render 'artists/shared/tab_navigation' %>
  <% elsif @client_profile %>
    <%= render 'clients/shared/profile_header' %>
    <%= render 'clients/shared/tab_navigation' %>
  <% end %>

  <!-- Breadcrumb -->
  <nav class="flex mb-8" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <% if @artist_profile %>
          <%= link_to inspiration_artist_path(@artist_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Inspiration
          <% end %>
        <% elsif @client_profile %>
          <%= link_to inspiration_client_path(@client_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Inspiration
          <% end %>
        <% end %>
      </li>
      <li class="inline-flex items-center">
        <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
        </svg>
        <% if @artist_profile %>
          <%= link_to artist_inspiration_boards_path(@artist_profile), class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Manage
          <% end %>
        <% elsif @client_profile %>
          <%= link_to client_inspiration_boards_path(@client_profile), class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Manage
          <% end %>
        <% end %>
      </li>
      <li class="inline-flex items-center">
        <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
        </svg>
        <% if @artist_profile %>
          <%= link_to artist_inspiration_board_path(@artist_profile, @inspiration_board), class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            <%= @inspiration_board.name %>
          <% end %>
        <% elsif @client_profile %>
          <%= link_to client_inspiration_board_path(@client_profile, @inspiration_board), class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            <%= @inspiration_board.name %>
          <% end %>
        <% end %>
      </li>
      <li aria-current="page">
        <div class="flex items-center">
          <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
          </svg>
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Edit</span>
        </div>
      </li>
    </ol>
  </nav>

  <div class="max-w-2xl">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Inspiration Board</h1>
      <p class="mt-2 text-gray-600 dark:text-gray-400">Update your inspiration board settings</p>
    </div>

  <div class="card-style rounded-lg overflow-hidden">
    <% 
      # Determine the form URL and cancel path based on the current route context
      if @artist_profile
        form_url = artist_inspiration_board_path(@artist_profile, @inspiration_board)
        cancel_path = artist_inspiration_board_path(@artist_profile, @inspiration_board)
      elsif @client_profile
        form_url = client_inspiration_board_path(@client_profile, @inspiration_board)
        cancel_path = client_inspiration_board_path(@client_profile, @inspiration_board)
      else
        # This shouldn't happen with current routing, but fallback just in case
        form_url = @inspiration_board
        cancel_path = @inspiration_board
      end
    %>
    <%= render 'form', 
        form_url: form_url,
        cancel_path: cancel_path,
        delete_path: form_url, # Use same URL for PATCH/DELETE
        show_delete_button: true,
        submit_text: "Update Board",
        submit_disable_text: "Updating..." %>
  </div>
  </div>
</div>