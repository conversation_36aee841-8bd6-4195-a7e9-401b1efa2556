<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Profile Header and Navigation -->
  <% if @artist_profile %>
    <%= render 'artists/shared/profile_header' %>
    <%= render 'artists/shared/tab_navigation' %>
  <% elsif @client_profile %>
    <%= render 'clients/shared/profile_header' %>
    <%= render 'clients/shared/tab_navigation' %>
  <% end %>

  <!-- Breadcrumb -->
  <nav class="flex mb-8" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <% if @artist_profile %>
          <%= link_to inspiration_artist_path(@artist_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Inspiration
          <% end %>
        <% elsif @client_profile %>
          <%= link_to inspiration_client_path(@client_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Inspiration
          <% end %>
        <% end %>
      </li>
      <li class="inline-flex items-center">
        <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
        </svg>
        <% if @artist_profile %>
          <%= link_to artist_inspiration_boards_path(@artist_profile), class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Manage
          <% end %>
        <% elsif @client_profile %>
          <%= link_to client_inspiration_boards_path(@client_profile), class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
            Manage
          <% end %>
        <% end %>
      </li>
      <li aria-current="page">
        <div class="flex items-center">
          <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
          </svg>
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400">New Board</span>
        </div>
      </li>
    </ol>
  </nav>

  <div class="max-w-2xl">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create Inspiration Board</h1>
      <p class="mt-2 text-gray-600 dark:text-gray-400">Organize your saved posts into collections</p>
    </div>

  <div class="card-style rounded-lg overflow-hidden">
    <% 
      # Determine the form URL and cancel path based on the current route context
      if @artist_profile
        form_url = artist_inspiration_boards_path(@artist_profile)
        cancel_path = inspiration_artist_path(@artist_profile)
      elsif @client_profile
        form_url = client_inspiration_boards_path(@client_profile)
        cancel_path = inspiration_client_path(@client_profile)
      else
        # Fallback to the global route
        form_url = create_my_inspiration_board_path
        cancel_path = my_inspiration_boards_path
      end
    %>
    <%= render 'form', 
        form_url: form_url,
        cancel_path: cancel_path,
        submit_text: "Create Board",
        submit_disable_text: "Creating..." %>
  </div>
  </div>
</div>