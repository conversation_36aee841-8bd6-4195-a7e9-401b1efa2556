<div class="p-4">
  <div class="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
    <% if @inspiration_board.user == current_user %>
      <!-- Dr<PERSON> <PERSON><PERSON> (Left) -->
      <div class="flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-move" data-sortable-handle>
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
        </svg>
        <span class="text-xs font-medium">Drag</span>
      </div>
      
      <!-- Actions (Right) -->
      <div class="flex items-center space-x-2">
        <button type="button" 
                onclick="toggleNotesForm(<%= item.id %>)"
                class="text-xs text-gray-600 dark:text-gray-400 hover:text-black dark:bg-white600 dark:hover:text-black dark:bg-white400 font-medium">
          <%= item.notes.present? ? "Edit" : "Note" %>
        </button>
        
        <% 
          if @inspiration_board.user.artist_profile
            delete_path = artist_inspiration_board_inspiration_board_item_path(@inspiration_board.user.artist_profile, @inspiration_board, item)
          elsif @inspiration_board.user.client_profile
            delete_path = client_inspiration_board_inspiration_board_item_path(@inspiration_board.user.client_profile, @inspiration_board, item)
          end
        %>
        <%= link_to "Remove", delete_path, 
            method: :delete,
            class: "text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300",
            data: { 
              turbo_method: :delete,
              turbo_confirm: "Remove this item from the board?" 
            } %>
      </div>
    <% else %>
      <!-- View-only actions for non-owners -->
      <div class="flex items-center space-x-3">
        <% if item.post %>
          <%= link_to "View Post", item.post, class: "text-xs text-black dark:bg-white600 dark:text-black dark:bg-white400 hover:text-black dark:bg-white700 dark:hover:text-black dark:bg-white300 font-medium" %>
        <% elsif item.portfolio_item %>
          <%= link_to "View Artist", artist_path(item.portfolio_item.artist_profile), class: "text-xs text-black dark:bg-white600 dark:text-black dark:bg-white400 hover:text-black dark:bg-white700 dark:hover:text-black dark:bg-white300 font-medium" %>
        <% end %>
        <%= link_to "Artist", artist_path(item.source_artist), class: "text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" %>
      </div>
    <% end %>
  </div>
  
  <!-- Notes Form (Hidden by default) -->
  <% if @inspiration_board.user == current_user %>
    <div id="notes-form-<%= item.id %>" class="hidden mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
      <% 
        if @inspiration_board.user.artist_profile
          update_path = artist_inspiration_board_inspiration_board_item_path(@inspiration_board.user.artist_profile, @inspiration_board, item)
        elsif @inspiration_board.user.client_profile
          update_path = client_inspiration_board_inspiration_board_item_path(@inspiration_board.user.client_profile, @inspiration_board, item)
        end
      %>
      <%= form_with model: item, url: update_path, method: :patch, local: true, class: "space-y-3" do |f| %>
        <%= f.text_area :notes, 
            value: item.notes,
            placeholder: "Add your notes about this item...",
            rows: 2,
            class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-black dark:bg-white500 focus:border-black dark:bg-white500 text-sm" %>
        <div class="flex items-center justify-end space-x-3">
          <button type="button" 
                  onclick="toggleNotesForm(<%= item.id %>)"
                  class="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
            Cancel
          </button>
          <%= f.submit "Save", 
              class: "bg-black dark:bg-white600 hover:bg-black dark:bg-white700 text-white text-xs font-medium py-1 px-3 rounded-md transition duration-150 ease-in-out" %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>