<!-- Client Profile Layout -->
<div class="max-w-6xl mx-auto px-4 py-8">
  <!-- Set client profile if available for shared partials -->
  <% if @inspiration_board.user.client_profile %>
    <% @client_profile = @inspiration_board.user.client_profile %>
    <%= render 'clients/shared/profile_header' %>
    <%= render 'clients/shared/tab_navigation' %>
    
    <!-- Breadcrumb (only for board owner) -->
    <% if @inspiration_board.user == current_user %>
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <%= link_to inspiration_client_path(@client_profile), class: "inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
              Inspiration
            <% end %>
          </li>
          <li class="inline-flex items-center">
            <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
            </svg>
            <%= link_to client_inspiration_boards_path(@client_profile), class: "text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300" do %>
              Manage
            <% end %>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
              </svg>
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400"><%= @inspiration_board.name %></span>
            </div>
          </li>
        </ol>
      </nav>
    <% end %>
  <% end %>
  
  <%= render 'inspiration_boards/board_header' %>
</div>