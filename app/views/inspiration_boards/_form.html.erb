<%= form_with model: @inspiration_board, url: local_assigns[:form_url], local: true, class: "space-y-6 p-6" do |f| %>
  <% if @inspiration_board.errors.any? %>
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(@inspiration_board.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul class="list-disc pl-5 space-y-1">
              <% @inspiration_board.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Board Name -->
  <div>
    <%= f.label :name, "Board Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
    <%= f.text_field :name, 
        placeholder: "e.g., Geometric Designs, Leg Tattoos, Color Ideas",
        class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-md focus:outline-none focus:ring-gray-900 dark:focus:ring-white focus:border-gray-500 dark:focus:border-gray-400" %>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Choose a descriptive name for your inspiration board
    </p>
  </div>

  <!-- Privacy Setting -->
  <div>
    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Privacy</label>
    <div class="space-y-3">
      <div class="flex items-start">
        <%= f.radio_button :privacy, true, class: "mt-1 h-4 w-4 text-gray-900 dark:text-white focus:ring-gray-900 dark:focus:ring-white border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700" %>
        <div class="ml-3">
          <%= f.label :privacy_true, "Private", class: "text-sm font-medium text-gray-900 dark:text-white" %>
          <p class="text-sm text-gray-500 dark:text-gray-400">Only you can see this board</p>
        </div>
      </div>
      <div class="flex items-start">
        <%= f.radio_button :privacy, false, class: "mt-1 h-4 w-4 text-gray-900 dark:text-white focus:ring-gray-900 dark:focus:ring-white border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700" %>
        <div class="ml-3">
          <%= f.label :privacy_false, "Public", class: "text-sm font-medium text-gray-900 dark:text-white" %>
          <p class="text-sm text-gray-500 dark:text-gray-400">Other users can discover and view this board</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
    <div class="flex items-center space-x-4">
      <%= link_to "Cancel", local_assigns[:cancel_path] || my_inspiration_boards_path, class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" %>
      <% if local_assigns[:show_delete_button] %>
        <%= link_to "Delete Board", local_assigns[:delete_path] || "#", 
            method: :delete,
            class: "text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 font-medium",
            data: { 
              turbo_method: :delete,
              turbo_confirm: "Are you sure you want to delete this board? This action cannot be undone." 
            } %>
      <% end %>
    </div>
    <%= f.submit local_assigns[:submit_text] || "Create Board", 
        class: "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-md transition duration-150 ease-in-out disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 dark:focus:ring-offset-gray-800",
        data: { disable_with: local_assigns[:submit_disable_text] || "Creating..." } %>
  </div>
<% end %>