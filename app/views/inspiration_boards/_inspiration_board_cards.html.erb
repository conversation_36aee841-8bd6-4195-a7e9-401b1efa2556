<% inspiration_boards.each do |board| %>
  <div class="group bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-xl overflow-hidden shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-300">
    <% 
      # Determine the correct board show path based on context and board owner
      if defined?(@client_profile) && @client_profile
        board_path = client_inspiration_board_path(@client_profile, board)
      elsif defined?(@artist_profile) && @artist_profile
        board_path = artist_inspiration_board_path(@artist_profile, board)
      elsif board.user.artist? && board.user.artist_profile
        board_path = artist_inspiration_board_path(board.user.artist_profile, board)
      elsif board.user.client? && board.user.client_profile
        board_path = client_inspiration_board_path(board.user.client_profile, board)
      else
        board_path = "#" # No view available
      end
    %>
    <%= link_to board_path, class: "block" do %>
      <article class="">
        <!-- Header with User Avatar and <PERSON>rna<PERSON> -->
        <div class="p-4">
          <div class="flex items-center space-x-3">
            <!-- User Avatar -->
            <div class="flex-shrink-0">
              <% if board.user.profile&.profile_photo&.attached? %>
                <%= image_tag board.user.profile.profile_photo.variant(resize_to_fill: [24, 24]), 
                    class: "w-6 h-6 rounded-full object-cover" %>
              <% else %>
                <div class="flex items-center justify-center w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full">
                  <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                    <%= board.user.name.first.upcase %>
                  </span>
                </div>
              <% end %>
            </div>
            
            <!-- User Info -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                <%= board.user.name %>
              </p>
            </div>
            
            <!-- Private indicator -->
            <% if board.private? %>
              <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
              </svg>
            <% end %>
          </div>
        </div>

        <!-- Board Title -->
        <div class="px-4 pb-4">
          <h3 class="text-xl font-bold text-gray-900 dark:text-white">
            <%= board.name %>
          </h3>
        </div>

        <!-- Board preview thumbnails -->
        <% 
          # Get images from portfolio items
          preview_items = []
          board.inspiration_board_items.includes(:portfolio_item).limit(4).each do |item|
            if item.portfolio_item&.image&.attached?
              preview_items << { image: item.portfolio_item.image, type: 'portfolio' }
            end
          end
        %>
        <% if preview_items.any? %>
          <div class="grid grid-cols-2 gap-1">
            <% preview_items.each do |item| %>
              <div class="aspect-square bg-gray-200 dark:bg-gray-700 overflow-hidden relative">
                <%= image_tag item[:image], class: "w-full h-full object-cover", loading: "lazy" %>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="aspect-[4/3] bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <svg class="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
          </div>
        <% end %>
        
        <!-- Badge Section -->
        <div class="p-4">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
            <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
            </svg>
            <%= board.inspiration_board_items.count %>
          </span>
        </div>
      </article>
    <% end %>
  </div>
<% end %>