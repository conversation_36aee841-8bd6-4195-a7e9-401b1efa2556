<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
  <!-- Search Header -->
  <div class="mb-8">
    <div class="text-center">
      <h1 class="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
        Search Results
      </h1>
      <% if @query.present? %>
        <p class="mt-4 text-lg leading-6 text-gray-600 dark:text-gray-400">
          Results for "<span class="font-medium text-black dark:bg-white600 dark:text-black dark:bg-white400"><%= @query %></span>"
        </p>
      <% else %>
        <p class="mt-4 text-lg leading-6 text-gray-600 dark:text-gray-400">
          Enter a search term to find artists, portfolio items, and inspiration boards
        </p>
      <% end %>
    </div>

    <!-- Search Form -->
    <div class="mt-8 max-w-4xl mx-auto">
      <%= form_with url: search_path, method: :get, local: true, class: "relative" do |f| %>
        <div class="relative">
          <%= f.text_field :q, 
              value: @query,
              placeholder: "Search artists by name or bio...",
              class: "w-full px-4 py-3 pl-12 pr-20 text-gray-900 dark:text-white bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-black dark:bg-white600 focus:border-black dark:bg-white600 dark:focus:ring-black dark:bg-white500 dark:focus:border-black dark:bg-white500 placeholder-gray-500 dark:placeholder-gray-400" %>
          
          <!-- Search Icon -->
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          
          <!-- Search Button -->
          <div class="absolute inset-y-0 right-0 flex items-center">
            <%= f.submit "Search", class: "mr-2 px-4 py-2 bg-black dark:bg-white text-white dark:text-black text-sm font-medium rounded-md hover:bg-gray-800 dark:hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <% if @query.present? %>
    <!-- Results Summary -->
    <% total_results = @results.values.sum { |category| category[:count] } %>
    <div class="mb-6">
      <p class="text-sm text-gray-600 dark:text-gray-400">
        Found <%= total_results %> result<%= total_results == 1 ? '' : 's' %>
        <% if total_results > 0 %>
          (<%= @results[:artists][:count] %> artist<%= @results[:artists][:count] == 1 ? '' : 's' %>, 
           <%= @results[:portfolio_items][:count] %> portfolio item<%= @results[:portfolio_items][:count] == 1 ? '' : 's' %>, 
           <%= @results[:inspiration_boards][:count] %> board<%= @results[:inspiration_boards][:count] == 1 ? '' : 's' %>)
        <% end %>
      </p>
    </div>

    <% if total_results > 0 %>
      <div class="space-y-12">
        <!-- Artists Section -->
        <% if @results[:artists][:count] > 0 %>
          <section>
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                Artists (<%= @results[:artists][:count] %>)
              </h2>
              <% if @results[:artists][:count] == 10 %>
                <%= link_to "View all artist results", artists_path(search: @query), 
                    class: "text-sm text-black dark:bg-white600 dark:text-black dark:bg-white400 hover:text-black dark:bg-white500 dark:hover:text-black dark:bg-white300" %>
              <% end %>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6 gap-6">
              <%= render 'artists/artist_cards', artist_profiles: @results[:artists][:items] %>
            </div>
          </section>
        <% end %>

        <!-- Portfolio Items Section -->
        <% if @results[:portfolio_items][:count] > 0 %>
          <section>
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                Portfolio Items (<%= @results[:portfolio_items][:count] %>)
              </h2>
            </div>
            
            <div class="masonry-grid mb-8" data-masonry>
              <% @results[:portfolio_items][:items].each do |item| %>
                <%= render 'portfolio_items/portfolio_item_card', portfolio_item: item %>
              <% end %>
            </div>
          </section>
        <% end %>


        <!-- Inspiration Boards Section -->
        <% if @results[:inspiration_boards][:count] > 0 %>
          <section>
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                Inspiration Boards (<%= @results[:inspiration_boards][:count] %>)
              </h2>
            </div>
            
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <%= render 'inspiration_boards/inspiration_board_cards', inspiration_boards: @results[:inspiration_boards][:items] %>
            </div>
          </section>
        <% end %>
      </div>
    <% else %>
      <!-- No Results -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No results found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Try searching with different keywords or check your spelling.
        </p>
      </div>
    <% end %>
  <% end %>
</div>