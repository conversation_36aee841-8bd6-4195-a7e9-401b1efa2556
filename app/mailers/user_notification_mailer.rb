class UserNotificationMailer < ApplicationMailer
  default from: 'Tattoo Marketplace <<EMAIL>>'

  def welcome(user)
    @user = user
    @login_url = new_session_url
    
    mail(
      to: @user.email_address,
      subject: "Welcome to Tattoo Marketplace!"
    )
  end

  def approval_granted(user)
    @user = user
    @login_url = new_session_url
    @profile_url = if @user.artist?
                     artist_url(@user.artist_profile) if @user.artist_profile
                   else
                     root_url
                   end
    
    mail(
      to: @user.email_address,
      subject: "Your account has been approved!"
    )
  end

end
