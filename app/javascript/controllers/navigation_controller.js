import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.setupUserDropdown()
    this.setupMobileSidebarToggle()
    this.setupDesktopSidebarToggle()
    this.sidebarHidden = false
  }

  setupUserDropdown() {
    const dropdown = document.querySelector('[data-dropdown]')
    if (!dropdown) return
    
    const toggle = dropdown.querySelector('[data-dropdown-toggle]')
    const menu = dropdown.querySelector('[data-dropdown-menu]')
    
    if (toggle && menu) {
      toggle.addEventListener('click', (e) => {
        e.preventDefault()
        const isVisible = menu.style.display !== 'none'
        menu.style.display = isVisible ? 'none' : 'block'
      })
      
      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!dropdown.contains(e.target)) {
          menu.style.display = 'none'
        }
      })
    }
  }

  setupMobileSidebarToggle() {
    const mobileToggle = document.getElementById('mobile-menu-toggle')
    const mobileMenu = document.getElementById('mobile-menu')
    const mobileCloseBtn = document.getElementById('mobile-close-btn')
    const mobileBackdrop = document.getElementById('mobile-backdrop')
    
    if (mobileToggle && mobileMenu) {
      mobileToggle.addEventListener('click', () => {
        this.showMobileSidebar()
      })
    }
    
    if (mobileCloseBtn) {
      mobileCloseBtn.addEventListener('click', () => {
        this.hideMobileSidebar()
      })
    }
    
    if (mobileBackdrop) {
      mobileBackdrop.addEventListener('click', () => {
        this.hideMobileSidebar()
      })
    }
    
    // Close mobile sidebar on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideMobileSidebar()
      }
    })
  }

  setupDesktopSidebarToggle() {
    const desktopToggle = document.getElementById('desktop-sidebar-toggle')
    
    if (desktopToggle) {
      desktopToggle.addEventListener('click', () => {
        this.toggleDesktopSidebar()
      })
    }
  }

  showMobileSidebar() {
    const mobileMenu = document.getElementById('mobile-menu')
    if (mobileMenu) {
      mobileMenu.classList.remove('-translate-x-full')
      mobileMenu.classList.add('translate-x-0')
      document.body.style.overflow = 'hidden'
    }
  }

  hideMobileSidebar() {
    const mobileMenu = document.getElementById('mobile-menu')
    if (mobileMenu) {
      mobileMenu.classList.remove('translate-x-0')
      mobileMenu.classList.add('-translate-x-full')
      document.body.style.overflow = ''
    }
  }

  toggleDesktopSidebar() {
    const sidebar = document.getElementById('sidebar')
    const mainContent = document.getElementById('main-content')
    const topNav = document.getElementById('top-nav')
    const footer = document.getElementById('footer')
    
    if (sidebar && mainContent && topNav && footer) {
      this.sidebarHidden = !this.sidebarHidden
      
      if (this.sidebarHidden) {
        // Hide sidebar
        sidebar.classList.add('-translate-x-full')
        mainContent.classList.remove('lg:pl-72')
        topNav.classList.remove('lg:pl-72')
        footer.classList.remove('lg:pl-72')
      } else {
        // Show sidebar
        sidebar.classList.remove('-translate-x-full')
        mainContent.classList.add('lg:pl-72')
        topNav.classList.add('lg:pl-72')
        footer.classList.add('lg:pl-72')
      }
    }
  }
}