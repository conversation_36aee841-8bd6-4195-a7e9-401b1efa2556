import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  currentPostId = null
  currentPortfolioItemId = null

  connect() {
    // Bind form submission if not already bound
    const form = this.getModal()?.querySelector('#save-to-board-form')
    if (form && !form.hasAttribute('data-stimulus-bound')) {
      form.addEventListener('submit', this.handleSubmit.bind(this))
      form.setAttribute('data-stimulus-bound', 'true')
    }
    
    // Enable/disable save button based on board selection
    const boardSelect = this.getModal()?.querySelector('#board-select')
    if (boardSelect && !boardSelect.hasAttribute('data-stimulus-bound')) {
      boardSelect.addEventListener('change', this.toggleSaveButton.bind(this))
      boardSelect.setAttribute('data-stimulus-bound', 'true')
    }
  }

  // Helper method to get modal element
  getModal() {
    return document.getElementById('save-to-board-modal')
  }

  // Show modal for posts
  showForPost(event) {
    const postId = event.currentTarget.dataset.postId
    this.currentPostId = parseInt(postId)
    this.currentPortfolioItemId = null
    this.loadBoards()
    const modal = this.getModal()
    if (modal) modal.showModal()
  }

  // Show modal for portfolio items  
  showForPortfolio(event) {
    const portfolioItemId = event.currentTarget.dataset.portfolioItemId
    this.currentPortfolioItemId = parseInt(portfolioItemId)
    this.currentPostId = null
    this.loadBoards()
    const modal = this.getModal()
    if (modal) modal.showModal()
  }

  // Load user's boards via Ajax
  async loadBoards() {
    try {
      const response = await fetch('/my/inspiration_boards.json', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const boards = await response.json()
      this.populateBoards(boards)
      
    } catch (error) {
      console.error('Error loading boards:', error)
      this.showError('Failed to load inspiration boards')
    }
  }

  // Populate board select dropdown
  populateBoards(boards) {
    const modal = this.getModal()
    if (!modal) return

    const boardSelect = modal.querySelector('#board-select')
    const boardsSection = modal.querySelector('#boards-section')
    const noBoardsSection = modal.querySelector('#no-boards-section')
    const saveButton = modal.querySelector('#save-button')

    if (!boardSelect) return

    // Clear existing options
    boardSelect.innerHTML = '<option value="">Select a board...</option>'
    
    if (boards.length > 0) {
      // Get current item's boards to check for duplicates
      this.loadCurrentBoardsForItem().then(currentBoards => {
        boards.forEach(board => {
          const option = document.createElement('option')
          option.value = board.id
          
          // Check if item is already in this board
          const alreadySaved = currentBoards.some(cb => cb.id === board.id)
          if (alreadySaved) {
            option.textContent = `${board.name} (Already saved)`
            option.disabled = true
          } else {
            option.textContent = `${board.name} (${board.items_count || 0} items)`
          }
          
          boardSelect.appendChild(option)
        })
        
        if (boardsSection) boardsSection.classList.remove('hidden')
        if (noBoardsSection) noBoardsSection.classList.add('hidden')
        if (saveButton) saveButton.disabled = true
      })
    } else {
      if (boardsSection) boardsSection.classList.add('hidden')
      if (noBoardsSection) noBoardsSection.classList.remove('hidden')
      if (saveButton) saveButton.disabled = true
    }
  }

  // Load boards that already contain the current item
  async loadCurrentBoardsForItem() {
    try {
      const itemType = this.currentPostId ? 'post' : 'portfolio'
      const itemId = this.currentPostId || this.currentPortfolioItemId
      
      const response = await fetch(`/api/inspiration_boards/check_item?type=${itemType}&id=${itemId}`, {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
      })
      
      if (response.ok) {
        return await response.json()
      }
      return []
    } catch (error) {
      console.error('Error checking current boards:', error)
      return []
    }
  }

  // Handle form submission
  async handleSubmit(event) {
    event.preventDefault()
    
    const modal = this.getModal()
    if (!modal) return

    const boardSelect = modal.querySelector('#board-select')
    const saveButton = modal.querySelector('#save-button')
    
    if (!boardSelect || !saveButton) return

    const boardId = boardSelect.value
    if (!boardId) {
      this.showError('Please select a board')
      return
    }

    // Update button to show loading state
    const originalText = saveButton.textContent
    saveButton.textContent = 'Saving...'
    saveButton.disabled = true

    try {
      const formData = new FormData()
      formData.append('inspiration_board_id', boardId)
      
      if (this.currentPostId) {
        formData.append('post_id', this.currentPostId)
      } else {
        formData.append('portfolio_item_id', this.currentPortfolioItemId)
      }

      const response = await fetch('/inspiration_board_items', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      if (response.ok) {
        // Close modal
        modal.close()
        
        // Show success message
        this.showSuccess('Item saved to board successfully!')
        
        // Reset form
        const form = modal.querySelector('#save-to-board-form')
        if (form) form.reset()
        saveButton.textContent = originalText
        saveButton.disabled = true
        
        // Update any save buttons in the UI to show saved state
        this.updateSaveButtonStates()
        
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || 'Failed to save item to board'
        this.showError(errorMessage)
        
        // Reset button
        saveButton.textContent = originalText
        saveButton.disabled = false
      }
    } catch (error) {
      console.error('Error saving to board:', error)
      this.showError('An error occurred while saving')
      
      // Reset button
      saveButton.textContent = originalText
      saveButton.disabled = false
    }
  }

  // Toggle save button based on selection
  toggleSaveButton() {
    const modal = this.getModal()
    if (!modal) return

    const boardSelect = modal.querySelector('#board-select')
    const saveButton = modal.querySelector('#save-button')
    
    if (!boardSelect || !saveButton) return

    const selectedOption = boardSelect.selectedOptions[0]
    saveButton.disabled = !boardSelect.value || selectedOption?.disabled
  }

  // Update save button states in the UI after successful save
  updateSaveButtonStates() {
    const itemId = this.currentPostId || this.currentPortfolioItemId
    const itemType = this.currentPostId ? 'post' : 'portfolio'
    
    // Find and update save buttons for this item
    const saveButtons = document.querySelectorAll(`[data-${itemType}-id="${itemId}"]`)
    saveButtons.forEach(button => {
      if (button.classList.contains('save-to-board-btn')) {
        button.classList.add('saved')
        // You could also update the button text or icon here
      }
    })
  }

  // Close modal
  close() {
    const modal = this.getModal()
    if (modal) modal.close()
  }

  // Show success toast
  showSuccess(message) {
    this.showToast(message, 'success')
  }

  // Show error toast
  showError(message) {
    this.showToast(message, 'error')
  }

  // Generic toast notification
  showToast(message, type = 'info') {
    const toast = document.createElement('div')
    toast.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 transition-opacity ${
      type === 'success' ? 'bg-green-600' : 
      type === 'error' ? 'bg-red-600' : 
      'bg-blue-600'
    }`
    toast.textContent = message
    
    document.body.appendChild(toast)
    
    // Auto remove after 3 seconds
    setTimeout(() => {
      toast.style.opacity = '0'
      setTimeout(() => toast.remove(), 300)
    }, 3000)
  }
}