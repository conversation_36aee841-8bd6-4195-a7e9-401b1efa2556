import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { animation: String }

  connect() {
    // If no animation is set on the element, get the global animation
    if (!this.hasAnimationValue) {
      // Get or set the global animation for this page load
      let globalAnimation = window.portfolioHoverAnimation
      
      if (!globalAnimation) {
        const animations = ['hover-glow', 'hover-pulse', 'hover-lift']
        globalAnimation = animations[Math.floor(Math.random() * animations.length)]
        window.portfolioHoverAnimation = globalAnimation
      }
      
      this.animationValue = globalAnimation
    }
    
    this.element.classList.add(this.animationValue)
  }
}