import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["dialog", "input", "list", "noResults"]

  connect() {
    this.setupKeyboardListener()
    this.actions = this.buildActions()
    this.filteredActions = this.actions
  }

  disconnect() {
    this.removeKeyboardListener()
  }

  setupKeyboardListener() {
    this.handleKeyDown = this.handleKeyDown.bind(this)
    document.addEventListener("keydown", this.handleKeyDown)
  }

  removeKeyboardListener() {
    document.removeEventListener("keydown", this.handleKeyDown)
  }

  handleKeyDown(event) {
    // Cmd+K on Mac or Ctrl+K on Windows/Linux
    if ((event.metaKey || event.ctrlKey) && event.key === "k") {
      event.preventDefault()
      this.show()
    }
    // Escape to close
    if (event.key === "Escape" && this.isVisible()) {
      event.preventDefault()
      this.hide()
    }
  }

  show() {
    this.dialogTarget.showModal()
    // Small delay to ensure dialog is visible before focusing
    setTimeout(() => {
      this.inputTarget.focus()
      this.inputTarget.value = ""
      this.updateList("")
    }, 10)
  }

  hide() {
    this.dialogTarget.close()
  }

  isVisible() {
    return this.dialogTarget.open
  }

  search(event) {
    const query = event.target.value.toLowerCase().trim()
    this.updateList(query)
  }

  updateList(query) {
    this.filteredActions = this.actions.filter(action => {
      return action.title.toLowerCase().includes(query) ||
             action.description.toLowerCase().includes(query) ||
             (action.keywords && action.keywords.some(keyword => 
               keyword.toLowerCase().includes(query)
             ))
    })

    this.renderList()
  }

  renderList() {
    if (this.filteredActions.length === 0) {
      this.listTarget.hidden = true
      this.noResultsTarget.hidden = false
      return
    }

    this.noResultsTarget.hidden = true
    this.listTarget.hidden = false

    // Clear existing items
    this.listTarget.innerHTML = ""

    this.filteredActions.forEach((action, index) => {
      const element = this.createActionElement(action, index === 0)
      this.listTarget.appendChild(element)
    })
  }

  createActionElement(action, isFirst = false) {
    const element = document.createElement("a")
    element.href = action.href || "#"
    element.className = `group block cursor-default px-4 py-2 select-none focus:outline-hidden hover:bg-gray-50 dark:hover:bg-gray-800 aria-selected:bg-black aria-selected:text-white dark:aria-selected:bg-white dark:aria-selected:text-black aria-selected:hover:bg-gray-800 dark:aria-selected:hover:bg-gray-200`
    
    if (isFirst) {
      element.setAttribute("aria-selected", "true")
    }

    element.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0 group-aria-selected:[&_svg]:text-gray-300 dark:group-aria-selected:[&_svg]:text-gray-700">
          ${action.icon}
        </div>
        <div class="ml-3 flex-1 min-w-0">
          <div class="text-sm font-medium text-gray-900 dark:text-white group-aria-selected:text-white dark:group-aria-selected:text-black truncate">
            ${action.title}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400 group-aria-selected:text-gray-300 dark:group-aria-selected:text-gray-700 truncate">
            ${action.description}
          </div>
        </div>
        ${action.badge ? `<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 group-aria-selected:bg-gray-700 group-aria-selected:text-white dark:group-aria-selected:bg-gray-300 dark:group-aria-selected:text-black">${action.badge}</span>` : ''}
      </div>
    `

    element.addEventListener("click", (e) => {
      e.preventDefault()
      this.executeAction(action)
    })

    return element
  }

  executeAction(action) {
    this.hide()
    
    if (action.href && action.href !== "#") {
      // Navigate to the URL
      window.location.href = action.href
    } else if (action.onClick) {
      // Execute custom function
      action.onClick()
    }
  }

  // Handle keyboard navigation
  handleListKeyDown(event) {
    const items = this.listTarget.querySelectorAll("a")
    const currentIndex = Array.from(items).findIndex(item => 
      item.getAttribute("aria-selected") === "true"
    )

    switch (event.key) {
      case "ArrowDown":
        event.preventDefault()
        this.selectNextItem(items, currentIndex)
        break
      case "ArrowUp":
        event.preventDefault()
        this.selectPreviousItem(items, currentIndex)
        break
      case "Enter":
        event.preventDefault()
        if (currentIndex >= 0) {
          items[currentIndex].click()
        }
        break
    }
  }

  selectNextItem(items, currentIndex) {
    if (items.length === 0) return
    
    const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0
    this.updateSelection(items, nextIndex)
  }

  selectPreviousItem(items, currentIndex) {
    if (items.length === 0) return
    
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1
    this.updateSelection(items, prevIndex)
  }

  updateSelection(items, newIndex) {
    items.forEach((item, index) => {
      if (index === newIndex) {
        item.setAttribute("aria-selected", "true")
        item.scrollIntoView({ block: "nearest" })
      } else {
        item.removeAttribute("aria-selected")
      }
    })
  }

  buildActions() {
    const isAuthenticated = document.body.dataset.authenticated === "true"
    const userType = document.body.dataset.userType
    const actions = []

    // Core navigation actions (available to all)
    actions.push(
      {
        title: "Home",
        description: "Go to homepage",
        href: "/",
        icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" /></svg>`,
        keywords: ["home", "main", "dashboard"]
      },
      {
        title: "View All Artists",
        description: "Browse tattoo artists",
        href: "/artists",
        icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" /></svg>`,
        keywords: ["artists", "browse", "profiles", "tattoo artists"]
      },
      {
        title: "Browse Pieces",
        description: "Explore portfolio pieces by style and location",
        href: "/pieces",
        icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" /></svg>`,
        keywords: ["pieces", "portfolio", "tattoos", "artwork", "gallery", "browse pieces"]
      },
      {
        title: "Search",
        description: "Search artists, portfolios, and more",
        href: "/search",
        icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" /></svg>`,
        keywords: ["search", "find", "look"]
      }
    )

    // Add inspiration for approved users
    if (isAuthenticated && document.body.dataset.userApproved === "true") {
      actions.push({
        title: "Inspiration",
        description: "Browse inspiration boards",
        href: "/inspiration/browse",
        icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" /></svg>`,
        keywords: ["inspiration", "boards", "ideas", "mood board"]
      })
    }

    // Unauthenticated user actions
    if (!isAuthenticated) {
      actions.push(
        {
          title: "Sign In",
          description: "Sign in to your account",
          href: "/session/new",
          icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15m3 0l3-3m0 0l-3-3m3 3H9" /></svg>`,
          keywords: ["sign in", "login", "authenticate"]
        },
        {
          title: "Join",
          description: "Create a new account",
          href: "/join",
          icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM3 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 019.374 21c-2.331 0-4.512-.645-6.374-1.766z" /></svg>`,
          keywords: ["join", "register", "sign up", "create account"]
        }
      )
    }

    // Authenticated user actions
    if (isAuthenticated) {
      // Profile link
      const profileSlug = document.body.dataset.userProfileSlug
      if (profileSlug) {
        const profilePath = userType === "artist" ? `/artists/${profileSlug}` : `/clients/${profileSlug}`
        actions.push({
          title: "Your Profile",
          description: `View your ${userType} profile`,
          href: profilePath,
          icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" /></svg>`,
          keywords: ["profile", "your profile", "me", "my profile"]
        })
      }


      // Creation actions for artists
      if (userType === "artist") {
        actions.push(
          {
            title: "Add Portfolio Item",
            description: "Add work to your portfolio",
            href: "/portfolio_items/new",
            icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>`,
            keywords: ["portfolio", "add portfolio", "work", "showcase"]
          }
        )
      }

      // Inspiration board actions for approved users
      if (document.body.dataset.userApproved === "true") {
        actions.push(
          {
            title: "My Inspiration Boards",
            description: "Manage your inspiration boards",
            href: "/my/inspiration_boards",
            icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" /></svg>`,
            keywords: ["my inspiration", "inspiration boards", "my boards", "collections"]
          },
          {
            title: "Create Inspiration Board",
            description: "Start a new inspiration board",
            href: "/my/inspiration_boards/new",
            icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>`,
            keywords: ["create inspiration", "new inspiration board", "new board", "collection"]
          }
        )
      }


      // Settings and account actions
      actions.push(
        {
          title: "Account Settings",
          description: "Manage your account settings",
          href: "#",
          icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>`,
          keywords: ["settings", "account", "preferences", "configuration"]
        },
        {
          title: "Toggle Theme",
          description: "Switch between light and dark mode",
          href: "#",
          icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" /></svg>`,
          keywords: ["theme", "dark mode", "light mode", "appearance"],
          onClick: () => {
            const toggleButton = document.querySelector("[data-dark-mode-toggle]")
            if (toggleButton) {
              toggleButton.click()
            }
          }
        },
        {
          title: "Sign Out",
          description: "Sign out of your account",
          href: "/session",
          icon: `<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h6" /></svg>`,
          keywords: ["sign out", "logout", "log out"],
          onClick: () => {
            // Find existing sign out link and click it
            const signOutLink = document.querySelector('a[href="/session"][data-turbo-method="delete"]')
            if (signOutLink) {
              signOutLink.click()
            } else {
              // Fallback: create a form to submit DELETE request
              const form = document.createElement("form")
              form.method = "post"
              form.action = "/session"
              
              const methodInput = document.createElement("input")
              methodInput.type = "hidden"
              methodInput.name = "_method"
              methodInput.value = "delete"
              form.appendChild(methodInput)
              
              const csrfToken = document.querySelector("meta[name='csrf-token']")
              if (csrfToken) {
                const csrfInput = document.createElement("input")
                csrfInput.type = "hidden"
                csrfInput.name = "authenticity_token"
                csrfInput.value = csrfToken.content
                form.appendChild(csrfInput)
              }
              
              document.body.appendChild(form)
              form.submit()
            }
          }
        }
      )
    }

    return actions
  }
}