import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["container", "loading"]
  static values = { 
    url: String,
    page: { type: Number, default: 1 },
    perPage: { type: Number, default: 24 },
    hasMore: { type: Boolean, default: true },
    threshold: { type: Number, default: 200 }
  }

  connect() {
    this.loading = false
    this.bindScrollEvent()
    
    // Reset state on Turbo navigation
    document.addEventListener('turbo:before-render', this.handleTurboBeforeRender.bind(this))
  }

  disconnect() {
    this.unbindScrollEvent()
    document.removeEventListener('turbo:before-render', this.handleTurboBeforeRender.bind(this))
  }

  handleTurboBeforeRender() {
    this.loading = false
    this.unbindScrollEvent()
  }

  bindScrollEvent() {
    this.scrollHandler = this.handleScroll.bind(this)
    window.addEventListener('scroll', this.scrollHandler)
  }

  unbindScrollEvent() {
    window.removeEventListener('scroll', this.scrollHandler)
  }

  handleScroll() {
    if (this.loading || !this.hasMoreValue) {
      return
    }

    const scrollPosition = window.pageYOffset + window.innerHeight
    const documentHeight = document.documentElement.scrollHeight
    
    if (scrollPosition >= documentHeight - this.thresholdValue) {
      this.loadMore()
    }
  }

  async loadMore() {
    if (this.loading || !this.hasMoreValue) {
      return
    }

    this.loading = true
    this.showLoading()

    try {
      const nextPage = this.pageValue + 1
      const url = new URL(this.urlValue, window.location.origin)
      
      // Preserve existing search parameters
      const currentParams = new URLSearchParams(window.location.search)
      currentParams.set('page', nextPage)
      url.search = currentParams.toString()

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.html && data.html.trim()) {
        this.appendContent(data.html)
        this.pageValue = nextPage
        
        // Use server-provided has_more flag
        this.hasMoreValue = data.has_more || false
      } else {
        this.hasMoreValue = false
      }

    } catch (error) {
      console.error('Error loading more content:', error)
      this.showError()
    } finally {
      this.loading = false
      this.hideLoading()
    }
  }

  appendContent(html) {
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html
    
    // For masonry layouts, we need to handle this differently
    if (this.containerTarget.hasAttribute('data-masonry')) {
      this.appendToMasonry(tempDiv)
    } else {
      // For regular grids, just append
      while (tempDiv.firstChild) {
        this.containerTarget.appendChild(tempDiv.firstChild)
      }
    }
  }

  appendToMasonry(tempDiv) {
    // Add new items to masonry grid
    const newItems = []
    while (tempDiv.firstChild) {
      newItems.push(tempDiv.firstChild)
      this.containerTarget.appendChild(tempDiv.firstChild)
    }
    
    // For CSS columns masonry (which we're using), just trigger a layout refresh
    // Force a reflow to ensure proper column distribution
    if (newItems.length > 0) {
      this.containerTarget.style.columnCount = this.containerTarget.style.columnCount
      
      // Trigger a reflow
      this.containerTarget.offsetHeight
    }
  }

  countItemsInHTML(html) {
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html
    // Count direct children that look like content items
    return tempDiv.children.length
  }

  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
  }

  showError() {
    // Simple error handling - could be enhanced with a proper error message
    console.warn('Failed to load more content. Please try again.')
  }

  // Manual trigger for testing
  triggerLoad() {
    this.loadMore()
  }
}