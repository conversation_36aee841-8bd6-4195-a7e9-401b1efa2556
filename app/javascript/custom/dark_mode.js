class DarkModeManager {
  constructor() {
    this.storageKey = 'tattoo-marketplace-theme';
    this.darkClass = 'dark';
    this.init();
  }

  init() {
    this.setInitialTheme();
    this.setupToggleListeners();
    this.updateToggleButtons();
  }

  setInitialTheme() {
    const savedTheme = localStorage.getItem(this.storageKey);
    
    if (savedTheme) {
      // Use saved preference
      this.setTheme(savedTheme);
    } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      // Use system preference if no saved preference
      this.setTheme('dark');
    } else {
      // Default to light mode
      this.setTheme('light');
    }

    // Listen for system theme changes
    if (window.matchMedia) {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem(this.storageKey)) {
          this.setTheme(e.matches ? 'dark' : 'light');
        }
      });
    }
  }

  setTheme(theme) {
    const html = document.documentElement;
    
    if (theme === 'dark') {
      html.classList.add(this.darkClass);
    } else {
      html.classList.remove(this.darkClass);
    }
    
    localStorage.setItem(this.storageKey, theme);
    this.updateToggleButtons();
  }

  toggleTheme() {
    const currentTheme = this.getCurrentTheme();
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  getCurrentTheme() {
    return document.documentElement.classList.contains(this.darkClass) ? 'dark' : 'light';
  }

  setupToggleListeners() {
    document.addEventListener('click', (e) => {
      if (e.target.matches('[data-dark-mode-toggle]') || e.target.closest('[data-dark-mode-toggle]')) {
        e.preventDefault();
        this.toggleTheme();
      }
    });
  }

  updateToggleButtons() {
    const toggles = document.querySelectorAll('[data-dark-mode-toggle]');
    const currentTheme = this.getCurrentTheme();
    
    toggles.forEach(toggle => {
      const lightIcon = toggle.querySelector('[data-light-icon]');
      const darkIcon = toggle.querySelector('[data-dark-icon]');
      const toggleText = toggle.querySelector('[data-toggle-text]');
      
      if (lightIcon && darkIcon) {
        if (currentTheme === 'dark') {
          lightIcon.style.display = 'block';
          darkIcon.style.display = 'none';
        } else {
          lightIcon.style.display = 'none';
          darkIcon.style.display = 'block';
        }
      }
      
      if (toggleText) {
        toggleText.textContent = currentTheme === 'dark' ? 'Light Mode' : 'Dark Mode';
      }
    });
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.darkModeManager = new DarkModeManager();
});

// Also initialize on Turbo navigation
document.addEventListener('turbo:load', () => {
  if (!window.darkModeManager) {
    window.darkModeManager = new DarkModeManager();
  } else {
    window.darkModeManager.updateToggleButtons();
  }
});

export default DarkModeManager;

// console.log('test');