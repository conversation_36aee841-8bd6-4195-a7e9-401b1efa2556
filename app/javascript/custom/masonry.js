// Masonry layout functionality
// Provides reusable masonry grid layout using CSS columns

class MasonryLayout {
  constructor(containerId = 'masonry-container') {
    this.container = document.getElementById(containerId);
    this.initialized = false;
    
    if (this.container) {
      this.init();
    }
  }

  init() {
    if (this.initialized) return;
    
    // Wait for images to load before initializing masonry
    const images = this.container.querySelectorAll('img');
    let loadedImages = 0;
    
    const checkImagesLoaded = () => {
      loadedImages++;
      if (loadedImages === images.length) {
        // All images loaded, initialize masonry
        setTimeout(() => {
          this.applyLayout();
        }, 100);
      }
    };
    
    // Handle window resize
    window.addEventListener('resize', () => {
      if (this.container) {
        this.applyLayout();
      }
    });
    
    if (images.length === 0) {
      // No images, initialize immediately
      this.applyLayout();
    } else {
      // Wait for all images to load
      images.forEach(img => {
        if (img.complete) {
          checkImagesLoaded();
        } else {
          img.addEventListener('load', checkImagesLoaded);
          img.addEventListener('error', checkImagesLoaded); // Handle failed loads
        }
      });
    }
    
    this.initialized = true;
  }

  applyLayout() {
    if (!this.container) return;
    
    this.container.style.columnCount = this.getColumnCount();
    this.container.style.columnGap = '1.5rem';
  }

  getColumnCount() {
    const width = window.innerWidth;
    if (width >= 1280) return '4';
    if (width >= 1024) return '3';
    if (width >= 640) return '2';
    return '1';
  }

  // Static method to initialize masonry on page load
  static initialize(containerId = 'masonry-container') {
    return new MasonryLayout(containerId);
  }
}

// Auto-initialize masonry when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize default masonry container
  MasonryLayout.initialize();
  
  // Initialize any additional masonry containers
  document.querySelectorAll('[data-masonry]').forEach(container => {
    if (container.id && container.id !== 'masonry-container') {
      MasonryLayout.initialize(container.id);
    }
  });
});

// Export for manual initialization if needed
window.MasonryLayout = MasonryLayout;