// Inspiration Board Save Modal Functionality

// Global variables to store current save context
let currentBoards = [];

// Function for posts
function showSaveToBoardModal(postId = null, type = 'post') {
  const modal = document.getElementById('save-to-board-modal');
  if (!modal) {
    console.error('Save to board modal not found');
    showToast('Modal not found. Please refresh the page.', 'error');
    return;
  }

  // Set the item ID and type
  const postInput = document.getElementById('modal-post-id');
  const portfolioInput = document.getElementById('modal-portfolio-item-id');
  
  if (!postInput || !portfolioInput) {
    console.error('Modal form inputs not found');
    showToast('Modal form not properly loaded. Please refresh the page.', 'error');
    return;
  }
  
  if (type === 'post') {
    postInput.value = postId || '';
    portfolioInput.value = '';
  } else {
    postInput.value = '';
    portfolioInput.value = postId || '';
  }

  // Show the modal first
  modal.showModal();
  
  // Then load user's boards and populate the select
  loadUserBoards();
}

// Function for portfolio items
function showSaveToBoardModalPortfolio(portfolioItemId) {
  showSaveToBoardModal(portfolioItemId, 'portfolio');
}

// Load user's boards and populate the select dropdown
async function loadUserBoards() {
  try {
    const response = await fetch('/my/inspiration_boards.json', {
      method: 'GET',
      credentials: 'same-origin', // Include cookies for authentication
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      }
    });
    
    if (!response.ok) {
      console.error('Response not OK:', response.status, response.statusText);
      const responseText = await response.text();
      console.error('Response body:', responseText);
      
      if (response.status === 401) {
        throw new Error('You must be logged in to access your boards');
      } else if (response.status === 403) {
        throw new Error('Access denied. Please ensure your account is approved.');
      }
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }
    
    const boards = await response.json();
    currentBoards = boards;
    
    console.log('Loaded boards:', boards); // Debug log
    
    const select = document.getElementById('board-select');
    const boardsSection = document.getElementById('boards-section');
    const noBoardsSection = document.getElementById('no-boards-section');
    const saveButton = document.getElementById('save-button');
    
    if (!select) {
      console.error('Board select element not found');
      return;
    }
    
    // Clear existing options
    select.innerHTML = '<option value="">Select a board...</option>';
    
    if (boards.length > 0) {
      // Populate select with boards
      boards.forEach(board => {
        const option = document.createElement('option');
        option.value = board.id;
        option.textContent = `${board.name} (${board.items_count || 0} items)`;
        select.appendChild(option);
      });
      
      boardsSection.classList.remove('hidden');
      noBoardsSection.classList.add('hidden');
      saveButton.disabled = false;
    } else {
      boardsSection.classList.add('hidden');
      noBoardsSection.classList.remove('hidden');
      saveButton.disabled = true;
    }
  } catch (error) {
    console.error('Error loading boards:', error);
    
    // Show error state with more helpful message
    const boardsSection = document.getElementById('boards-section');
    const noBoardsSection = document.getElementById('no-boards-section');
    const saveButton = document.getElementById('save-button');
    
    if (boardsSection) boardsSection.classList.add('hidden');
    if (noBoardsSection) {
      noBoardsSection.classList.remove('hidden');
      // Update the no boards message to indicate an error
      const message = noBoardsSection.querySelector('p');
      if (message) {
        message.textContent = 'Error loading boards. Please try again.';
      }
    }
    if (saveButton) saveButton.disabled = true;
    
    // Show more specific error toast
    if (error.message.includes('logged in')) {
      showToast('Please log in to save items to boards', 'error');
    } else if (error.message.includes('Access denied')) {
      showToast('Your account needs approval to use this feature', 'error');
    } else {
      showToast('Failed to load inspiration boards', 'error');
    }
  }
}

// Handle form submission
function handleSaveToBoardSubmission() {
  const form = document.getElementById('save-to-board-form');
  if (!form) return;

  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(form);
    const boardId = formData.get('inspiration_board_id');
    
    if (!boardId) {
      alert('Please select a board');
      return;
    }

    try {
      const response = await fetch('/inspiration_board_items', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
      });

      if (response.ok) {
        // Close modal
        const modal = document.getElementById('save-to-board-modal');
        modal.close();
        
        // Show success message
        showToast('Item saved to board successfully!', 'success');
        
        // Reset form
        form.reset();
      } else {
        const errorText = await response.text();
        showToast('Failed to save item to board', 'error');
        console.error('Save failed:', errorText);
      }
    } catch (error) {
      console.error('Error saving to board:', error);
      showToast('An error occurred while saving', 'error');
    }
  });
}

// Simple toast notification
function showToast(message, type = 'info') {
  const toast = document.createElement('div');
  toast.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
    type === 'success' ? 'bg-green-600' : 
    type === 'error' ? 'bg-red-600' : 
    'bg-blue-600'
  }`;
  toast.textContent = message;
  
  document.body.appendChild(toast);
  
  setTimeout(() => {
    toast.remove();
  }, 3000);
}

// Legacy functions for backwards compatibility
function toggleSaveMenu(postId = null) {
  showSaveToBoardModal(postId, 'post');
}

function toggleSaveMenuPortfolio(portfolioItemId) {
  showSaveToBoardModalPortfolio(portfolioItemId);
}

// Function for notes forms in board show page
function toggleNotesForm(itemId) {
  const form = document.getElementById(`notes-form-${itemId}`);
  if (!form) return;
  
  if (form.classList.contains('hidden')) {
    form.classList.remove('hidden');
  } else {
    form.classList.add('hidden');
  }
}

// Initialize modal functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  handleSaveToBoardSubmission();
  
  // Enable/disable save button based on board selection
  const boardSelect = document.getElementById('board-select');
  const saveButton = document.getElementById('save-button');
  
  if (boardSelect && saveButton) {
    boardSelect.addEventListener('change', function() {
      saveButton.disabled = !this.value;
      if (this.value) {
        saveButton.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
      } else {
        saveButton.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
      }
    });
  }
});

// Make functions globally available
window.showSaveToBoardModal = showSaveToBoardModal;
window.showSaveToBoardModalPortfolio = showSaveToBoardModalPortfolio;
window.toggleSaveMenu = toggleSaveMenu;
window.toggleSaveMenuPortfolio = toggleSaveMenuPortfolio;
window.toggleNotesForm = toggleNotesForm;