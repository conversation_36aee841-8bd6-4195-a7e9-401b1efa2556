// Handle delete confirmation modal form submissions
document.addEventListener('DOMContentLoaded', function() {
  // Handle form submissions for delete confirmation
  document.addEventListener('submit', function(event) {
    const form = event.target;
    if (form.hasAttribute('data-delete-form')) {
      const commentId = form.getAttribute('data-delete-form');
      const deleteButton = form.querySelector(`[data-delete-button="${commentId}"]`);
      const deleteText = form.querySelector(`[data-delete-text="${commentId}"]`);
      const deleteLoading = form.querySelector(`[data-delete-loading="${commentId}"]`);
      
      if (deleteButton && deleteText && deleteLoading) {
        // Disable the button and show loading state
        deleteButton.disabled = true;
        deleteText.classList.add('hidden');
        deleteLoading.classList.remove('hidden');
        
        // Optional: You can add additional logic here for AJAX submission later
      }
    }
  });
});