/*! @tailwindplus/elements v1.0.0 | Proprietary License | https://tailwindcss.com/plus/license */
var dn=Object.defineProperty;var mo=e=>{throw TypeError(e)};var pn=(e,n,t)=>n in e?dn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t;var H=(e,n,t)=>pn(e,typeof n!="symbol"?n+"":n,t),St=(e,n,t)=>n.has(e)||mo("Cannot "+t);var f=(e,n,t)=>(St(e,n,"read from private field"),t?t.call(e):n.get(e)),I=(e,n,t)=>n.has(e)?mo("Cannot add the same private member more than once"):n instanceof WeakSet?n.add(e):n.set(e,t),S=(e,n,t,o)=>(St(e,n,"write to private field"),o?o.call(e,t):n.set(e,t),t),F=(e,n,t)=>(St(e,n,"access private method"),t);if(typeof globalThis.window<"u"){let e=!1;document.addEventListener("submit",n=>{if(e){e=!1;return}let t=n.target;if(t&&t.method==="dialog"){let o=t.closest("el-dialog");if(!o||!("beforeClose"in o))return;let r=o.beforeClose();if(r===!0||(n.preventDefault(),n.stopImmediatePropagation(),r===!1))return;r.then(i=>{i&&(e=!0,t.dispatchEvent(n))}).catch(console.error)}},!0)}var rt=class extends Event{constructor(n,{oldState:t="",newState:o="",...r}={}){super(n,r);H(this,"oldState");H(this,"newState");this.oldState=String(t||""),this.newState=String(o||"")}},ho=new WeakMap;function Ao(e,n,t){ho.set(e,setTimeout(()=>{ho.has(e)&&e.dispatchEvent(new rt("toggle",{cancelable:!1,oldState:n,newState:t}))},0))}var Mt=globalThis.ShadowRoot||function(){},mn=globalThis.HTMLDialogElement||function(){},et=new WeakMap,Y=new WeakMap,B=new WeakMap,Ae=new WeakMap;function tt(e){return Ae.get(e)||"hidden"}var ot=new WeakMap;function qe(e){return[...e].pop()}function hn(e){let n=e.popoverTargetElement;if(!(n instanceof HTMLElement))return;let t=tt(n);e.popoverTargetAction==="show"&&t==="showing"||e.popoverTargetAction==="hide"&&t==="hidden"||(t==="showing"?Te(n,!0,!0):fe(n,!1)&&(ot.set(n,e),Ot(n)))}function fe(e,n){return!(e.popover!=="auto"&&e.popover!=="manual"&&e.popover!=="hint"||!e.isConnected||n&&tt(e)!=="showing"||!n&&tt(e)!=="hidden"||e instanceof mn&&e.hasAttribute("open")||document.fullscreenElement===e)}function go(e){if(!e)return 0;let n=Y.get(document)||new Set,t=B.get(document)||new Set;return t.has(e)?[...t].indexOf(e)+n.size+1:n.has(e)?[...n].indexOf(e)+1:0}function gn(e){let n=xo(e),t=bn(e);return go(n)>go(t)?n:t}function Ve(e){let n,t=B.get(e)||new Set,o=Y.get(e)||new Set,r=t.size>0?t:o.size>0?o:null;return r?(n=qe(r),n.isConnected?n:(r.delete(n),Ve(e))):null}function bo(e){for(let n of e||[])if(!n.isConnected)e.delete(n);else return n;return null}function xe(e){return typeof e.getRootNode=="function"?e.getRootNode():e.parentNode?xe(e.parentNode):e}function xo(e){for(;e;){if(e instanceof HTMLElement&&e.popover==="auto"&&Ae.get(e)==="showing")return e;if(e=e instanceof Element&&e.assignedSlot||e.parentElement||xe(e),e instanceof Mt&&(e=e.host),e instanceof Document)return}}function bn(e){for(;e;){let n=e.popoverTargetElement;if(n instanceof HTMLElement)return n;if(e=e.parentElement||xe(e),e instanceof Mt&&(e=e.host),e instanceof Document)return}}function vo(e,n){let t=new Map,o=0;for(let s of n||[])t.set(s,o),o+=1;t.set(e,o),o+=1;let r=null;function i(s){if(!s)return;let a=!1,l=null,u=null;for(;!a;){if(l=xo(s)||null,l===null||!t.has(l))return;(e.popover==="hint"||l.popover==="auto")&&(a=!0),a||(s=l.parentElement)}u=t.get(l),(r===null||t.get(r)<u)&&(r=l)}return i(e.parentElement||xe(e)),r}function vn(e){return e.hidden||e instanceof Mt||(e instanceof HTMLButtonElement||e instanceof HTMLInputElement||e instanceof HTMLSelectElement||e instanceof HTMLTextAreaElement||e instanceof HTMLOptGroupElement||e instanceof HTMLOptionElement||e instanceof HTMLFieldSetElement)&&e.disabled||e instanceof HTMLInputElement&&e.type==="hidden"||e instanceof HTMLAnchorElement&&e.href===""?!1:typeof e.tabIndex=="number"&&e.tabIndex!==-1}function wn(e){if(e.shadowRoot&&e.shadowRoot.delegatesFocus!==!0)return null;let n=e;n.shadowRoot&&(n=n.shadowRoot);let t=n.querySelector("[autofocus]");if(t)return t;{let i=n.querySelectorAll("slot");for(let s of i){let a=s.assignedElements({flatten:!0});for(let l of a){if(l.hasAttribute("autofocus"))return l;if(t=l.querySelector("[autofocus]"),t)return t}}}let o=e.ownerDocument.createTreeWalker(n,NodeFilter.SHOW_ELEMENT),r=o.currentNode;for(;r;){if(vn(r))return r;r=o.nextNode()}}function En(e){wn(e)?.focus()}var nt=new WeakMap;function Ot(e){if(!fe(e,!1))return;let n=e.ownerDocument;if(!e.dispatchEvent(new rt("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!fe(e,!1))return;let t=!1,o=e.popover,r=null,i=vo(e,Y.get(n)||new Set),s=vo(e,B.get(n)||new Set);if(o==="auto"&&(Dt(B.get(n)||new Set,t,!0),de(i||n,t,!0),r="auto"),o==="hint"&&(s?(de(s,t,!0),r="hint"):(Dt(B.get(n)||new Set,t,!0),i?(de(i,t,!0),r="auto"):r="hint")),o==="auto"||o==="hint"){if(o!==e.popover||!fe(e,!1))return;Ve(n)||(t=!0),r==="auto"?(Y.has(n)||Y.set(n,new Set),Y.get(n).add(e)):r==="hint"&&(B.has(n)||B.set(n,new Set),B.get(n).add(e))}nt.delete(e);let a=n.activeElement;e.classList.add(":popover-open"),Ae.set(e,"showing"),et.has(n)||et.set(n,new Set),et.get(n).add(e),To(ot.get(e),!0),En(e),t&&a&&e.popover==="auto"&&nt.set(e,a),Ao(e,"closed","open")}function Te(e,n=!1,t=!1){if(!fe(e,!0))return;let o=e.ownerDocument;if(["auto","hint"].includes(e.popover)&&(de(e,n,t),!fe(e,!0)))return;let r=Y.get(o)||new Set,i=r.has(e)&&qe(r)===e;if(To(ot.get(e),!1),ot.delete(e),t&&(e.dispatchEvent(new rt("beforetoggle",{oldState:"open",newState:"closed"})),i&&qe(r)!==e&&de(e,n,t),!fe(e,!0)))return;et.get(o)?.delete(e),r.delete(e),B.get(o)?.delete(e),e.classList.remove(":popover-open"),Ae.set(e,"hidden"),t&&Ao(e,"open","closed");let s=nt.get(e);s&&(nt.delete(e),n&&s.focus())}function yn(e,n=!1,t=!1){let o=Ve(e);for(;o;)Te(o,n,t),o=Ve(e)}function Dt(e,n=!1,t=!1){let o=bo(e);for(;o;)Te(o,n,t),o=bo(e)}function wo(e,n,t,o){let r=!1,i=!1;for(;r||!i;){i=!0;let s=null,a=!1;for(let l of n)if(l===e)a=!0;else if(a){s=l;break}if(!s)return;for(;tt(s)==="showing"&&n.size;)Te(qe(n),t,o);n.has(e)&&qe(n)!==e&&(r=!0),r&&(o=!1)}}function de(e,n,t){let o=e.ownerDocument||e;if(e instanceof Document)return yn(o,n,t);if(B.get(o)?.has(e)){wo(e,B.get(o),n,t);return}Dt(B.get(o)||new Set,n,t),Y.get(o)?.has(e)&&wo(e,Y.get(o),n,t)}var It=new WeakMap;function Eo(e){if(!e.isTrusted)return;let n=e.composedPath()[0];if(!n)return;let t=n.ownerDocument;if(!Ve(t))return;let r=gn(n);if(r&&e.type==="pointerdown")It.set(t,r);else if(e.type==="pointerup"){let i=It.get(t)===r;It.delete(t),i&&de(r||t,!1,!0)}}var kt=new WeakMap;function To(e,n=!1){if(!e)return;kt.has(e)||kt.set(e,e.getAttribute("aria-expanded"));let t=e.popoverTargetElement;if(t instanceof HTMLElement&&t.popover==="auto")e.setAttribute("aria-expanded",String(n));else{let o=kt.get(e);o?e.setAttribute("aria-expanded",o):e.removeAttribute("aria-expanded")}}var yo=globalThis.ShadowRoot||function(){};function An(){return typeof HTMLElement<"u"&&typeof HTMLElement.prototype=="object"&&"popover"in HTMLElement.prototype}function ce(e,n,t){let o=e[n];Object.defineProperty(e,n,{value(r){return o.call(this,t(r))}})}var xn=/(^|[^\\]):popover-open\b/g;function Tn(){return typeof globalThis.CSSLayerBlockRule=="function"}function Ln(){let e=Tn();return`
${e?"@layer popover-polyfill {":""}
  :where([popover]) {
    position: fixed;
    z-index: 2147483647;
    inset: 0;
    padding: 0.25em;
    width: fit-content;
    height: fit-content;
    border-width: initial;
    border-color: initial;
    border-image: initial;
    border-style: solid;
    background-color: canvas;
    color: canvastext;
    overflow: auto;
    margin: auto;
  }

  :where([popover]:not(.\\:popover-open)) {
    display: none;
  }

  :where(dialog[popover].\\:popover-open) {
    display: block;
  }

  :where(dialog[popover][open]) {
    display: revert;
  }

  :where([anchor].\\:popover-open) {
    inset: auto;
  }

  :where([anchor]:popover-open) {
    inset: auto;
  }

  @supports not (background-color: canvas) {
    :where([popover]) {
      background-color: white;
      color: black;
    }
  }

  @supports (width: -moz-fit-content) {
    :where([popover]) {
      width: -moz-fit-content;
      height: -moz-fit-content;
    }
  }

  @supports not (inset: 0) {
    :where([popover]) {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
${e?"}":""}
`}var ye=null;function Pt(e){let n=Ln();if(ye===null)try{ye=new CSSStyleSheet,ye.replaceSync(n)}catch{ye=!1}if(ye===!1){let t=document.createElement("style");t.textContent=n,e instanceof Document?e.head.prepend(t):e.prepend(t)}else e.adoptedStyleSheets=[ye,...e.adoptedStyleSheets]}function Sn(){if(typeof window>"u")return;window.ToggleEvent=window.ToggleEvent||rt;function e(l){return l?.includes(":popover-open")&&(l=l.replace(xn,"$1.\\:popover-open")),l}ce(Document.prototype,"querySelector",e),ce(Document.prototype,"querySelectorAll",e),ce(Element.prototype,"querySelector",e),ce(Element.prototype,"querySelectorAll",e),ce(Element.prototype,"matches",e),ce(Element.prototype,"closest",e),ce(DocumentFragment.prototype,"querySelectorAll",e),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let l=(this.getAttribute("popover")||"").toLowerCase();return l===""||l=="auto"?"auto":l=="hint"?"hint":"manual"},set(l){l===null?this.removeAttribute("popover"):this.setAttribute("popover",l)}},showPopover:{enumerable:!0,configurable:!0,value(l={}){Ot(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){Te(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(l={}){return typeof l=="boolean"&&(l={force:l}),Ae.get(this)==="showing"&&l.force===void 0||l.force===!1?Te(this,!0,!0):(l.force===void 0||l.force===!0)&&Ot(this),Ae.get(this)==="showing"}}});let n=Element.prototype.attachShadow;n&&Object.defineProperties(Element.prototype,{attachShadow:{enumerable:!0,configurable:!0,writable:!0,value(l){let u=n.call(this,l);return Pt(u),u}}});let t=HTMLElement.prototype.attachInternals;t&&Object.defineProperties(HTMLElement.prototype,{attachInternals:{enumerable:!0,configurable:!0,writable:!0,value(){let l=t.call(this);return l.shadowRoot&&Pt(l.shadowRoot),l}}});let o=new WeakMap;function r(l){Object.defineProperties(l.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(u){if(u===null)this.removeAttribute("popovertarget"),o.delete(this);else if(u instanceof Element)this.setAttribute("popovertarget",""),o.set(this,u);else throw new TypeError("popoverTargetElement must be an element or null")},get(){if(this.localName!=="button"&&this.localName!=="input"||this.localName==="input"&&this.type!=="reset"&&this.type!=="image"&&this.type!=="button"||this.disabled||this.form&&this.type==="submit")return null;let u=o.get(this);if(u&&u.isConnected)return u;if(u&&!u.isConnected)return o.delete(this),null;let m=xe(this),g=this.getAttribute("popovertarget");return(m instanceof Document||m instanceof yo)&&g&&m.getElementById(g)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let u=(this.getAttribute("popovertargetaction")||"").toLowerCase();return u==="show"||u==="hide"?u:"toggle"},set(u){this.setAttribute("popovertargetaction",u)}}})}r(HTMLButtonElement),r(HTMLInputElement);let i=l=>{let u=l.composedPath(),m=u[0];if(!(m instanceof Element)||m?.shadowRoot)return;let g=xe(m);if(!(g instanceof yo||g instanceof Document))return;let c=u.find(b=>b.matches?.("[popovertargetaction],[popovertarget]"));if(c){hn(c),l.preventDefault();return}},s=l=>{let u=l.key,m=l.target;!l.defaultPrevented&&m&&(u==="Escape"||u==="Esc")&&de(m.ownerDocument,!0,!0)};(l=>{l.addEventListener("click",i),l.addEventListener("keydown",s),l.addEventListener("pointerdown",Eo),l.addEventListener("pointerup",Eo)})(document),Pt(document)}An()||Sn();function Lo(){return typeof HTMLButtonElement<"u"&&"command"in HTMLButtonElement.prototype&&"source"in((globalThis.CommandEvent||{}).prototype||{})}function So(){document.addEventListener("invoke",d=>{d.type=="invoke"&&d.isTrusted&&(d.stopImmediatePropagation(),d.preventDefault())},!0),document.addEventListener("command",d=>{d.type=="command"&&d.isTrusted&&(d.stopImmediatePropagation(),d.preventDefault())},!0);function e(d,p,h=!0){Object.defineProperty(d,p,{...Object.getOwnPropertyDescriptor(d,p),enumerable:h})}function n(d){return d&&typeof d.getRootNode=="function"?d.getRootNode():d&&d.parentNode?n(d.parentNode):d}let t=new WeakMap,o=new WeakMap;class r extends Event{constructor(p,h={}){super(p,h);let{source:w,command:y}=h;if(w!=null&&!(w instanceof Element))throw new TypeError("source must be an element");t.set(this,w||null),o.set(this,y!==void 0?String(y):"")}get[Symbol.toStringTag](){return"CommandEvent"}get source(){if(!t.has(this))throw new TypeError("illegal invocation");let p=t.get(this);if(!(p instanceof Element))return null;let h=n(p);return h!==n(this.target||document)?h.host:p}get command(){if(!o.has(this))throw new TypeError("illegal invocation");return o.get(this)}get action(){throw new Error("CommandEvent#action was renamed to CommandEvent#command")}get invoker(){throw new Error("CommandEvent#invoker was renamed to CommandEvent#source")}}e(r.prototype,"source"),e(r.prototype,"command");class i extends Event{constructor(){throw new Error("InvokeEvent has been deprecated, it has been renamed to `CommandEvent`")}}let s=new WeakMap;function a(d){Object.defineProperties(d.prototype,{commandForElement:{enumerable:!0,configurable:!0,set(p){if(this.hasAttribute("invokeaction"))throw new TypeError("Element has deprecated `invokeaction` attribute, replace with `command`");if(this.hasAttribute("invoketarget"))throw new TypeError("Element has deprecated `invoketarget` attribute, replace with `commandfor`");if(p===null)this.removeAttribute("commandfor"),s.delete(this);else if(p instanceof Element){this.setAttribute("commandfor","");let h=n(p);n(this)===h||h===this.ownerDocument?s.set(this,p):s.delete(this)}else throw new TypeError("commandForElement must be an element or null")},get(){if(this.localName!=="button")return null;if(this.hasAttribute("invokeaction")||this.hasAttribute("invoketarget"))return console.warn("Element has deprecated `invoketarget` or `invokeaction` attribute, use `commandfor` and `command` instead"),null;if(this.disabled)return null;if(this.form&&this.getAttribute("type")!=="button")return console.warn("Element with `commandFor` is a form participant. It should explicitly set `type=button` in order for `commandFor` to work"),null;let p=s.get(this);if(p)return p.isConnected?p:(s.delete(this),null);let h=n(this),w=this.getAttribute("commandfor");return(h instanceof Document||h instanceof ShadowRoot)&&w&&h.getElementById(w)||null}},command:{enumerable:!0,configurable:!0,get(){let p=this.getAttribute("command")||"";if(p.startsWith("--"))return p;let h=p.toLowerCase();switch(h){case"show-modal":case"close":case"toggle-popover":case"hide-popover":case"show-popover":return h}return""},set(p){this.setAttribute("command",p)}},invokeAction:{enumerable:!1,configurable:!0,get(){throw new Error("invokeAction is deprecated. It has been renamed to command")},set(p){throw new Error("invokeAction is deprecated. It has been renamed to command")}},invokeTargetElement:{enumerable:!1,configurable:!0,get(){throw new Error("invokeTargetElement is deprecated. It has been renamed to command")},set(p){throw new Error("invokeTargetElement is deprecated. It has been renamed to command")}}})}let l=new WeakMap;Object.defineProperties(HTMLElement.prototype,{oncommand:{enumerable:!0,configurable:!0,get(){return m.takeRecords(),l.get(this)||null},set(d){let p=l.get(this)||null;p&&this.removeEventListener("command",p),l.set(this,typeof d=="object"||typeof d=="function"?d:null),typeof d=="function"&&this.addEventListener("command",d)}}});function u(d){for(let p of d)p.oncommand=new Function("event",p.getAttribute("oncommand"))}let m=new MutationObserver(d=>{for(let p of d){let{target:h}=p;p.type==="childList"?u(h.querySelectorAll("[oncommand]")):u([h])}});m.observe(document,{subtree:!0,childList:!0,attributeFilter:["oncommand"]}),u(document.querySelectorAll("[oncommand]"));function g(d){if(d.defaultPrevented||d.type!=="click")return;let p=d.target.closest("button[invoketarget], button[invokeaction], input[invoketarget], input[invokeaction]");if(p&&(console.warn("Elements with `invoketarget` or `invokeaction` are deprecated and should be renamed to use `commandfor` and `command` respectively"),p.matches("input")))throw new Error("Input elements no longer support `commandfor`");let h=d.target.closest("button[commandfor], button[command]");if(!h)return;if(h.form&&h.getAttribute("type")!=="button")throw d.preventDefault(),new Error("Element with `commandFor` is a form participant. It should explicitly set `type=button` in order for `commandFor` to work. In order for it to act as a Submit button, it must not have command or commandfor attributes");if(h.hasAttribute("command")!==h.hasAttribute("commandfor")){let A=h.hasAttribute("command")?"command":"commandfor",E=h.hasAttribute("command")?"commandfor":"command";throw new Error(`Element with ${A} attribute must also have a ${E} attribute to function.`)}if(h.command!=="show-popover"&&h.command!=="hide-popover"&&h.command!=="toggle-popover"&&h.command!=="show-modal"&&h.command!=="close"&&!h.command.startsWith("--")){console.warn(`"${h.command}" is not a valid command value. Custom commands must begin with --`);return}let w=h.commandForElement;if(!w)return;let y=new r("command",{command:h.command,source:h,cancelable:!0});if(w.dispatchEvent(y),y.defaultPrevented)return;let v=y.command.toLowerCase();if(w.popover){let A=!w.matches(":popover-open");A&&(v==="toggle-popover"||v==="show-popover")?w.showPopover({source:h}):!A&&v==="hide-popover"&&w.hidePopover()}else if(w.localName==="dialog"){let A=!w.hasAttribute("open");A&&v==="show-modal"?w.showModal():!A&&v==="close"&&w.close()}}function c(d){d.addEventListener("click",g,!0)}function b(d,p){let h=d.prototype.attachShadow;d.prototype.attachShadow=function(y){let v=h.call(this,y);return p(v),v};let w=d.prototype.attachInternals;d.prototype.attachInternals=function(){let y=w.call(this);return y.shadowRoot&&p(y.shadowRoot),y}}a(HTMLButtonElement),b(HTMLElement,d=>{c(d),m.observe(d,{attributeFilter:["oncommand"]}),u(d.querySelectorAll("[oncommand]"))}),c(document),Object.assign(globalThis,{CommandEvent:r,InvokeEvent:i})}function Io(){if(typeof HTMLDialogElement!="function")return!1;let e=!1,n=document.createElement("dialog");return n.addEventListener("beforetoggle",t=>{e=!0,t.preventDefault()}),n.show(),e}function ko(){let e=new WeakMap;function n(s){let a=s.open?"closed":"open",l=s.open?"open":"closed";if(e.has(s)){let u=e.get(s);l=u.oldState,clearTimeout(u.id)}e.set(s,{oldState:l,id:setTimeout(()=>{s.dispatchEvent(new ToggleEvent("toggle",{newState:a,oldState:l}))})})}let t=HTMLDialogElement.prototype.show,o=HTMLDialogElement.prototype.showModal,r=HTMLDialogElement.prototype.close;function i(s){let a=new ToggleEvent("beforetoggle",{newState:"closed",oldState:"open",cancelable:!1});s.dispatchEvent(a),s.open&&n(s)}document.addEventListener("submit",s=>{let a=s.target;if(a.method==="dialog"){let l=a.closest("dialog");l instanceof HTMLDialogElement&&i(l)}},!0),Object.defineProperties(HTMLDialogElement.prototype,{show:{value(){if(this.open||this.matches(":popover-open, :modal")||!this.ownerDocument)return t.apply(this,arguments);let s=new ToggleEvent("beforetoggle",{newState:"open",oldState:"closed",cancelable:!0});this.dispatchEvent(s)&&(n(this),t.apply(this,arguments))}},showModal:{value(){if(this.open||this.matches(":popover-open, :modal")||!this.isConnected||!this.ownerDocument)return o.apply(this,arguments);let s=new ToggleEvent("beforetoggle",{newState:"open",oldState:"closed",cancelable:!0});if(this.dispatchEvent(s))return n(this),o.apply(this,arguments)}},close:{value(){return!this.open&&!this.matches(":popover-open, :modal")?r.apply(this,arguments):(i(this),r.apply(this,arguments))}}})}function it(e){function n(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",n))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",n),n())}typeof globalThis.window<"u"&&(Lo()||So(),Io()||ko(),it(()=>{let e=document.createElement("style");e.textContent="@layer popover-polyfill;",e.setAttribute("suppressHydrationWarning",""),document.documentElement.prepend(e)}));function Le(e){"focus"in e&&e.focus({focusVisible:st})}var st=!1;if(typeof globalThis.window<"u"){let e;(o=>(o[o.Keyboard=0]="Keyboard",o[o.Mouse=1]="Mouse"))(e||(e={})),document.addEventListener("keydown",n=>{n.metaKey||n.altKey||n.ctrlKey||(st=!0,document.documentElement.dataset.focusVisible="")},!0),document.addEventListener("click",n=>{n.detail===1?(st=!1,delete document.documentElement.dataset.focusVisible):n.detail===0&&(st=!0,document.documentElement.dataset.focusVisible="")},!0)}typeof globalThis.HTMLElement>"u"&&(globalThis.HTMLElement=class{});var pe,We,te,x=class extends HTMLElement{constructor(){super(...arguments);I(this,pe,new AbortController);I(this,We,!1);I(this,te,!1)}connectedCallback(){if("observedAttributes"in this.constructor&&typeof this.constructor.observedAttributes=="object"&&Array.isArray(this.constructor.observedAttributes))for(let t of this.constructor.observedAttributes)typeof t=="string"&&(t in this||Object.defineProperty(this,t,{get(){return this.getAttribute(t)},set(o){if(o==null||o===!1){this.removeAttribute(t);return}this.setAttribute(t,o)}}));S(this,We,!0),queueMicrotask(()=>{if(!f(this,pe).signal.aborted)try{this.mount?.(f(this,pe).signal)}catch(t){console.error(t)}})}disconnectedCallback(){f(this,pe).abort(),S(this,pe,new AbortController)}setAttributeNoCallbacks(t,o){try{S(this,te,!0),this.setAttribute(t,o)}finally{S(this,te,!1)}}removeAttributeNoCallbacks(t){try{S(this,te,!0),this.removeAttribute(t)}finally{S(this,te,!1)}}attributeChangedCallback(t,o,r){f(this,We)&&(f(this,te)||o!==r&&this.onAttributeChange?.(t,o,r))}};pe=new WeakMap,We=new WeakMap,te=new WeakMap;function T(e,n){typeof globalThis.customElements>"u"||customElements.get(e)===n||customElements.define(e,n)}function Q(){let e=[],n={addEventListener(t,o,r,i){return t.addEventListener(o,r,i),n.add(()=>t.removeEventListener(o,r,i))},requestAnimationFrame(...t){let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(...t){return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(...t){let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(...t){let o={current:!0};return queueMicrotask(()=>{o.current&&t[0]()}),n.add(()=>{o.current=!1})},style(t,o,r){let i=t.style.getPropertyValue(o);return o.startsWith("--")?t.style.setProperty(o,r):Object.assign(t.style,{[o]:r}),this.add(()=>{o.startsWith("--")?t.style.setProperty(o,i):Object.assign(t.style,{[o]:i})})},add(t){return e.includes(t)||e.push(t),()=>{let o=e.indexOf(t);if(o>=0)for(let r of e.splice(o,1))r()}},dispose(){for(let t of e.splice(0))t()}};return n}function Se(e,n=()=>[]){let t=!1,o=null,r=Q();return{start(i,s){let a=[e,...n()];t?t=!1:t=o!==null&&o!==i,o=i;for(let l of a)In(l,()=>{t||(i==="in"?(l.dataset.transition="",l.dataset.enter="",l.dataset.closed="",delete l.dataset.leave):i==="out"&&(l.dataset.transition="",l.dataset.leave="",delete l.dataset.enter))},o!==null);r.nextFrame(()=>{for(let l of a)t?i==="in"?(delete l.dataset.enter,delete l.dataset.closed,l.dataset.leave=""):i==="out"&&(delete l.dataset.leave,l.dataset.enter="",l.dataset.closed=""):i==="in"?delete l.dataset.closed:i==="out"&&(l.dataset.closed="");r.requestAnimationFrame(()=>{r.add(kn(e,()=>{if(!(t&&typeof e.getAnimations=="function"&&e.getAnimations({subtree:!0}).length>0)){for(let l of a)delete l.dataset.transition,delete l.dataset.enter,delete l.dataset.closed,delete l.dataset.leave;o=null,s?.()}}))})})},abort(){r.dispose(),t=!1,o=null}}}function In(e,n,t=!1){if(t){n();return}let o=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=o}function kn(e,n){let t=Q();if(!e)return t.dispose;let o=!1;t.add(()=>{o=!0});let r=e.getAnimations?.({subtree:!0}).filter(i=>i instanceof CSSTransition)??[];return r.length===0?(n(),t.dispose):(Promise.allSettled(r.map(i=>i.finished)).then(()=>{o||n()}),t.dispose)}var Ie=Math.min,X=Math.max,_e=Math.round,Ke=Math.floor,_=e=>({x:e,y:e}),Pn={left:"right",right:"left",bottom:"top",top:"bottom"},On={start:"end",end:"start"};function Ct(e,n,t){return X(e,Ie(n,t))}function at(e,n){return typeof e=="function"?e(n):e}function me(e){return e.split("-")[0]}function ut(e){return e.split("-")[1]}function Ht(e){return e==="x"?"y":"x"}function Ft(e){return e==="y"?"height":"width"}function oe(e){return["top","bottom"].includes(me(e))?"y":"x"}function Rt(e){return Ht(oe(e))}function Po(e,n,t){t===void 0&&(t=!1);let o=ut(e),r=Rt(e),i=Ft(r),s=r==="x"?o===(t?"end":"start")?"right":"left":o==="start"?"bottom":"top";return n.reference[i]>n.floating[i]&&(s=$e(s)),[s,$e(s)]}function Oo(e){let n=$e(e);return[lt(e),n,lt(n)]}function lt(e){return e.replace(/start|end/g,n=>On[n])}function Dn(e,n,t){let o=["left","right"],r=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return t?n?r:o:n?o:r;case"left":case"right":return n?i:s;default:return[]}}function Do(e,n,t,o){let r=ut(e),i=Dn(me(e),t==="start",o);return r&&(i=i.map(s=>s+"-"+r),n&&(i=i.concat(i.map(lt)))),i}function $e(e){return e.replace(/left|right|bottom|top/g,n=>Pn[n])}function Mn(e){return{top:0,right:0,bottom:0,left:0,...e}}function Mo(e){return typeof e!="number"?Mn(e):{top:e,right:e,bottom:e,left:e}}function he(e){let{x:n,y:t,width:o,height:r}=e;return{width:o,height:r,top:t,left:n,right:n+o,bottom:t+r,x:n,y:t}}function Co(e,n,t){let{reference:o,floating:r}=e,i=oe(n),s=Rt(n),a=Ft(s),l=me(n),u=i==="y",m=o.x+o.width/2-r.width/2,g=o.y+o.height/2-r.height/2,c=o[a]/2-r[a]/2,b;switch(l){case"top":b={x:m,y:o.y-r.height};break;case"bottom":b={x:m,y:o.y+o.height};break;case"right":b={x:o.x+o.width,y:g};break;case"left":b={x:o.x-r.width,y:g};break;default:b={x:o.x,y:o.y}}switch(ut(n)){case"start":b[s]-=c*(t&&u?-1:1);break;case"end":b[s]+=c*(t&&u?-1:1);break}return b}var Ho=async(e,n,t)=>{let{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:s}=t,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(n)),u=await s.getElementRects({reference:e,floating:n,strategy:r}),{x:m,y:g}=Co(u,o,l),c=o,b={},d=0;for(let p=0;p<a.length;p++){let{name:h,fn:w}=a[p],{x:y,y:v,data:A,reset:E}=await w({x:m,y:g,initialPlacement:o,placement:c,strategy:r,middlewareData:b,rects:u,platform:s,elements:{reference:e,floating:n}});m=y??m,g=v??g,b={...b,[h]:{...b[h],...A}},E&&d<=50&&(d++,typeof E=="object"&&(E.placement&&(c=E.placement),E.rects&&(u=E.rects===!0?await s.getElementRects({reference:e,floating:n,strategy:r}):E.rects),{x:m,y:g}=Co(u,c,l)),p=-1)}return{x:m,y:g,placement:c,strategy:r,middlewareData:b}};async function Nt(e,n){var t;n===void 0&&(n={});let{x:o,y:r,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:m="viewport",elementContext:g="floating",altBoundary:c=!1,padding:b=0}=at(n,e),d=Mo(b),h=a[c?g==="floating"?"reference":"floating":g],w=he(await i.getClippingRect({element:(t=await(i.isElement==null?void 0:i.isElement(h)))==null||t?h:h.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:m,strategy:l})),y=g==="floating"?{x:o,y:r,width:s.floating.width,height:s.floating.height}:s.reference,v=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),A=await(i.isElement==null?void 0:i.isElement(v))?await(i.getScale==null?void 0:i.getScale(v))||{x:1,y:1}:{x:1,y:1},E=he(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:v,strategy:l}):y);return{top:(w.top-E.top+d.top)/A.y,bottom:(E.bottom-w.bottom+d.bottom)/A.y,left:(w.left-E.left+d.left)/A.x,right:(E.right-w.right+d.right)/A.x}}var Fo=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var t,o;let{placement:r,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:u}=n,{mainAxis:m=!0,crossAxis:g=!0,fallbackPlacements:c,fallbackStrategy:b="bestFit",fallbackAxisSideDirection:d="none",flipAlignment:p=!0,...h}=at(e,n);if((t=i.arrow)!=null&&t.alignmentOffset)return{};let w=me(r),y=oe(a),v=me(a)===a,A=await(l.isRTL==null?void 0:l.isRTL(u.floating)),E=c||(v||!p?[$e(a)]:Oo(a)),L=d!=="none";!c&&L&&E.push(...Do(a,p,d,A));let N=[a,...E],le=await Nt(n,h),C=[],Z=((o=i.flip)==null?void 0:o.overflows)||[];if(m&&C.push(le[w]),g){let ae=Po(r,s,A);C.push(le[ae[0]],le[ae[1]])}if(Z=[...Z,{placement:r,overflows:C}],!C.every(ae=>ae<=0)){var co,fo;let ae=(((co=i.flip)==null?void 0:co.index)||0)+1,Lt=N[ae];if(Lt&&(!(g==="alignment"?y!==oe(Lt):!1)||Z.every($=>$.overflows[0]>0&&oe($.placement)===y)))return{data:{index:ae,overflows:Z},reset:{placement:Lt}};let Be=(fo=Z.filter(ue=>ue.overflows[0]<=0).sort((ue,$)=>ue.overflows[1]-$.overflows[1])[0])==null?void 0:fo.placement;if(!Be)switch(b){case"bestFit":{var po;let ue=(po=Z.filter($=>{if(L){let ee=oe($.placement);return ee===y||ee==="y"}return!0}).map($=>[$.placement,$.overflows.filter(ee=>ee>0).reduce((ee,fn)=>ee+fn,0)]).sort(($,ee)=>$[1]-ee[1])[0])==null?void 0:po[0];ue&&(Be=ue);break}case"initialPlacement":Be=a;break}if(r!==Be)return{reset:{placement:Be}}}return{}}}};var Ro=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){let{x:t,y:o,placement:r}=n,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:h=>{let{x:w,y}=h;return{x:w,y}}},...l}=at(e,n),u={x:t,y:o},m=await Nt(n,l),g=oe(me(r)),c=Ht(g),b=u[c],d=u[g];if(i){let h=c==="y"?"top":"left",w=c==="y"?"bottom":"right",y=b+m[h],v=b-m[w];b=Ct(y,b,v)}if(s){let h=g==="y"?"top":"left",w=g==="y"?"bottom":"right",y=d+m[h],v=d-m[w];d=Ct(y,d,v)}let p=a.fn({...n,[c]:b,[g]:d});return{...p,data:{x:p.x-t,y:p.y-o,enabled:{[c]:i,[g]:s}}}}}};function ct(){return typeof window<"u"}function ge(e){return Bo(e)?(e.nodeName||"").toLowerCase():"#document"}function R(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function K(e){var n;return(n=(Bo(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function Bo(e){return ct()?e instanceof Node||e instanceof R(e).Node:!1}function q(e){return ct()?e instanceof Element||e instanceof R(e).Element:!1}function U(e){return ct()?e instanceof HTMLElement||e instanceof R(e).HTMLElement:!1}function No(e){return!ct()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof R(e).ShadowRoot}function Pe(e){let{overflow:n,overflowX:t,overflowY:o,display:r}=V(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!["inline","contents"].includes(r)}function qo(e){return["table","td","th"].includes(ge(e))}function Ue(e){return[":popover-open",":modal"].some(n=>{try{return e.matches(n)}catch{return!1}})}function ft(e){let n=dt(),t=q(e)?V(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>t[o]?t[o]!=="none":!1)||(t.containerType?t.containerType!=="normal":!1)||!n&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!n&&(t.filter?t.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(t.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(t.contain||"").includes(o))}function Vo(e){let n=J(e);for(;U(n)&&!be(n);){if(ft(n))return n;if(Ue(n))return null;n=J(n)}return null}function dt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function be(e){return["html","body","#document"].includes(ge(e))}function V(e){return R(e).getComputedStyle(e)}function je(e){return q(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function J(e){if(ge(e)==="html")return e;let n=e.assignedSlot||e.parentNode||No(e)&&e.host||K(e);return No(n)?n.host:n}function Wo(e){let n=J(e);return be(n)?e.ownerDocument?e.ownerDocument.body:e.body:U(n)&&Pe(n)?n:Wo(n)}function ke(e,n,t){var o;n===void 0&&(n=[]),t===void 0&&(t=!0);let r=Wo(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),s=R(r);if(i){let a=pt(s);return n.concat(s,s.visualViewport||[],Pe(r)?r:[],a&&t?ke(a):[])}return n.concat(r,ke(r,[],t))}function pt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ko(e){let n=V(e),t=parseFloat(n.width)||0,o=parseFloat(n.height)||0,r=U(e),i=r?e.offsetWidth:t,s=r?e.offsetHeight:o,a=_e(t)!==i||_e(o)!==s;return a&&(t=i,o=s),{width:t,height:o,$:a}}function qt(e){return q(e)?e:e.contextElement}function Oe(e){let n=qt(e);if(!U(n))return _(1);let t=n.getBoundingClientRect(),{width:o,height:r,$:i}=Ko(n),s=(i?_e(t.width):t.width)/o,a=(i?_e(t.height):t.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}var Cn=_(0);function Uo(e){let n=R(e);return!dt()||!n.visualViewport?Cn:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function Hn(e,n,t){return n===void 0&&(n=!1),!t||n&&t!==R(e)?!1:n}function ve(e,n,t,o){n===void 0&&(n=!1),t===void 0&&(t=!1);let r=e.getBoundingClientRect(),i=qt(e),s=_(1);n&&(o?q(o)&&(s=Oe(o)):s=Oe(e));let a=Hn(i,t,o)?Uo(i):_(0),l=(r.left+a.x)/s.x,u=(r.top+a.y)/s.y,m=r.width/s.x,g=r.height/s.y;if(i){let c=R(i),b=o&&q(o)?R(o):o,d=c,p=pt(d);for(;p&&o&&b!==d;){let h=Oe(p),w=p.getBoundingClientRect(),y=V(p),v=w.left+(p.clientLeft+parseFloat(y.paddingLeft))*h.x,A=w.top+(p.clientTop+parseFloat(y.paddingTop))*h.y;l*=h.x,u*=h.y,m*=h.x,g*=h.y,l+=v,u+=A,d=R(p),p=pt(d)}}return he({width:m,height:g,x:l,y:u})}function Vt(e,n){let t=je(e).scrollLeft;return n?n.left+t:ve(K(e)).left+t}function jo(e,n,t){t===void 0&&(t=!1);let o=e.getBoundingClientRect(),r=o.left+n.scrollLeft-(t?0:Vt(e,o)),i=o.top+n.scrollTop;return{x:r,y:i}}function Fn(e){let{elements:n,rect:t,offsetParent:o,strategy:r}=e,i=r==="fixed",s=K(o),a=n?Ue(n.floating):!1;if(o===s||a&&i)return t;let l={scrollLeft:0,scrollTop:0},u=_(1),m=_(0),g=U(o);if((g||!g&&!i)&&((ge(o)!=="body"||Pe(s))&&(l=je(o)),U(o))){let b=ve(o);u=Oe(o),m.x=b.x+o.clientLeft,m.y=b.y+o.clientTop}let c=s&&!g&&!i?jo(s,l,!0):_(0);return{width:t.width*u.x,height:t.height*u.y,x:t.x*u.x-l.scrollLeft*u.x+m.x+c.x,y:t.y*u.y-l.scrollTop*u.y+m.y+c.y}}function Rn(e){return Array.from(e.getClientRects())}function Nn(e){let n=K(e),t=je(e),o=e.ownerDocument.body,r=X(n.scrollWidth,n.clientWidth,o.scrollWidth,o.clientWidth),i=X(n.scrollHeight,n.clientHeight,o.scrollHeight,o.clientHeight),s=-t.scrollLeft+Vt(e),a=-t.scrollTop;return V(o).direction==="rtl"&&(s+=X(n.clientWidth,o.clientWidth)-r),{width:r,height:i,x:s,y:a}}function Bn(e,n){let t=R(e),o=K(e),r=t.visualViewport,i=o.clientWidth,s=o.clientHeight,a=0,l=0;if(r){i=r.width,s=r.height;let u=dt();(!u||u&&n==="fixed")&&(a=r.offsetLeft,l=r.offsetTop)}return{width:i,height:s,x:a,y:l}}function qn(e,n){let t=ve(e,!0,n==="fixed"),o=t.top+e.clientTop,r=t.left+e.clientLeft,i=U(e)?Oe(e):_(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,l=r*i.x,u=o*i.y;return{width:s,height:a,x:l,y:u}}function $o(e,n,t){let o;if(n==="viewport")o=Bn(e,t);else if(n==="document")o=Nn(K(e));else if(q(n))o=qn(n,t);else{let r=Uo(e);o={x:n.x-r.x,y:n.y-r.y,width:n.width,height:n.height}}return he(o)}function zo(e,n){let t=J(e);return t===n||!q(t)||be(t)?!1:V(t).position==="fixed"||zo(t,n)}function Vn(e,n){let t=n.get(e);if(t)return t;let o=ke(e,[],!1).filter(a=>q(a)&&ge(a)!=="body"),r=null,i=V(e).position==="fixed",s=i?J(e):e;for(;q(s)&&!be(s);){let a=V(s),l=ft(s);!l&&a.position==="fixed"&&(r=null),(i?!l&&!r:!l&&a.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||Pe(s)&&!l&&zo(e,s))?o=o.filter(m=>m!==s):r=a,s=J(s)}return n.set(e,o),o}function Wn(e){let{element:n,boundary:t,rootBoundary:o,strategy:r}=e,s=[...t==="clippingAncestors"?Ue(n)?[]:Vn(n,this._c):[].concat(t),o],a=s[0],l=s.reduce((u,m)=>{let g=$o(n,m,r);return u.top=X(g.top,u.top),u.right=Ie(g.right,u.right),u.bottom=Ie(g.bottom,u.bottom),u.left=X(g.left,u.left),u},$o(n,a,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function $n(e){let{width:n,height:t}=Ko(e);return{width:n,height:t}}function _n(e,n,t){let o=U(n),r=K(n),i=t==="fixed",s=ve(e,!0,i,n),a={scrollLeft:0,scrollTop:0},l=_(0);function u(){l.x=Vt(r)}if(o||!o&&!i)if((ge(n)!=="body"||Pe(r))&&(a=je(n)),o){let b=ve(n,!0,i,n);l.x=b.x+n.clientLeft,l.y=b.y+n.clientTop}else r&&u();i&&!o&&r&&u();let m=r&&!o&&!i?jo(r,a):_(0),g=s.left+a.scrollLeft-l.x-m.x,c=s.top+a.scrollTop-l.y-m.y;return{x:g,y:c,width:s.width,height:s.height}}function Bt(e){return V(e).position==="static"}function _o(e,n){if(!U(e)||V(e).position==="fixed")return null;if(n)return n(e);let t=e.offsetParent;return K(e)===t&&(t=t.ownerDocument.body),t}function Go(e,n){let t=R(e);if(Ue(e))return t;if(!U(e)){let r=J(e);for(;r&&!be(r);){if(q(r)&&!Bt(r))return r;r=J(r)}return t}let o=_o(e,n);for(;o&&qo(o)&&Bt(o);)o=_o(o,n);return o&&be(o)&&Bt(o)&&!ft(o)?t:o||Vo(e)||t}var Kn=async function(e){let n=this.getOffsetParent||Go,t=this.getDimensions,o=await t(e.floating);return{reference:_n(e.reference,await n(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Un(e){return V(e).direction==="rtl"}var jn={convertOffsetParentRelativeRectToViewportRelativeRect:Fn,getDocumentElement:K,getClippingRect:Wn,getOffsetParent:Go,getElementRects:Kn,getClientRects:Rn,getDimensions:$n,getScale:Oe,isElement:q,isRTL:Un};function Yo(e,n){return e.x===n.x&&e.y===n.y&&e.width===n.width&&e.height===n.height}function zn(e,n){let t=null,o,r=K(e);function i(){var a;clearTimeout(o),(a=t)==null||a.disconnect(),t=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();let u=e.getBoundingClientRect(),{left:m,top:g,width:c,height:b}=u;if(a||n(),!c||!b)return;let d=Ke(g),p=Ke(r.clientWidth-(m+c)),h=Ke(r.clientHeight-(g+b)),w=Ke(m),v={rootMargin:-d+"px "+-p+"px "+-h+"px "+-w+"px",threshold:X(0,Ie(1,l))||1},A=!0;function E(L){let N=L[0].intersectionRatio;if(N!==l){if(!A)return s();N?s(!1,N):o=setTimeout(()=>{s(!1,1e-7)},1e3)}N===1&&!Yo(u,e.getBoundingClientRect())&&s(),A=!1}try{t=new IntersectionObserver(E,{...v,root:r.ownerDocument})}catch{t=new IntersectionObserver(E,v)}t.observe(e)}return s(!0),i}function Qo(e,n,t,o){o===void 0&&(o={});let{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,u=qt(e),m=r||i?[...u?ke(u):[],...ke(n)]:[];m.forEach(w=>{r&&w.addEventListener("scroll",t,{passive:!0}),i&&w.addEventListener("resize",t)});let g=u&&a?zn(u,t):null,c=-1,b=null;s&&(b=new ResizeObserver(w=>{let[y]=w;y&&y.target===u&&b&&(b.unobserve(n),cancelAnimationFrame(c),c=requestAnimationFrame(()=>{var v;(v=b)==null||v.observe(n)})),t()}),u&&!l&&b.observe(u),b.observe(n));let d,p=l?ve(e):null;l&&h();function h(){let w=ve(e);p&&!Yo(p,w)&&t(),p=w,d=requestAnimationFrame(h)}return t(),()=>{var w;m.forEach(y=>{r&&y.removeEventListener("scroll",t),i&&y.removeEventListener("resize",t)}),g?.(),(w=b)==null||w.disconnect(),b=null,l&&cancelAnimationFrame(d)}}var Xo=Ro,Jo=Fo;var Zo=(e,n,t)=>{let o=new Map,r={platform:jn,...t},i={...r.platform,_c:o};return Ho(e,n,{...r,platform:i})};function en(e,n){let t=()=>{};return function(r){if(t(),!r)return;let i=n();if(!i||!e.hasAttribute("anchor"))return;let s=e.getAttribute("anchor"),a=e.getAttribute("anchor-strategy")||"absolute";a!=="absolute"&&a!=="fixed"&&(console.warn(`[createAnchorUpdater] Invalid anchor strategy "${a}" for element:`,e),a="absolute"),t=Qo(i,e,()=>{Zo(i,e,{strategy:a,placement:s.replace(" ","-"),middleware:[Jo(),Xo()]}).then(({x:l,y:u,placement:m})=>{if(!Gn()&&a==="absolute"){let b=null;for(let d=e.parentElement;d;d=d.parentElement){let p=getComputedStyle(d).position;if(p==="relative"||p==="absolute"||p==="fixed"||p==="sticky"){b=d;break}}if(b){let d=b.getBoundingClientRect();l-=d.left+window.scrollX,u-=d.top+window.scrollY}}let g=`${l}px`,c=`${u}px`;switch(m.split("-")[0]){case"top":c=`calc(${u}px - var(--anchor-gap, 0px))`,g=`calc(${l}px + var(--anchor-offset, 0px))`;break;case"right":g=`calc(${l}px + var(--anchor-gap, 0px))`,c=`calc(${u}px + var(--anchor-offset, 0px))`;break;case"bottom":c=`calc(${u}px + var(--anchor-gap, 0px))`,g=`calc(${l}px + var(--anchor-offset, 0px))`;break;case"left":g=`calc(${l}px - var(--anchor-gap, 0px))`,c=`calc(${u}px + var(--anchor-offset, 0px))`;break}Object.assign(e.style,{left:g,top:c,position:a})})})}}function Gn(){return"showPopover"in HTMLElement.prototype&&HTMLElement.prototype.showPopover.toString().includes("[native code]")}function ne(e,n,t){function o(){let r=e.getBoundingClientRect();if(!(r.x!==0||r.y!==0||r.width!==0||r.height!==0)){for(let s of e.children){let a=s.getBoundingClientRect();if(a.x!==0||a.y!==0||a.width!==0||a.height!==0)return}t()}}if(typeof ResizeObserver<"u"){let r=new ResizeObserver(o);r.observe(e),n.addEventListener("abort",()=>r.disconnect())}if(typeof IntersectionObserver<"u"){let r=new IntersectionObserver(o);r.observe(e),n.addEventListener("abort",()=>r.disconnect())}}var ze=!1,Wt=!1;typeof navigator<"u"&&(ze=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),Wt=navigator.userAgent.toLowerCase().includes("firefox"));var $t=!1;function De(e,n,t,o,r,i){Yn(e.ownerDocument);let s=Se(e),a=en(e,t),l=Q();e.setAttribute("popover","");let u=n();u&&(u.setAttribute("type","button"),u.setAttribute("aria-haspopup","true"),u.setAttribute("aria-controls",e.id),u.setAttribute("aria-expanded","false"),u.id&&e.setAttribute("aria-labelledby",u.id)),e.hasAttribute("open")&&queueMicrotask(()=>e.showPopover()),e.addEventListener("beforetoggle",g=>{let c=g;a(c.newState==="open");let b=e.hasAttribute("open");c.newState==="open"&&!b?e.setAttributeNoCallbacks("open",""):c.newState==="closed"&&b&&e.removeAttributeNoCallbacks("open"),c.newState==="open"?(u?.setAttribute("aria-expanded","true"),r?.(),$t=e.getAttribute("popover")===""):(u?.setAttribute("aria-expanded","false"),i?.(),$t=!1),c.oldState==="closed"&&c.newState==="open"?(ze&&(l.dispose(),l=Q()),s.start("in")):c.oldState==="open"&&c.newState==="closed"&&(ze&&l.style(e,"transition-property","none"),s.start("out"))},{signal:o});function m(){e.hasAttribute("open")&&e.hidePopover()}ne(e,o,m),u&&ne(u,o,m),o.addEventListener("abort",()=>s.abort())}var tn=new WeakSet;function Yn(e){if(tn.has(e))return;tn.add(e);let n=null;e.addEventListener("mousedown",()=>{Wt||ze||!$t||(e.body.setAttribute("tabindex","-1"),n&&clearTimeout(n),n=setTimeout(()=>e.body.removeAttribute("tabindex")))},{capture:!0})}function j(e,n,t,o){function r(){let a=e.getBoundingClientRect();o.style.setProperty(n,a.width+"px")}let i=e.ownerDocument,s=new ResizeObserver(r);s.observe(e),i.addEventListener("transitionend",r),t.addEventListener("abort",()=>{s.disconnect(),i.removeEventListener("transitionend",r)})}var Qn=0;function k(e){return`${e}-${Qn++}`}var Xn=200;function re(e,n,t,o){Jn(),e.addEventListener(n,r=>{_t!==null&&Date.now()-_t<Xn||o(r)},{passive:!0,signal:t})}var _t=null,nn=!1;function Jn(){nn||(nn=!0,document.addEventListener("keydown",()=>{_t=Date.now()},{capture:!0}))}var mt=class extends Map{constructor(t){super();this.factory=t}get(t){let o=super.get(t);return o===void 0&&(o=this.factory(t,this),this.set(t,o)),o}};function Ge(e){return Kt(e)&&"tabIndex"in e}function Kt(e){return Zn(e)&&"tagName"in e}function Ut(e){return Kt(e)&&"accessKey"in e}function Zn(e){return typeof e!="object"||e===null?!1:"nodeType"in e}function rn(e){return Kt(e)&&"style"in e}function sn(e){return Ut(e)&&e.nodeName==="INPUT"}var ln=new mt(()=>({referenceCounter:0,d:Q()}));function ht(e){let n=ln.get(e);if(n.referenceCounter++,n.referenceCounter===1){let o=[rr(),or(),tr()];o.forEach(({before:r})=>r({doc:e,d:n.d})),o.forEach(({after:r})=>r({doc:e,d:n.d}))}let t=!1;return()=>{t||(t=!0,n.referenceCounter--,!(n.referenceCounter>0)&&(n.d.dispose(),ln.delete(e)))}}function tr(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")},after(){}}}function or(){let e;return{before({doc:n}){let t=n.documentElement,o=n.defaultView??window;e=Math.max(0,o.innerWidth-t.clientWidth),t.style.setProperty("--el-top-layer-scrollbar-offset","0px")},after({doc:n,d:t}){let o=n.documentElement,r=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,e-r);t.style(o,"paddingRight",`${i}px`),t.add(()=>{o.style.setProperty("--el-top-layer-scrollbar-offset",`-${i}px`)})}}}function nr(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function rr(){return nr()?{before({doc:e,d:n}){function t(o){return!!o.closest("[popover], dialog > *")}n.microTask(()=>{if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let i=Q();i.style(e.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>i.dispose()))}let o=window.scrollY??window.pageYOffset,r=null;n.addEventListener(e,"click",i=>{if(Ge(i.target))try{let s=i.target.closest("a");if(!s)return;let{hash:a}=new URL(s.href),l=e.querySelector(a);Ge(l)&&!t(l)&&(r=l)}catch{}},!0),n.addEventListener(e,"touchstart",i=>{if(Ge(i.target)&&rn(i.target))if(t(i.target)){let s=i.target;for(;s.parentElement&&t(s.parentElement);)s=s.parentElement;n.style(s,"overscrollBehavior","contain")}else n.style(i.target,"touchAction","none")}),n.addEventListener(e,"touchmove",i=>{if(Ge(i.target)){if(sn(i.target))return;if(t(i.target)){let s=i.target;for(;s.parentElement&&s.dataset.tailwindplusPortal!==""&&!(s.scrollHeight>s.clientHeight||s.scrollWidth>s.clientWidth);)s=s.parentElement;s.dataset.tailwindplusPortal===""&&i.preventDefault()}else i.preventDefault()}},{passive:!1}),n.add(()=>{let i=window.scrollY??window.pageYOffset;o!==i&&window.scrollTo(0,o),r&&r.isConnected&&(r.scrollIntoView({block:"nearest"}),r=null)})})},after(){}}:{before(){},after(){}}}function gt(e,n){let t=null;e.addEventListener("toggle",o=>{o.newState==="open"?t||(t=ht(e.ownerDocument)):t&&(t(),t=null)},{signal:n}),n.addEventListener("abort",()=>{t&&(t(),t=null)})}var D,Ye,jt=class extends x{constructor(){super(...arguments);I(this,D,[]);I(this,Ye,null)}mount(t){let o=this.getInput(),r=this.getButton(),i=this.getOptions();o.id||(o.id=k("autocomplete-input")),r&&(r.id||(r.id=k("autocomplete-button"))),i.id||(i.id=k("autocomplete-listbox")),De(i,()=>this.getButton(),()=>this.getInput(),t,()=>this.onBeforeOpen(),()=>this.onBeforeClose()),gt(i,t),o.setAttribute("role","combobox"),o.setAttribute("aria-autocomplete","list"),o.setAttribute("aria-expanded","false"),o.setAttribute("aria-controls",i.id),o.setAttribute("aria-activedescendant",""),o.setAttribute("autocomplete","off"),r&&(r.setAttribute("type","button"),r.setAttribute("tabindex","-1"),r.setAttribute("aria-expanded","false"),r.setAttribute("aria-haspopup","listbox"),r.setAttribute("popovertarget",i.id)),i.setAttribute("role","listbox"),i.setAttribute("popover","manual");let s={passive:!0,signal:t},a=this;function l(){for(let c of i.getItems())c.getAttribute("role")!=="option"&&(c.id||(c.id=k("option")),c.setAttribute("role","option"),c.setAttribute("aria-selected","false"),c.setAttribute("tabIndex","-1"),c.addEventListener("click",()=>a.selectOption(c),s),re(c,"mouseover",t,()=>a.setActiveItem(c,!1)),re(c,"mouseout",t,()=>a.clearActiveItem()))}l();let u=new MutationObserver(l);u.observe(this,{attributes:!1,childList:!0,subtree:!0}),r&&j(r,"--button-width",t,this),j(o,"--input-width",t,this),o.addEventListener("input",()=>{o.matches(":disabled")||(this.filterOptions(),f(this,D).length>0?i.hasAttribute("open")||i.showPopover():i.hidePopover())},{signal:t});let m=()=>{o.matches(":disabled")||(o.focus(),i.hasAttribute("open")?i.hidePopover():(this.filterOptions(),f(this,D).length>0&&i.showPopover()))};o.addEventListener("pointerdown",m,{signal:t}),r&&(r.addEventListener("pointerdown",c=>{c.preventDefault(),m()},{signal:t}),r.addEventListener("click",c=>{c.preventDefault(),c.stopImmediatePropagation()},{signal:t})),o.addEventListener("blur",({relatedTarget:c})=>{c&&this.contains(c)||i.hidePopover()},{signal:t}),o.addEventListener("keydown",c=>{if(!o.matches(":disabled"))switch(c.key){case"ArrowDown":{c.preventDefault(),i.hasAttribute("open")||(f(this,D).length===0&&this.filterOptions(),f(this,D).length>0&&i.showPopover()),this.goToItem(3);break}case"ArrowUp":{c.preventDefault(),i.hasAttribute("open")||(f(this,D).length===0&&this.filterOptions(),f(this,D).length>0&&i.showPopover()),this.goToItem(2);break}case"Home":case"PageUp":return i.hasAttribute("open")?(c.preventDefault(),c.stopPropagation(),this.goToItem(0)):void 0;case"End":case"PageDown":return i.hasAttribute("open")?(c.preventDefault(),c.stopPropagation(),this.goToItem(1)):void 0;case"Enter":{let b=this.getActiveItem();b&&(c.preventDefault(),this.selectOption(b)),i.hasAttribute("open")&&(c.preventDefault(),i.hidePopover());break}case"Escape":{if(!i.hasAttribute("open"))return;c.preventDefault(),i.hidePopover();break}case"Tab":{i.hidePopover();break}}},{signal:t});let g=Array.from(i.querySelectorAll("el-option[disabled]"));for(let c of g)c.setAttribute("aria-disabled","true"),c.setAttribute("aria-selected","false");i.addEventListener("click",c=>c.stopPropagation()),t.addEventListener("abort",()=>{u.disconnect()})}getInput(){let t=this.querySelector("input");if(!t)throw new Error("`<el-autocomplete>` must contain an input element.");return t}getButton(){return this.querySelector("button")}getOptions(){let t=this.querySelector("el-options");if(!t)throw new Error("`<el-autocomplete>` must contain a `<el-options>` element.");return t}filterOptions(){let t=this.getInput().value.toLowerCase();if(f(this,Ye)!==t){this.clearActiveItem(),S(this,Ye,t),S(this,D,[]);for(let o of this.getOptions().getItems()){let r=o.getAttribute("value")?.toLowerCase()||"";t===""||r.includes(t)?(f(this,D).push(o),o.removeAttribute("hidden"),o.removeAttribute("aria-hidden")):(o.setAttribute("hidden",""),o.setAttribute("aria-hidden","true"))}}}getActiveItem(){let o=this.getInput().getAttribute("aria-activedescendant");return o?document.getElementById(o):null}goToItem(t){if(f(this,D).length===0)return;let o=this.getActiveItem(),r=o?f(this,D).indexOf(o):null;switch(r===-1&&(r=null),t){case 0:{this.setActiveItem(f(this,D)[0]);break}case 1:{this.setActiveItem(f(this,D)[f(this,D).length-1]);break}case 2:{if(r===null){this.goToItem(1);break}this.setActiveItem(f(this,D)[Math.max(0,r-1)]);break}case 3:{if(r===null){this.goToItem(0);break}this.setActiveItem(f(this,D)[Math.min(f(this,D).length-1,r+1)]);break}case 4:break}}setActiveItem(t,o=!0){let r=this.getInput(),i=this.getActiveItem();i!==null&&i.setAttribute("aria-selected","false"),t.setAttribute("aria-selected","true"),r.setAttribute("aria-activedescendant",t.id),o&&t.scrollIntoView({block:"nearest"})}clearActiveItem(){let t=this.getInput(),o=this.getActiveItem();o!==null&&o.setAttribute("aria-selected","false"),t.setAttribute("aria-activedescendant","")}selectOption(t){let o=this.getInput(),r=t.getAttribute("value");r&&(o.value=r,o.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),o.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0})),this.getOptions().hidePopover())}onBeforeOpen(){let t=this.getInput(),o=this.getButton();t.setAttribute("aria-expanded","true"),o?.setAttribute("aria-expanded","true")}onBeforeClose(){let t=this.getInput(),o=this.getButton();t.setAttribute("aria-expanded","false"),o?.setAttribute("aria-expanded","false"),this.clearActiveItem()}};D=new WeakMap,Ye=new WeakMap;T("el-autocomplete",jt);var ie=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),gi=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var bi=["textarea","input"].join(",");var an=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function un(e){let n=e.innerText??"",t=e.cloneNode(!0);if(!Ut(t))return n;let o=!1;for(let i of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))i.remove(),o=!0;let r=o?t.innerText??"":n;return an.test(r)&&(r=r.replace(an,"")),r}function cn(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let t=e.getAttribute("aria-labelledby");if(t){let o=t.split(" ").map(r=>{let i=document.getElementById(r);if(i){let s=i.getAttribute("aria-label");return typeof s=="string"?s.trim():un(i).trim()}return null}).filter(Boolean);if(o.length>0)return o.join(", ")}return un(e).trim()}var M,Qe,Xe,W,bt,Me,zt=class extends x{constructor(){super(...arguments);I(this,W);I(this,M,[]);I(this,Qe,null);I(this,Xe,({query:t,content:o})=>o.toLocaleLowerCase().includes(t.toLocaleLowerCase().trim()))}mount(t){let o=this.getInput(),r=this.getItems();o.id||(o.id=k("command-input")),r.id||(r.id=k("command-items")),o.setAttribute("role","combobox"),o.setAttribute("aria-autocomplete","list"),o.setAttribute("autocomplete","off"),o.setAttribute("aria-controls",r.id),r.setAttribute("role","listbox");let i=this;function s(){var l;for(let u of r.getItems())u.getAttribute("role")!=="option"&&(u.id||(u.id=k("item")),u.setAttribute("role","option"),u.setAttribute("tabIndex","-1"),u.setAttribute("aria-selected","false"),u.hasAttribute("disabled")&&u.setAttribute("aria-disabled","true"),re(u,"mouseover",t,()=>{var m;return F(m=i,W,Me).call(m,u,!1)}));F(l=i,W,bt).call(l,!0)}s();let a=new MutationObserver(s);a.observe(this,{attributes:!1,childList:!0,subtree:!0}),j(o,"--input-width",t,this),o.addEventListener("input",()=>F(this,W,bt).call(this),{signal:t}),o.addEventListener("keydown",l=>{switch(l.key){case"ArrowDown":{l.preventDefault(),this.goToItem(3);break}case"ArrowUp":{l.preventDefault(),this.goToItem(2);break}case"Home":case"PageUp":return l.preventDefault(),l.stopPropagation(),this.goToItem(0);case"End":case"PageDown":return l.preventDefault(),l.stopPropagation(),this.goToItem(1);case"Enter":{let u=this.getActiveItem();u&&(l.preventDefault(),u.click());break}case"Tab":break}},{signal:t}),t.addEventListener("abort",()=>{a.disconnect()})}getInput(){let t=this.querySelector("input");if(!t)throw new Error("`<el-command-palette>` must contain an input element.");return t}getItems(){let t=this.querySelector("el-command-list");if(!t)throw new Error("`<el-command-palette>` must contain a `<el-command-list>` element.");return t}getGroups(){return this.getItems().querySelectorAll("el-command-group")}getSuggestions(){return this.querySelector("el-defaults")}getActiveItem(){let o=this.getInput().getAttribute("aria-activedescendant");return o?document.getElementById(o):null}goToItem(t){if(f(this,M).length===0)return;let o=this.getActiveItem(),r=o?f(this,M).indexOf(o):null;switch(r===-1&&(r=null),t){case 0:{F(this,W,Me).call(this,f(this,M)[0]);break}case 1:{F(this,W,Me).call(this,f(this,M)[f(this,M).length-1]);break}case 2:{if(r===null){this.goToItem(1);break}F(this,W,Me).call(this,f(this,M)[Math.max(0,r-1)]);break}case 3:{if(r===null){this.goToItem(0);break}F(this,W,Me).call(this,f(this,M)[Math.min(f(this,M).length-1,r+1)]);break}case 4:break}}clearActiveItem(){let t=this.getInput(),o=this.getActiveItem();if(o!==null){o.setAttribute("aria-selected","false");let r=this.querySelector(`el-command-preview[for="${o.id}"]`);r&&r.setAttribute("hidden","")}t.removeAttribute("aria-activedescendant"),this.dispatchEvent(new CustomEvent("change",{detail:{relatedTarget:null},bubbles:!1,cancelable:!1}))}reset(){let t=this.getInput();t.value="",t.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),t.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0})),F(this,W,bt).call(this,!0),this.clearActiveItem()}setFilterCallback(t){S(this,Xe,t)}};M=new WeakMap,Qe=new WeakMap,Xe=new WeakMap,W=new WeakSet,bt=function(t=!1){let o=this.getItems(),r=this.getInput().value??"";S(this,M,[]);for(let a of o.getItems()){if(a.closest("el-defaults"))continue;let l=cn(a)??"";r===""||!f(this,Xe).call(this,{query:r,node:a,content:l})?(a.setAttribute("hidden",""),a.setAttribute("aria-hidden","true")):(f(this,M).push(a),a.removeAttribute("hidden"),a.removeAttribute("aria-hidden"))}for(let a of this.getGroups())a.getItems().some(u=>!u.hasAttribute("hidden"))?a.removeAttribute("hidden"):a.setAttribute("hidden","");let i=this.getSuggestions();i&&(r===""?(i.removeAttribute("hidden"),S(this,M,i.getItems())):i.setAttribute("hidden",""));let s=this.querySelector("el-no-results");s&&(r===""||f(this,M).length>0?s.setAttribute("hidden",""):s.removeAttribute("hidden")),f(this,M).length===0?o.setAttribute("hidden",""):o.removeAttribute("hidden"),!(t&&r==="")&&(f(this,M).length===0?this.clearActiveItem():f(this,Qe)!==r&&this.goToItem(0),S(this,Qe,r))},Me=function(t,o=!0){let r=this.getInput(),i=this.getActiveItem();if(t===i)return;if(i!==null){i.setAttribute("aria-selected","false");let a=this.querySelector(`el-command-preview[for="${i.id}"]`);a&&a.setAttribute("hidden","")}t.setAttribute("aria-selected","true"),r.setAttribute("aria-activedescendant",t.id);let s=this.querySelector(`el-command-preview[for="${t.id}"]`);s&&s.removeAttribute("hidden"),o&&t.scrollIntoView({block:"nearest"}),this.dispatchEvent(new CustomEvent("change",{detail:{relatedTarget:t},bubbles:!1,cancelable:!1}))};var Gt=class extends x{getItems(){return Array.from(this.querySelectorAll(`${ie},[role="option"]`))}},Yt=class extends x{getItems(){return Array.from(this.querySelectorAll(`${ie},[role="option"]`))}},Qt=class extends x{},Xt=class extends x{},Jt=class extends x{getItems(){return Array.from(this.querySelectorAll(`${ie},[role="option"]`))}};T("el-command-palette",zt);T("el-command-list",Gt);T("el-defaults",Yt);T("el-no-results",Qt);T("el-command-group",Jt);T("el-command-preview",Xt);var z=[];it(()=>{function e(n){if(n.target===document.body||z[0]===n.target)return;let t=n.target;t&&"closest"in t&&(t=t.closest(ie),z.unshift(t??n.target),z=z.filter(o=>o!=null&&o.isConnected),z.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("pointerdown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("pointerdown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var Ce=null;typeof globalThis.window<"u"&&(Ce=HTMLDialogElement.prototype.close,Object.defineProperties(HTMLDialogElement.prototype,{close:{value(){let e=this.closest("el-dialog");if(!(e instanceof He))return Ce?.apply(this,arguments);let n=e.beforeClose();if(n===!0)return Ce?.apply(this,arguments);n!==!1&&n.then(t=>t?Ce?.apply(this,arguments):null).catch(console.error)}}}),document.addEventListener("command",e=>{let n=e.target;if(!(n instanceof HTMLDialogElement)||!("command"in e)||e.command!=="close")return;let t=n.closest("el-dialog");if(!(t instanceof He))return;let o=t.beforeClose();o!==!0&&(e.stopImmediatePropagation(),e.preventDefault(),o!==!1&&o.then(r=>r?Ce?.apply(n):null).catch(console.error))},!0));var G,se,Fe,we,Je,Zt,He=class extends x{constructor(){super(...arguments);I(this,Je);I(this,G,null);I(this,se,null);I(this,Fe,!0);I(this,we,Se(this,()=>Array.from(this.querySelectorAll("el-dialog-panel,el-dialog-backdrop"))))}mount(t){let o=this.getNativeDialog();o.style.setProperty("right","var(--el-top-layer-scrollbar-offset, 0px)");let r=this.hasAttribute("open");for(let a of F(this,Je,Zt).call(this))a.setAttribute("aria-expanded",r.toString());sr(o,t,a=>{o.close(),a.preventDefault()});let i=this.querySelector("el-dialog-panel");ne(i??o,t,()=>{this.hasAttribute("open")&&o.close()});let s=null;o.addEventListener("beforetoggle",a=>{let l=a;l.newState==="open"&&l.oldState==="closed"&&this.beforeOpen();let u=this.hasAttribute("open");if(l.newState==="open"&&!u?(this.dispatchEvent(new CustomEvent("open",{bubbles:!1,cancelable:!1})),this.setAttribute("open","")):l.newState==="closed"&&u&&(this.dispatchEvent(new CustomEvent("close",{bubbles:!1,cancelable:!1})),this.removeAttribute("open")),l.newState==="open"&&l.oldState==="closed")z.length>0&&!s&&(s=z[0]);else if(l.newState==="closed"&&l.oldState==="open"){let m=f(this,Fe);setTimeout(()=>{if(!m){s&&s===document.activeElement&&s.isConnected&&"blur"in s&&typeof s.blur=="function"&&s.blur();return}s&&s!==document.activeElement&&s.isConnected&&Le(s),s=null})}},{signal:t}),t.addEventListener("abort",()=>f(this,we).abort()),this.hasAttribute("open")&&o.showModal()}onAttributeChange(t,o,r){switch(t){case"open":{let i=this.getNativeDialog();for(let s of F(this,Je,Zt).call(this))s.setAttribute("aria-expanded",r!==null?"true":"false");r===null?i.close():i.showModal();break}}}getNativeDialog(){let t=this.querySelector("dialog");if(!t)throw new Error("[ElDialog] No `<dialog>` element found");return t}beforeOpen(){S(this,Fe,!0),f(this,G)&&(f(this,G).abort(),S(this,G,null)),f(this,se)||S(this,se,ht(this.ownerDocument)),f(this,we)&&f(this,we).start("in")}beforeClose(){if(f(this,se)&&(f(this,se).call(this),S(this,se,null)),f(this,G))return!1;S(this,G,new AbortController);let t=f(this,G).signal;return new Promise(o=>{f(this,we)?.start("out",()=>{t.aborted||(S(this,G,null),requestAnimationFrame(()=>{let r=this.getNativeDialog(),i=r.style.cssText;r.style.cssText=i+"transition-duration: 0 !important;",Ce?.apply(r),requestAnimationFrame(()=>{r.style.cssText=i})}),o(!0))})})}show(){this.getNativeDialog().showModal()}hide({restoreFocus:t=!0}={}){S(this,Fe,t),this.getNativeDialog().close()}};G=new WeakMap,se=new WeakMap,Fe=new WeakMap,we=new WeakMap,Je=new WeakSet,Zt=function(){return document.querySelectorAll(`[commandfor="${this.getNativeDialog().id}"]`)},H(He,"observedAttributes",["open"]);var eo=class extends x{mount(n){ir(this,n,()=>{let t=this.getDialog().getNativeDialog();t.hasAttribute("open")&&t.close()})}getDialog(){let n=this.closest("el-dialog");if(!n)throw new Error("[ElDialogPanel] No `<el-dialog>` parent found");return n}},to=class extends x{mount(){this.setAttribute("inert","")}};T("el-dialog",He);T("el-dialog-panel",eo);T("el-dialog-backdrop",to);function ir(e,n,t){document.addEventListener("click",o=>{if(o.target===e){let{clientX:i,clientY:s}=o,a=e.getBoundingClientRect();if(i>=a.left&&i<=a.right&&s>=a.top&&s<=a.bottom)return;t(o);return}let r=e.closest("dialog");if(r&&r.contains(o.target)&&!e.contains(o.target)){t(o);return}if(o.target===o.target.ownerDocument.documentElement){t(o);return}},{signal:n,capture:!0})}function sr(e,n,t){e.addEventListener("keydown",o=>{o.key==="Escape"&&(o.defaultPrevented||t(o))},{signal:n})}var Re,Ze,oo,vt=class extends x{constructor(){super(...arguments);I(this,Ze);I(this,Re,Se(this))}mount(t){this.id||(this.id=k("disclosure")),this.hasAttribute("hidden")?this.removeAttributeNoCallbacks("open"):this.setAttributeNoCallbacks("open","");let o=()=>{this.hasAttribute("open")&&this.hide()},r=!this.hasAttribute("hidden");for(let i of F(this,Ze,oo).call(this))ne(i,t,o),i.setAttribute("aria-expanded",r.toString()),i.setAttribute("aria-controls",this.id);this.addEventListener("command",i=>{if(i.target instanceof HTMLElement&&"command"in i)switch(i.command){case"--show":{this.show(),i.preventDefault();break}case"--hide":{this.hide(),i.preventDefault();break}case"--toggle":{this.toggle(),i.preventDefault();break}}},{signal:t}),ne(this,t,o),t.addEventListener("abort",()=>f(this,Re).abort())}onAttributeChange(t,o,r){switch(t){case"hidden":{r===null?this.setAttributeNoCallbacks("open",""):this.removeAttributeNoCallbacks("open");for(let i of F(this,Ze,oo).call(this))i.setAttribute("aria-expanded",r===null?"true":"false");r===null?f(this,Re).start("in"):f(this,Re).start("out");break}case"open":{r===null?this.hide():this.show();break}}}show(){this.removeAttribute("hidden")}hide(){this.setAttribute("hidden","")}toggle(){this.hasAttribute("hidden")?this.show():this.hide()}};Re=new WeakMap,Ze=new WeakSet,oo=function(){return document.querySelectorAll(`[commandfor="${this.id}"]`)},H(vt,"observedAttributes",["hidden","open"]);T("el-disclosure",vt);function wt(e,n,t,o,r){let i=null;for(let a of n)a.addEventListener("pointerdown",l=>{l.button===0&&e.classList.contains(":popover-open")&&(i=Date.now()+100)},{signal:o,capture:!0});e.ownerDocument.addEventListener("focusin",a=>{if(!t.hasAttribute("open"))return;let l=a.target,u=a.relatedTarget;l!==null&&(i&&Date.now()<i||e.contains(l)||n.some(m=>m.contains(l))||r(u))},{signal:o})}var lr=200;function Et(e,n,t){let o=null,r="",i=null,s=null;e.id||(e.id=k(n.role));let a=n.getButton();a.id||(a.id=k(`${n.role}-button`)),De(e,()=>n.getButton(),()=>n.getButton(),t,()=>n.onBeforeOpen(),()=>{n.onBeforeClose(),d(),r="",i&&(clearTimeout(i),i=null)}),gt(e,t),e.setAttribute("popover","manual"),e.setAttribute("role",n.role),a.setAttribute("popovertarget",e.id),a.setAttribute("aria-haspopup",n.role),e.addEventListener("click",v=>v.stopPropagation());function l(){let v=n.getItems(),A={passive:!0,signal:t},E=n.role==="menu"?"menuitem":"option";for(let L of v)L.getAttribute("role")!==E&&(L.id||(L.id=k("item")),L.setAttribute("role",E),L.setAttribute("tabIndex","-1"),L.addEventListener("click",()=>n.onItemClick(L),A),re(L,"mouseover",t,()=>b(L,!1)),re(L,"mouseout",t,()=>d()))}l();let u=new MutationObserver(l);u.observe(e,{attributes:!1,childList:!0,subtree:!0}),wt(e,[a],e,t,v=>{v===null&&(s=Date.now()+100),e.hidePopover()});let m=null,g=!1;a.addEventListener("pointerdown",v=>{if(v.button===0&&!a.matches(":disabled")){if(v.pointerType==="touch"){g=!0;return}e.togglePopover(),m=Date.now()}},{signal:t}),document.addEventListener("pointerup",v=>{if(v.button!==0||a.matches(":disabled")||!e.hasAttribute("open"))return;if(Date.now()-(m??0)>lr){let E=v.composedPath();if(E.includes(e)){if(m!==null){let L=p();L&&L.click()}return}for(let L of E){if(!(L instanceof Element))continue;if((L.getAttribute("commandfor")||L.getAttribute("popovertarget"))===e.id)return}e.hidePopover()}m=null},{signal:t,capture:!0}),a.addEventListener("click",v=>{if(g){g=!1;return}v.preventDefault(),v.stopPropagation()},{signal:t});let c=null;e.addEventListener("beforetoggle",v=>{let A=v;A.newState==="open"&&A.oldState==="closed"&&z.length>0&&!c&&(c=z[0])},{signal:t}),e.addEventListener("toggle",v=>{let A=v;A.newState==="closed"&&A.oldState==="open"&&setTimeout(()=>{!e.contains(document.activeElement)&&document.activeElement!==document.body||s&&Date.now()<s||(c&&c!==document.activeElement&&c.isConnected&&Le(c),c=null)})},{signal:t}),t.addEventListener("abort",()=>{i&&(clearTimeout(i),i=null),u.disconnect()});function b(v,A=!0){let E=p();E!==null&&E.setAttribute("tabIndex","-1"),e.removeAttribute("tabIndex"),v.setAttribute("tabIndex","0"),v.focus({preventScroll:!0}),o=v,A&&v.scrollIntoView({block:"nearest"})}function d(){let v=p();v!==null&&v.setAttribute("tabIndex","-1"),o=null,e.hasAttribute("open")&&(e.setAttribute("tabIndex","0"),e.focus())}function p(){return o}function h(v,A=!1){if(v==="")return null;let E=n.getItems(),L=v.toLowerCase(),N=p(),le=N?E.indexOf(N):-1;if(!A&&N&&le!==-1&&(N.textContent?.trim().toLowerCase()||"").startsWith(L))return N;for(let C=le+1;C<E.length;C++)if((E[C].textContent?.trim().toLowerCase()||"").startsWith(L))return E[C];for(let C=0;C<=le;C++)if((E[C].textContent?.trim().toLowerCase()||"").startsWith(L))return E[C];return null}function w(v){let A=r==="";i&&(clearTimeout(i),i=null),r+=v.toLowerCase();let E=h(r,A);E&&b(E,!0),i=setTimeout(()=>{r="",i=null},350)}function y(){return r!==""}return{ignoreNextFocusRestoration:()=>s=Date.now()+100,setActiveItem:b,clearActiveItem:d,getActiveItem:p,findItemBySearchQuery:h,handleSearchKey:w,hasActiveSearchQuery:y}}var Ee,Ne,P,yt=class extends x{constructor(){super(...arguments);I(this,Ee,this.attachInternals());I(this,Ne,"");I(this,P,null)}mount(t){let o=this.getOptions();this.value=this.getAttribute("value")??this.value??"";let r=this.getButton();r.id||(r.id=k("select-button")),j(r,"--button-width",t,this),r.addEventListener("keydown",s=>{if(!r.matches(":disabled"))switch(s.key){case"ArrowUp":{o.showPopover(),this.goToItem(6),s.preventDefault();break}case"ArrowDown":{o.showPopover(),this.goToItem(5),s.preventDefault();break}case"Enter":{s.preventDefault(),f(this,Ee).form&&f(this,Ee).form.requestSubmit();break}case" ":{if(o.hasAttribute("open")&&f(this,P)&&f(this,P).hasActiveSearchQuery()){s.preventDefault(),s.stopPropagation(),f(this,P).handleSearchKey(s.key);break}s.preventDefault(),o.hasAttribute("open")?o.hidePopover():(o.showPopover(),this.goToItem(5));break}default:{o.hasAttribute("open")&&s.key.length===1&&!s.ctrlKey&&!s.altKey&&!s.metaKey&&(s.preventDefault(),s.stopPropagation(),this.handleSearchKey(s.key));break}}},{signal:t});for(let s of f(this,Ee).labels)s.setAttribute("for",r.id);S(this,P,Et(o,{role:"listbox",getItems:()=>this.getItems(),onItemClick:s=>this.setSelectedOption(s),getButton:()=>this.getButton(),onBeforeOpen:()=>this.onBeforeOpen(),onBeforeClose:()=>this.onBeforeClose()},t)),o.addEventListener("keydown",s=>{switch(s.key){case"ArrowDown":return s.preventDefault(),s.stopPropagation(),this.goToItem(3);case"ArrowUp":return s.preventDefault(),s.stopPropagation(),this.goToItem(2);case"Home":case"PageUp":return s.preventDefault(),s.stopPropagation(),this.goToItem(0);case"End":case"PageDown":return s.preventDefault(),s.stopPropagation(),this.goToItem(1);case" ":if(f(this,P)&&f(this,P).hasActiveSearchQuery()){s.preventDefault(),s.stopPropagation(),f(this,P).handleSearchKey(s.key);return}case"Enter":{s.preventDefault(),s.stopPropagation();let a=this.getActiveItem();a?a.click():o.hidePopover();return}case"Tab":{f(this,P)&&f(this,P).ignoreNextFocusRestoration();break}case"Escape":{s.preventDefault(),s.stopPropagation(),o.hidePopover(),r.focus();break}default:{s.key.length===1&&!s.ctrlKey&&!s.altKey&&!s.metaKey&&(s.preventDefault(),s.stopPropagation(),f(this,P)&&f(this,P).handleSearchKey(s.key));break}}},{signal:t}),o.addEventListener("toggle",s=>{s.newState==="open"&&this.onOpen()},{signal:t});let i=Array.from(o.querySelectorAll("el-option[disabled]"));for(let s of i)s.setAttribute("aria-disabled","true"),s.setAttribute("aria-selected","false")}onAttributeChange(t,o,r){switch(t){case"value":{r!==null&&(this.value=r);break}}}getButton(){let t=this.querySelector("button");if(!t)throw new Error("`<el-select>` must contain a button element.");return t}getOptions(){let t=this.querySelector("el-options");if(!t)throw new Error("`<el-select>` must contain a `<el-options>` element.");return t}setSelectedOption(t){this.value=t.getAttribute("value"),this.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),this.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0})),this.getOptions().hidePopover()}getOptionByName(t){return this.getOptions().getOptionByName(t)}getItems(){return this.getOptions().getItems()}getActiveItem(){return f(this,P)?.getActiveItem()}getSelectedOption(){return this.getOptionByName(f(this,Ne))}goToItem(t=4){let o=this.getItems();if(o.length===0)return;let r=this.getActiveItem(),i=r?o.indexOf(r):null;switch(i===-1&&(i=null),t){case 0:{this.setActiveItem(o[0]);break}case 1:{this.setActiveItem(o[o.length-1]);break}case 2:{if(i===null){this.goToItem(6);break}this.setActiveItem(o[Math.max(0,i-1)]);break}case 3:{if(i===null){this.goToItem(5);break}this.setActiveItem(o[Math.min(o.length-1,i+1)]);break}case 4:break;case 5:case 6:{let s=this.getSelectedOption();s?this.setActiveItem(s):this.goToItem(t===5?0:1);break}}}setActiveItem(t){f(this,P)&&f(this,P).setActiveItem(t)}clearActiveItem(){f(this,P)&&f(this,P).clearActiveItem()}onBeforeOpen(){let t=this.getButton(),o=t.dataset.originalTabIndex;o&&(t.dataset.originalTabIndex=o),t.setAttribute("tabIndex","-1")}onOpen(){this.getActiveItem()===null&&this.goToItem(5)}onBeforeClose(){let t=this.getButton(),o=t.dataset.originalTabIndex;delete t.dataset.originalTabIndex,o!==void 0?t.setAttribute("tabIndex",o):t.removeAttribute("tabIndex");let r=this.getActiveItem();r!==null&&r.setAttribute("tabIndex","-1")}handleSearchKey(t){f(this,P)&&f(this,P).handleSearchKey(t)}set value(t){S(this,Ne,t),f(this,Ee).setFormValue(t);let o=this.getSelectedOption();if(o){for(let r of this.getItems())r.setAttribute("aria-selected","false");o.setAttribute("aria-selected","true");try{this.querySelectorAll("el-selectedcontent").forEach(r=>r.update())}catch{}}}get value(){return f(this,Ne)}};Ee=new WeakMap,Ne=new WeakMap,P=new WeakMap,H(yt,"formAssociated",!0);var no=class extends x{mount(){this.update()}update(){let t=this.getSelect().getSelectedOption();if(!t)return;let o=document.createDocumentFragment();for(let r of t.childNodes)o.append(r.cloneNode(!0));this.replaceChildren(o)}getSelect(){let n=this.closest("el-select");if(!n)throw new Error("`<el-selectedcontent>` must be inside of a `<el-select>` element.");return n}};T("el-select",yt);T("el-selectedcontent",no);var ro=class extends x{getButton(){let n=this.querySelector("button");if(!n)throw new Error("[ElDropdown] No `<button>` element found");return n}mount(n){let t=this.getButton();t.id||(t.id=k("dropdown-button")),j(t,"--button-width",n,this);let o=this.querySelectorAll("label");for(let r of o)r.setAttribute("for",t.id)}},O,At=class extends x{constructor(){super(...arguments);I(this,O,null)}mount(t){S(this,O,Et(this,{role:"menu",getItems:()=>this.getItems(),onItemClick:()=>this.hidePopover(),getButton:()=>this.getDropdown().getButton(),onBeforeOpen:()=>this.onBeforeOpen(),onBeforeClose:()=>this.onBeforeClose()},t));let r=this.getDropdown().getButton();r.addEventListener("keydown",i=>{if(!r.disabled)switch(i.key){case"ArrowDown":{this.showPopover(),this.goToItem(0),i.preventDefault();break}case"ArrowUp":{this.showPopover(),this.goToItem(1),i.preventDefault();break}case" ":if(this.hasAttribute("open")&&f(this,O)&&f(this,O).hasActiveSearchQuery()){i.preventDefault(),i.stopPropagation(),f(this,O).handleSearchKey(i.key);break}case"Enter":{i.preventDefault(),this.hasAttribute("open")?this.hidePopover():(this.showPopover(),this.goToItem(0));break}default:{this.hasAttribute("open")&&i.key.length===1&&!i.ctrlKey&&!i.altKey&&!i.metaKey&&(i.preventDefault(),i.stopPropagation(),f(this,O)&&f(this,O).handleSearchKey(i.key));break}}},{signal:t}),this.addEventListener("keydown",i=>{switch(i.key){case"ArrowDown":return i.preventDefault(),i.stopPropagation(),this.goToItem(3);case"ArrowUp":return i.preventDefault(),i.stopPropagation(),this.goToItem(2);case"Home":case"PageUp":return i.preventDefault(),i.stopPropagation(),this.goToItem(0);case"End":case"PageDown":return i.preventDefault(),i.stopPropagation(),this.goToItem(1);case" ":if(f(this,O)&&f(this,O).hasActiveSearchQuery()){i.preventDefault(),i.stopPropagation(),f(this,O).handleSearchKey(i.key);return}case"Enter":{i.preventDefault(),i.stopPropagation();let s=this.getActiveItem();s?s.click():this.hidePopover();return}case"Tab":{f(this,O)&&f(this,O).ignoreNextFocusRestoration();break}case"Escape":{i.preventDefault(),i.stopPropagation(),this.hidePopover(),r.focus();break}default:{i.key.length===1&&!i.ctrlKey&&!i.altKey&&!i.metaKey&&(i.preventDefault(),i.stopPropagation(),f(this,O)&&f(this,O).handleSearchKey(i.key));break}}},{signal:t})}onBeforeOpen(){let o=this.getDropdown().getButton(),r=o.dataset.originalTabIndex;r&&(o.dataset.originalTabIndex=r),o.setAttribute("tabIndex","-1"),this.getActiveItem()===null&&(this.setAttribute("tabIndex","0"),setTimeout(()=>this.focus({preventScroll:!0})))}onBeforeClose(){let o=this.getDropdown().getButton(),r=o.dataset.originalTabIndex;delete o.dataset.originalTabIndex,r!==void 0?o.setAttribute("tabIndex",r):o.removeAttribute("tabIndex");let i=this.getActiveItem();i!==null&&i.setAttribute("tabIndex","-1")}goToItem(t=4){let o=this.getItems();if(o.length===0)return;let r=this.getActiveItem(),i=r?o.indexOf(r):null;switch(i===-1&&(i=null),t){case 0:{this.setActiveItem(o[0]);break}case 1:{this.setActiveItem(o[o.length-1]);break}case 2:{if(i===null){this.goToItem(1);break}this.setActiveItem(o[Math.max(0,i-1)]);break}case 3:{if(i===null){this.goToItem(0);break}this.setActiveItem(o[Math.min(o.length-1,i+1)]);break}case 4:break}}setActiveItem(t){f(this,O)&&f(this,O).setActiveItem(t)}clearActiveItem(){f(this,O)&&f(this,O).clearActiveItem()}getDropdown(){let t=this.closest("el-dropdown");if(!t)throw new Error("[ElMenu] No `<el-dropdown>` element found");return t}getItems(){return Array.from(this.querySelectorAll(`${ie},[role="menuitem"]`))}getActiveItem(){return f(this,O)?.getActiveItem()||null}onAttributeChange(t,o,r){switch(t){case"open":{r===null?this.hidePopover():this.showPopover();break}}}};O=new WeakMap,H(At,"observedAttributes",["anchor","open"]);T("el-menu",At);T("el-dropdown",ro);var xt=class extends x{onAttributeChange(n,t,o){switch(n){case"open":{o===null?this.hidePopover():this.showPopover();break}}}getOptionByName(n){let t=this.querySelector(`el-option[value="${n}"]`);return t||null}getItems(){return Array.from(this.querySelectorAll("el-option:not([disabled])"))}};H(xt,"observedAttributes",["anchor","open"]);var io=class extends x{};T("el-options",xt);T("el-option",io);var so=class extends x{getPopovers(){return Array.from(this.querySelectorAll("* > el-popover"))}},Tt=class extends x{mount(n){if(!this.id)throw new Error("[ElPopover] No id found for popover (ensure `id` is set)");let o=this.getButton();o.id||(o.id=k("popover-button")),De(this,()=>this.getButton(),()=>this.getButton(),n),j(o,"--button-width",n,this),this.setAttribute("tabindex","-1"),o.addEventListener("keydown",s=>{(s.key==="Enter"||s.key===" ")&&(s.preventDefault(),this.togglePopover())},{signal:n});let r=this,i=this.closest("el-popover-group");i&&i.getPopovers().includes(this)&&(r=i),wt(r,[o],this,n,()=>this.hidePopover()),this.addEventListener("toggle",s=>{let a=s;a.newState==="closed"&&a.oldState==="open"&&setTimeout(()=>{!this.contains(document.activeElement)&&document.activeElement!==document.body||o&&o!==document.activeElement&&o.isConnected&&Le(o)})},{signal:n})}getButton(){let n=this.id,t=document.querySelector(`[popovertarget="${n}"]`);if(!t)throw new Error('[ElPopover] No button found for popover (ensure you add a `<button popovertarget="${id}">` on the page)');return t}onAttributeChange(n,t,o){switch(n){case"open":{o===null?this.hidePopover():this.showPopover();break}}}};H(Tt,"observedAttributes",["anchor","open"]);T("el-popover",Tt);T("el-popover-group",so);var lo=class extends x{mount(n){let t=this.getList(),o=this.getPanels(),r=t.getTabButtons(),i=o.getPanels();if(r.length!==i.length){console.warn("[ElTabGroup] Mismatch between number of tabs and panels");return}for(let a=0;a<i.length;a++){let l=i[a],u=r[a];u.id||(u.id=k("tailwindplus-tab")),l.id||(l.id=k("tailwindplus-tab-panel")),l.setAttribute("aria-labelledby",u.id),u.setAttribute("aria-controls",l.id),u.setAttribute("role","tab")}let s=this.getActiveTab();s===-1&&(s=0),t.setActiveTab(s),o.setActivePanel(s),t.addEventListener("keydown",a=>{switch(a.key){case"ArrowLeft":{a.preventDefault();let u=this.getActiveTab()-1;u<0&&(u=r.length-1),this.setActiveTab(u),r[u].focus();break}case"ArrowRight":{a.preventDefault();let u=this.getActiveTab()+1;u>=r.length&&(u=0),this.setActiveTab(u),r[u].focus();break}case"Home":case"PageUp":{a.preventDefault(),this.setActiveTab(0),r[0].focus();break}case"End":case"PageDown":{a.preventDefault(),this.setActiveTab(r.length-1),r[r.length-1].focus();break}}},{signal:n});for(let a=0;a<r.length;a++)r[a].addEventListener("click",u=>{u.preventDefault(),this.setActiveTab(a)},{signal:n})}getActiveTab(){let n=this.querySelector("el-tab-panels"),t=n.getPanels().find(o=>!o.hasAttribute("hidden"));return t?n.getPanels().indexOf(t):-1}getList(){let n=this.querySelector("el-tab-list");if(!n)throw new Error("[ElTabGroup] No `<el-tab-list>` element found");return n}getPanels(){let n=this.querySelector("el-tab-panels");if(!n)throw new Error("[ElTabGroup] No `<el-tab-panels>` element found");return n}setActiveTab(n){if(this.getActiveTab()===n)return;let o=this.getList(),r=this.getPanels(),i=o.getTabButtons();n<0||n>=i.length||(o.setActiveTab(n),r.setActivePanel(n))}},ao=class extends x{mount(){this.setAttribute("role","tablist"),this.setAttribute("aria-orientation","horizontal")}getTabButtons(){let n=this.querySelectorAll("button");return Array.from(n)}setActiveTab(n){this.getTabButtons().forEach((o,r)=>{let i=r===n;o.setAttribute("tabindex",i?"0":"-1"),o.setAttribute("aria-selected",i?"true":"false")})}},uo=class extends x{mount(){let t=this.getTabGroup().getList(),o=this.getPanels(),r=new MutationObserver(i=>{for(let s of i){let a=s.target;switch(s.attributeName){case"hidden":if(!a.hasAttribute(s.attributeName)){let l=o.indexOf(a);t.setActiveTab(l),this.setActivePanel(l)}}}});for(let i of o)i.setAttribute("role","tabpanel"),i.setAttribute("tabindex","0"),r.observe(i,{attributeFilter:["hidden"],attributes:!0})}getTabGroup(){let n=this.closest("el-tab-group");if(!n)throw new Error("`<el-tab-panels>` must be inside of a `<el-tab-group>` element.");return n}getPanels(){return Array.from(this.children)}setActivePanel(n){this.getPanels().forEach((o,r)=>{o.toggleAttribute("hidden",r!==n)})}};T("el-tab-list",ao);T("el-tab-panels",uo);T("el-tab-group",lo);typeof globalThis.window<"u"&&setTimeout(()=>window.dispatchEvent(new Event("elements:ready")));