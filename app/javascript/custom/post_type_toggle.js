document.addEventListener('DOMContentLoaded', function() {
  const postTypeToggle = document.querySelector('[data-post-type-toggle]');
  
  if (!postTypeToggle) return;
  
  const imageRadio = postTypeToggle.querySelector('[data-post-type-option="image"]');
  const textRadio = postTypeToggle.querySelector('[data-post-type-option="text"]');
  const imageContent = document.querySelectorAll('[data-post-content="image"]');
  const textContent = document.querySelectorAll('[data-post-content="text"]');
  
  function toggleContent() {
    if (imageRadio && imageRadio.checked) {
      imageContent.forEach(el => el.style.display = 'block');
      textContent.forEach(el => el.style.display = 'none');
    } else if (textRadio && textRadio.checked) {
      imageContent.forEach(el => el.style.display = 'none');
      textContent.forEach(el => el.style.display = 'block');
    }
  }
  
  // Set initial state
  toggleContent();
  
  // Add event listeners
  if (imageRadio) {
    imageRadio.addEventListener('change', toggleContent);
  }
  if (textRadio) {
    textRadio.addEventListener('change', toggleContent);
  }
});

// Handle Turbo navigation
document.addEventListener('turbo:load', function() {
  const postTypeToggle = document.querySelector('[data-post-type-toggle]');
  
  if (!postTypeToggle) return;
  
  const imageRadio = postTypeToggle.querySelector('[data-post-type-option="image"]');
  const textRadio = postTypeToggle.querySelector('[data-post-type-option="text"]');
  const imageContent = document.querySelectorAll('[data-post-content="image"]');
  const textContent = document.querySelectorAll('[data-post-content="text"]');
  
  function toggleContent() {
    if (imageRadio && imageRadio.checked) {
      imageContent.forEach(el => el.style.display = 'block');
      textContent.forEach(el => el.style.display = 'none');
    } else if (textRadio && textRadio.checked) {
      imageContent.forEach(el => el.style.display = 'none');
      textContent.forEach(el => el.style.display = 'block');
    }
  }
  
  // Set initial state
  toggleContent();
  
  // Add event listeners
  if (imageRadio) {
    imageRadio.addEventListener('change', toggleContent);
  }
  if (textRadio) {
    textRadio.addEventListener('change', toggleContent);
  }
});