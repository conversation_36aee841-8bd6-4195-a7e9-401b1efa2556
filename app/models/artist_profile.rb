class ArtistProfile < ApplicationRecord
  extend FriendlyId
  friendly_id :slug_candidates, use: :slugged

  belongs_to :user
  has_one_attached :profile_photo
  
  # Styles
  has_many :artist_styles, dependent: :destroy
  has_many :styles, through: :artist_styles
  
  has_many :portfolio_items, dependent: :destroy

  validates :name, presence: true

  def slug_candidates
    [
      :name,
      [:name, -> { Time.current.to_i }]
    ]
  end

  def should_generate_new_friendly_id?
    name_changed? || super
  end

  def to_param
    slug
  end

  private
end
