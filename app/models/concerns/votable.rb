module Votable
  extend ActiveSupport::Concern

  included do
    has_many :votes, as: :votable, dependent: :destroy
  end

  def upvotes_count
    votes.upvotes.count
  end

  def downvotes_count
    votes.downvotes.count
  end

  def net_votes
    votes_sum
  end

  def user_vote(user)
    return nil unless user
    votes.find_by(user: user)
  end

  def voted_by?(user)
    return false unless user
    votes.exists?(user: user)
  end

  def upvoted_by?(user)
    return false unless user
    votes.exists?(user: user, vote_type: :upvote)
  end

  def downvoted_by?(user)
    return false unless user
    votes.exists?(user: user, vote_type: :downvote)
  end

  def update_votes_sum
    new_sum = votes.sum(:vote_type)
    update_column(:votes_sum, new_sum) if votes_sum != new_sum
  end

  # Scopes for sorting by votes
  module ClassMethods
    def by_votes(direction = :desc)
      order(votes_sum: direction)
    end

    def top_voted
      by_votes(:desc)
    end

    def most_controversial
      # Items with many votes but low net score
      joins(:votes)
        .group(:id)
        .having('COUNT(votes.id) > ?', 5)
        .order('ABS(votes_sum) ASC, COUNT(votes.id) DESC')
    end
  end
end