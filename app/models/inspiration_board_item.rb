class InspirationBoardItem < ApplicationRecord
  belongs_to :inspiration_board
  belongs_to :portfolio_item

  validates :inspiration_board_id, uniqueness: { 
    scope: [:portfolio_item_id], 
    message: "Portfolio item is already in this inspiration board" 
  }

  scope :with_notes, -> { where.not(notes: [nil, ""]) }
  scope :ordered, -> { order(created_at: :desc) }
  scope :recent, -> { order(created_at: :desc) }

  def image
    portfolio_item&.image
  end

  def source_artist
    portfolio_item&.artist_profile
  end

  def source
    portfolio_item
  end
end
