class ClientProfile < ApplicationRecord
  extend FriendlyId
  friendly_id :slug_candidates, use: :slugged

  belongs_to :user
  has_one_attached :profile_photo
  # has_many :inspiration_boards, dependent: :destroy  # Phase 4

  validates :name, presence: true

  def slug_candidates
    [
      :name,
      [:name, -> { Time.current.to_i }]
    ]
  end

  def should_generate_new_friendly_id?
    name_changed? || super
  end

  def to_param
    slug
  end
end
