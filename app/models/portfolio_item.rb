class PortfolioItem < ApplicationRecord
  belongs_to :artist_profile
  has_one_attached :image
  has_many :inspiration_board_items, dependent: :destroy
  has_many :portfolio_item_styles, dependent: :destroy
  has_many :styles, through: :portfolio_item_styles

  validate :image_attached

  scope :ordered, -> { order(created_at: :desc) }
  scope :by_style, ->(style_id) { joins(:styles).where(styles: { id: style_id }) }
  scope :by_location, ->(location) { joins(:artist_profile).where(artist_profiles: { location: location }) }

  after_create :process_image_variants

  private

  def image_attached
    errors.add(:image, "must be attached") unless image.attached?
  end

  def process_image_variants
    ImageProcessingJob.perform_later(image.blob) if image.attached? && image.blob.persisted?
  end
end
