class User < ApplicationRecord
  has_secure_password
  has_many :sessions, dependent: :destroy
  has_one :artist_profile, dependent: :destroy
  has_one :client_profile, dependent: :destroy
  has_many :inspiration_boards, dependent: :destroy

  enum :role, { client: 0, artist: 1 }

  normalizes :email_address, with: ->(e) { e.strip.downcase }

  validates :email_address, presence: true, uniqueness: true

  def self.find_by_email(email)
    return nil if email.blank?
    find_by(email_address: email.strip.downcase)
  end

  def profile
    artist? ? artist_profile : client_profile
  end

  def display_name
    profile&.name || email_address
  end

  def approved_or_admin?
    approved? || admin?
  end

end
