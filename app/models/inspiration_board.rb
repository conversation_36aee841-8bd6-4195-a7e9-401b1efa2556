class InspirationBoard < ApplicationRecord
  belongs_to :user
  has_many :inspiration_board_items, dependent: :destroy
  has_many :portfolio_items, through: :inspiration_board_items

  validates :name, presence: true
  validates :name, uniqueness: { scope: :user_id }

  scope :by_user, ->(user) { where(user: user) }
  scope :public_boards, -> { where(privacy: false) }
  scope :private_boards, -> { where(privacy: true) }
  scope :ordered, -> { order(created_at: :desc) }

  def public?
    !privacy
  end

  def private?
    privacy
  end
end
