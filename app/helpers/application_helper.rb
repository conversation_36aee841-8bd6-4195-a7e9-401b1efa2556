module ApplicationHelper
  def require_authentication_link(path, notice: "You need to be signed in to do this action", **options)
    if current_user
      path
    else
      new_session_path(notice: notice)
    end
  end

  def current_user_following?(artist_profile)
    return false unless current_user&.client? && current_user.approved_or_admin?
    current_user.client_profile&.follows&.exists?(artist_profile: artist_profile)
  end

  def follow_button_for(artist_profile, options = {})
    return unless current_user&.client? && current_user.approved_or_admin?
    
    css_classes = options[:class] || "px-3 py-2 rounded-md text-sm font-medium transition-colors"
    
    if current_user_following?(artist_profile)
      button_to follows_path, method: :delete, params: { artist_profile_id: artist_profile.id }, 
                class: "#{css_classes} border border-gray-300 text-gray-700 bg-white hover:bg-gray-50" do
        "Following"
      end
    else
      button_to follows_path, params: { artist_profile_id: artist_profile.id }, 
                class: "#{css_classes} border border-indigo-600 text-indigo-600 bg-white hover:bg-indigo-50" do
        "Follow"
      end
    end
  end
end
