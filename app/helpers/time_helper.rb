module <PERSON><PERSON><PERSON><PERSON>
  def short_time_ago(time)
    seconds = (Time.current - time).to_i
    
    case seconds
    when 0...60
      "#{seconds}s ago"
    when 60...3600
      "#{seconds / 60}m ago" 
    when 3600...86400
      "#{seconds / 3600}h ago"
    when 86400...2592000
      "#{seconds / 86400}d ago"
    when 2592000...31536000
      "#{seconds / 2592000}mo ago"
    else
      "#{seconds / 31536000}y ago"
    end
  end

  def abbreviated_time_ago(time)
    seconds = (Time.current - time).to_i
    
    case seconds
    when 0...60
      "#{seconds}s"
    when 60...3600
      "#{seconds / 60}m" 
    when 3600...86400
      "#{seconds / 3600}h"
    when 86400...2592000
      "#{seconds / 86400}d"
    when 2592000...31536000
      "#{seconds / 2592000}mo"
    else
      "#{seconds / 31536000}y"
    end
  end
end