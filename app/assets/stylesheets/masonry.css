/* Masonry Grid Layout Styles */
.masonry-grid {
  columns: 1;
  column-gap: 0.5rem;
}

@media (min-width: 640px) {
  .masonry-grid {
    columns: 2;
  }
}

@media (min-width: 1024px) {
  .masonry-grid {
    columns: 3;
  }
}

@media (min-width: 1536px) {
  .masonry-grid {
    columns: 4;
  }
}

.masonry-item {
  display: inline-block;
  width: 100%;
  break-inside: avoid;
  margin-bottom: 0.5rem;
}

/* Additional responsive breakpoints for custom column counts */
@media (min-width: 1920px) {
  .masonry-grid {
    columns: 4;
  }
}

@media (min-width: 2400px) {
  .masonry-grid {
    columns: 5;
  }
}

/* Masonry variations for different layouts */
.masonry-grid-sm {
  columns: 1;
  column-gap: 1rem;
}

@media (min-width: 640px) {
  .masonry-grid-sm {
    columns: 2;
  }
}

@media (min-width: 1024px) {
  .masonry-grid-sm {
    columns: 3;
  }
}

.masonry-grid-lg {
  columns: 1;
  column-gap: 2rem;
}

@media (min-width: 768px) {
  .masonry-grid-lg {
    columns: 2;
  }
}

@media (min-width: 1024px) {
  .masonry-grid-lg {
    columns: 3;
  }
}

@media (min-width: 1280px) {
  .masonry-grid-lg {
    columns: 4;
  }
}

@media (min-width: 1600px) {
  .masonry-grid-lg {
    columns: 5;
  }
}

@media (min-width: 1920px) {
  .masonry-grid-lg {
    columns: 6;
  }
}

@media (min-width: 2400px) {
  .masonry-grid-lg {
    columns: 7;
  }
}