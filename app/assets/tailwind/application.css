@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

html {
  scroll-behavior: smooth;
}

/* Card component styles - light mode: shadow, dark mode: outline only */
.card {
  @apply bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm dark:shadow-none;
}

.card:hover {
  @apply shadow-md dark:shadow-none;
}

/* Global utility for consistent card styling */
.card-style {
  @apply bg-white dark:bg-black border border-gray-300 dark:border-gray-600 shadow-sm dark:shadow-none;
}

.card-style:hover {
  @apply shadow-md dark:shadow-none;
}

/* Command Palette - Hide scrollbar */
[data-command-palette-target="list"] {
  /* Hide scrollbar for WebKit browsers */
  -webkit-scrollbar: none;
  /* Hide scrollbar for Firefox */
  scrollbar-width: none;
  /* Hide scrollbar for IE and Edge */
  -ms-overflow-style: none;
}

/* Threaded Comments - Visual Threading Lines */
.comment .thread-line {
  @apply bg-gray-200 dark:bg-gray-700;
}

/* Collapse button animation and styling */
.collapse-btn .collapse-icon {
  transition: transform 0.2s ease;
}

.collapse-btn[data-collapsed="true"] .collapse-icon {
  transform: rotate(-90deg);
}

/* Hide threading lines when collapsed */
.replies.collapsed {
  display: none;
}

/* Threading line dynamic height adjustment */
.thread-line {
  transition: height 0.2s ease;
}

/* Animated dots for loading states */
@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

.animate-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

/* Portfolio Card Hover Animations */
/* Animation 1: Glow - border becomes brighter white */
.hover-glow {
  transition: border-color 0.3s ease;
}

.dark .hover-glow:hover {
  border-color: white;
}

/* Animation 2: Pulse - heartbeat effect with border flashing */
@keyframes pulse-border {
  0%, 100% {
    border-color: rgb(75 85 99); /* gray-600 */
  }
  50% {
    border-color: white;
  }
}

.dark .hover-pulse:hover {
  animation: pulse-border 1.5s ease-in-out infinite;
}

/* Animation 3: Lift - translate up with white border */
.hover-lift {
  transition: transform 0.3s ease, border-color 0.3s ease;
}

.dark .hover-lift:hover {
  transform: translateY(-4px);
  border-color: white;
}
