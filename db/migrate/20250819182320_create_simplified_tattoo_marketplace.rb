class CreateSimplifiedTattooMarketplace < ActiveRecord::Migration[8.0]
  def change
    # This migration creates the complete simplified schema for the tattoo marketplace
    # It consolidates all necessary tables without the eliminated features:
    # - No posts (removed)
    # - No flash_items (removed) 
    # - No waitlist_entries (removed)
    # - No artist_specialties (simplified to just styles)
    # - No availability/waitlist_enabled fields (removed)
    
    # ===== CORE USER SYSTEM =====
    
    # Users table - core authentication
    create_table :users do |t|
      t.string :email_address, null: false
      t.string :password_digest, null: false
      t.integer :role, default: 0, null: false  # 0: client, 1: artist
      t.boolean :approved, default: false, null: false
      t.boolean :admin, default: false, null: false
      t.timestamps
      
      t.index :email_address, unique: true
    end
    
    # Sessions table
    create_table :sessions do |t|
      t.references :user, null: false, foreign_key: true
      t.string :token, null: false
      t.string :ip_address
      t.string :user_agent
      t.datetime :created_at, null: false
      
      t.index :token, unique: true
    end
    
    # ===== PROFILE SYSTEM =====
    
    # Artist profiles table (simplified)
    create_table :artist_profiles do |t|
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false
      t.string :slug, null: false
      t.text :biography
      t.string :location
      t.string :contact_email
      t.string :instagram_url
      t.string :website_url
      t.string :booking_link
      t.string :studio_link
      t.timestamps
      
      t.index :slug, unique: true
      t.index :user_id, unique: true
    end
    
    # Client profiles table
    create_table :client_profiles do |t|
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false
      t.string :slug, null: false
      t.string :location
      t.timestamps
      
      t.index :slug, unique: true
      t.index :user_id, unique: true
    end
    
    # ===== STYLE SYSTEM (SIMPLIFIED) =====
    
    # Styles table (no more specialties - just unlimited styles)
    create_table :styles do |t|
      t.string :title, null: false
      t.timestamps
      
      t.index :title, unique: true
    end
    
    # Artist styles join table (many-to-many)
    create_table :artist_styles do |t|
      t.references :artist_profile, null: false, foreign_key: true
      t.references :style, null: false, foreign_key: true
      t.timestamps
      
      t.index [:artist_profile_id, :style_id], unique: true
    end
    
    # ===== PORTFOLIO SYSTEM =====
    
    # Portfolio items table (core artist showcase)
    create_table :portfolio_items do |t|
      t.references :artist_profile, null: false, foreign_key: true
      t.text :caption
      t.timestamps
    end
    
    # Inspiration boards (Pinterest-like collections)
    create_table :inspiration_boards do |t|
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false
      t.boolean :privacy, default: false, null: false  # false = public, true = private
      t.timestamps
      
      t.index [:user_id, :name], unique: true
    end
    
    # Inspiration board items
    create_table :inspiration_board_items do |t|
      t.references :inspiration_board, null: false, foreign_key: true
      t.references :portfolio_item, foreign_key: true
      t.timestamps
    end
    
    # ===== ADMIN/SYSTEM TABLES =====
    
    # Site settings (configurable features)
    create_table :site_settings do |t|
      t.boolean :approval_required, default: true, null: false
      t.string :site_name, default: 'Tattoo Marketplace'
      t.string :contact_email
      t.text :site_description
      t.timestamps
    end
    
    # FriendlyId slugs (for SEO-friendly URLs)
    create_table :friendly_id_slugs do |t|
      t.string :slug, null: false
      t.integer :sluggable_id, null: false
      t.string :sluggable_type, limit: 50
      t.string :scope
      t.datetime :created_at
      
      t.index [:slug, :sluggable_type, :scope], unique: true
      t.index [:slug, :sluggable_type]
      t.index [:sluggable_id]
      t.index [:sluggable_type]
    end
    
    # ===== ACTIVE STORAGE TABLES =====
    # (For file uploads - profile photos, portfolio images, etc.)
    
    create_table :active_storage_blobs do |t|
      t.string :key, null: false
      t.string :filename, null: false
      t.string :content_type
      t.text :metadata
      t.string :service_name, null: false
      t.bigint :byte_size, null: false
      t.string :checksum
      t.datetime :created_at, null: false
      
      t.index :key, unique: true
    end
    
    create_table :active_storage_attachments do |t|
      t.string :name, null: false
      t.string :record_type, null: false
      t.bigint :record_id, null: false
      t.bigint :blob_id, null: false
      t.datetime :created_at, null: false
      
      t.index :blob_id
      t.index [:record_type, :record_id, :name, :blob_id], unique: true, name: :index_active_storage_attachments_uniqueness
    end
    
    create_table :active_storage_variant_records do |t|
      t.bigint :blob_id, null: false
      t.string :variation_digest, null: false
      
      t.index [:blob_id, :variation_digest], unique: true, name: :index_active_storage_variant_records_uniqueness
    end
    
    # ===== ACTION TEXT TABLES =====
    # (For rich text content)
    
    create_table :action_text_rich_texts do |t|
      t.string :name, null: false
      t.text :body
      t.string :record_type, null: false
      t.bigint :record_id, null: false
      t.datetime :created_at, null: false
      t.datetime :updated_at, null: false
      
      t.index [:record_type, :record_id, :name], unique: true, name: :index_action_text_rich_texts_uniqueness
    end
    
    # ===== FOREIGN KEY CONSTRAINTS =====
    
    add_foreign_key :active_storage_attachments, :active_storage_blobs, column: :blob_id
    add_foreign_key :active_storage_variant_records, :active_storage_blobs, column: :blob_id
  end
end
