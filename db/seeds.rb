# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🎨 Seeding Simplified Tattoo Marketplace..."

# Helper function to determine gender from name and assign appropriate headshot
def assign_headshot_by_gender(user, name)
  # Get available headshots - check both artist_headshots and regular headshots
  if user.role == 'artist'
    headshots_dir = Rails.root.join('db', 'starter', 'artist_headshots')
  else
    headshots_dir = Rails.root.join('db', 'starter', 'headshots')
  end
  
  female_headshots = Dir.glob(File.join(headshots_dir, 'female-*.{jpg,jpeg,webp}')).sort
  male_headshots = Dir.glob(File.join(headshots_dir, 'male-*.{jpg,jpeg,webp}')).sort
  
  return unless female_headshots.any? || male_headshots.any?
  
  # Common female names for gender detection
  female_names = %w[
    maria sakura jane sarah emily ashley lisa alex emma olivia sophia ava mia isabella
    charlotte amelia harper evelyn abigail emily madison elizabeth sofia avery ella
    scarlett grace chloe victoria riley aria lily aubrey zoe penelope hannah layla
    nora lily ellie violet mila hazel elena naomi maya lucy kennedy raven luna phoenix
    storm sage ember ivy willow nova jade
  ]
  
  # Determine gender based on name
  first_name = name.split.first&.downcase
  is_female = female_names.include?(first_name)
  
  # Select appropriate headshot
  if is_female && female_headshots.any?
    headshot_path = female_headshots.sample
  elsif !is_female && male_headshots.any?
    headshot_path = male_headshots.sample
  else
    # Fallback to any available headshot
    all_headshots = female_headshots + male_headshots
    headshot_path = all_headshots.sample if all_headshots.any?
  end
  
  # Attach headshot if available
  if headshot_path && File.exist?(headshot_path)
    begin
      case user.role
      when 'artist'
        profile = user.artist_profile
      when 'client'  
        profile = user.client_profile
      end
      
      if profile && profile.respond_to?(:profile_photo)
        profile.profile_photo.attach(
          io: File.open(headshot_path),
          filename: File.basename(headshot_path),
          content_type: case File.extname(headshot_path).downcase
                       when '.jpg', '.jpeg' then 'image/jpeg'
                       when '.webp' then 'image/webp'
                       else 'image/jpeg'
                       end
        )
        puts "   📸 Assigned #{File.basename(headshot_path)} to #{name}"
      end
    rescue => e
      puts "   ⚠️ Failed to assign headshot to #{name}: #{e.message}"
    end
  end
end

# Create default site settings
SiteSetting.find_or_create_by!(id: 1) do |setting|
  setting.approval_required = true
  setting.site_name = 'Tattoo Marketplace'
end

puts "✅ Site settings created"

# Create styles  
styles_data = [
  "Traditional",
  "Japanese", 
  "Realism",
  "Watercolor",
  "Tribal",
  "New School",
  "Neo Traditional", 
  "Blackwork",
  "Dotwork",
  "Geometric",
  "Portrait",
  "Biomechanical",
  "Surrealism",
  "Minimalist"
]

styles_data.each do |style_title|
  Style.find_or_create_by!(title: style_title)
end

puts "✅ Created #{Style.count} styles"

# Create admin user
admin_user = User.find_or_create_by!(email_address: "<EMAIL>") do |user|
  user.password = "password123"
  user.password_confirmation = "password123"
  user.role = "client"
  user.admin = true
  user.approved = true
end

# Create admin client profile
unless admin_user.client_profile
  admin_user.create_client_profile!(
    name: "Admin User"
  )
  
  # Assign headshot for admin user (gender neutral)
  assign_headshot_by_gender(admin_user, "Admin User")
end

puts "✅ Created admin user"

# Create sample artists
sample_artists = [
  {
    email: "<EMAIL>",
    name: "Raven Blackthorne",
    location: "Brooklyn, NY",
    bio: "Dark traditional artist with a passion for occult imagery and gothic designs. 15+ years of creating bold pieces that blend classic American traditional with modern darkness.",
    styles: ["Traditional"],
    contact_email: "<EMAIL>",
    instagram: "https://instagram.com/raven_blackthorne",
    website: "https://ravenblackthornetattoos.com",
    studio_link: "https://instagram.com/brooklyn_shadow_studio"
  },
  {
    email: "<EMAIL>", 
    name: "Kenzo Storm",
    location: "Los Angeles, CA",
    bio: "Japanese tattoo master trained in traditional Irezumi techniques. Blending ancient artistry with contemporary vision to create stunning large-scale pieces.",
    styles: ["Japanese", "Blackwork"],
    contact_email: "<EMAIL>",
    instagram: "https://instagram.com/kenzo_storm_ink",
    studio_link: "https://la-dragon-studio.com"
  },
  {
    email: "<EMAIL>",
    name: "Sage Wilde",
    location: "Austin, TX",
    bio: "Fine line minimalist specializing in botanical and geometric designs. Creating delicate artwork that speaks to the soul through precision and simplicity.",
    styles: ["Minimalist"],
    contact_email: "<EMAIL>",
    instagram: "https://instagram.com/sage_wilde_ink",
    website: "https://sagewildetattoo.com"
  },
  {
    email: "<EMAIL>",
    name: "Luna Vega", 
    location: "Miami, FL",
    bio: "Surrealist and realism specialist known for creating photorealistic portraits with dreamlike elements. Each piece captures the essence of otherworldly beauty.",
    styles: ["Realism"],
    contact_email: "<EMAIL>"
  },
  {
    email: "<EMAIL>",
    name: "Phoenix Cross",
    location: "Seattle, WA", 
    bio: "Biomechanical innovator pushing the boundaries between flesh and machine. Creating cyberpunk-inspired pieces that blur the line between reality and fantasy.",
    styles: ["Biomechanical", "New School"],
    contact_email: "<EMAIL>",
    instagram: "https://instagram.com/phoenix_cross_ink"
  }
]

created_artists = []
sample_artists.each do |artist_data|
  user = User.find_or_create_by!(email_address: artist_data[:email]) do |u|
    u.password = "password123"
    u.password_confirmation = "password123"
    u.role = "artist"
    u.approved = true
  end

  unless user.artist_profile
    profile = user.create_artist_profile!(
      name: artist_data[:name],
      biography: artist_data[:bio],
      location: artist_data[:location],
      contact_email: artist_data[:contact_email],
      instagram_url: artist_data[:instagram],
      website_url: artist_data[:website],
      studio_link: artist_data[:studio_link]
    )

    # Add styles using the new artist_styles join table
    artist_data[:styles]&.each do |style_title|
      style = Style.find_by(title: style_title)
      if style
        ArtistStyle.find_or_create_by!(
          artist_profile: profile,
          style: style
        )
      end
    end

    # Assign headshot based on gender
    assign_headshot_by_gender(user, artist_data[:name])

    created_artists << user
  end
end

puts "✅ Created #{created_artists.length} sample artists"

# Create sample clients
sample_clients = [
  {
    email: "<EMAIL>",
    name: "John Doe",
    location: "New York, NY"
  },
  {
    email: "<EMAIL>", 
    name: "Jane Smith",
    location: "San Francisco, CA"
  },
  {
    email: "<EMAIL>",
    name: "Bob Wilson",
    location: "Chicago, IL"
  },
  {
    email: "<EMAIL>",
    name: "Sarah Johnson", 
    location: "Portland, OR"
  },
  {
    email: "<EMAIL>",
    name: "Emily Davis",
    location: "Denver, CO"
  },
  {
    email: "<EMAIL>",
    name: "Chris Miller",
    location: "Phoenix, AZ"
  },
  {
    email: "<EMAIL>",
    name: "Ashley Brown",
    location: "Nashville, TN"
  },
  {
    email: "<EMAIL>",
    name: "Michael Jones",
    location: "Las Vegas, NV"
  },
  {
    email: "<EMAIL>",
    name: "Lisa Garcia",
    location: "San Diego, CA"
  },
  {
    email: "<EMAIL>",
    name: "Ryan Martinez",
    location: "Houston, TX"
  }
]

created_clients = []
sample_clients.each do |client_data|
  user = User.find_or_create_by!(email_address: client_data[:email]) do |u|
    u.password = "password123"
    u.password_confirmation = "password123"
    u.role = "client"
    u.approved = true
  end

  unless user.client_profile
    user.create_client_profile!(
      name: client_data[:name],
      location: client_data[:location]
    )
    
    # Assign headshot based on gender
    assign_headshot_by_gender(user, client_data[:name])
    
    created_clients << user
  end
end

puts "✅ Created #{created_clients.length} sample clients"

# Get available tattoo images for portfolio items
tattoo_images_dir = Rails.root.join('db', 'starter', 'tattoos')
tattoo_images = Dir.glob(File.join(tattoo_images_dir, '*.jpg')).sort

puts "📸 Found #{tattoo_images.length} tattoo images"

# Create portfolio items for all artists
created_portfolio_items = []
if created_artists.any? && tattoo_images.any?
  remaining_images = tattoo_images.shuffle
  
  # Portfolio item descriptions
  portfolio_descriptions = [
    "One of my favorite pieces from this year - the detail work really came together beautifully.",
    "This sleeve took 3 sessions but was absolutely worth it. Love how the shading turned out.",
    "Fresh off the needle! This piece combines traditional elements with modern execution.",
    "Portrait work is always challenging but so rewarding when it captures the subject perfectly.",
    "This geometric mandala was a dream to create - precision and flow in perfect harmony.",
    "Japanese traditional work requires patience and respect for the art form. This dragon piece tells a story.",
    "Sometimes simple is best - this minimalist design speaks volumes with clean lines.",
    "Biomechanical fusion where organic meets mechanical. Pushing creative boundaries.",
    "Dotwork requires incredible patience but creates such unique texture and depth.",
    "This tribal piece honors traditional patterns while adapting to modern placement.",
    "The symbolism in this piece runs deep - every element has meaning for the client.",
    "Custom work like this is why I love being a tattoo artist. Pure collaboration.",
    "This blackwork required steady hands and patience, but the result speaks for itself.",
    "Watercolor techniques in tattooing create such unique, flowing artwork.",
    "Fine line work demands precision, but creates such delicate, beautiful results.",
    "This neo-traditional piece blends classic with contemporary perfectly.",
    "Realism work challenges me every time, but capturing life in ink is magical.",
    "The healing process on this piece was perfect - client took excellent care.",
    "Abstract work allows for so much creative freedom and artistic expression.",
    "This cover-up transformation exceeded all expectations - new life from old ink."
  ]
  
  created_artists.each_with_index do |artist, artist_index|
    next unless artist.artist_profile
    
    # Create 3-6 portfolio items per artist
    portfolio_item_count = 3 + (artist_index % 4) # 3, 4, 5, or 6 items
    portfolio_item_count.times do |i|
      break if i >= remaining_images.length
      
      image_path = remaining_images[i + (artist_index * 6)] # Distribute images across artists
      next unless image_path && File.exist?(image_path)
      
      portfolio_item = PortfolioItem.new(
        artist_profile: artist.artist_profile,
        caption: portfolio_descriptions.sample
      )
      
      # Attach tattoo image
      portfolio_item.image.attach(
        io: File.open(image_path),
        filename: File.basename(image_path),
        content_type: "image/jpeg"
      )
      
      if portfolio_item.save
        created_portfolio_items << portfolio_item
      end
    end
  end
  
  puts "✅ Created #{created_portfolio_items.length} portfolio items for artists"
end

# Assign styles to portfolio items based on their artist's styles
created_portfolio_items.each do |portfolio_item|
  artist_styles = portfolio_item.artist_profile.styles
  if artist_styles.any?
    # Assign 1-2 styles from the artist's styles to each portfolio item
    styles_to_assign = artist_styles.sample(1 + rand(2))
    styles_to_assign.each do |style|
      PortfolioItemStyle.find_or_create_by!(
        portfolio_item: portfolio_item,
        style: style
      )
    end
  end
end

puts "✅ Assigned styles to #{created_portfolio_items.length} portfolio items"

# Create some inspiration boards with portfolio content
if created_clients.any? && created_portfolio_items.any?
  board_names = [
    "Sleeve Ideas", "Small Tattoos", "Traditional Inspiration", "Japanese Style", 
    "Geometric Patterns", "Portrait Ideas", "Minimalist Designs", "Blackwork Collection",
    "Future Tattoos", "Artist References", "Color Inspiration", "Fine Line Work",
    "Cover-up Ideas", "Matching Tattoos"
  ]

  created_clients.each do |client|
    next unless client.client_profile

    # Create 2-4 inspiration boards per client
    boards_to_create = board_names.sample(2 + rand(3))
    boards_to_create.each_with_index do |board_name, index|
      board = InspirationBoard.find_or_create_by!(
        user: client,
        name: board_name,
        privacy: rand(2) == 0 # Randomly public or private
      )

      # Add portfolio items to the boards
      available_portfolio_items = created_portfolio_items.sample(rand(2..5))
      available_portfolio_items.each do |portfolio_item|
        InspirationBoardItem.find_or_create_by!(
          inspiration_board: board,
          portfolio_item: portfolio_item
        )
      end
    end
  end

  puts "✅ Created inspiration boards with portfolio content"
end


puts "\n🎉 Seeding complete!"

# Count headshots assigned
headshots_dir = Rails.root.join('db', 'starter', 'headshots')
available_headshots = Dir.glob(File.join(headshots_dir, '*.{jpg,jpeg,webp}')).length
profiles_with_avatars = ArtistProfile.joins(:profile_photo_attachment).count + ClientProfile.joins(:profile_photo_attachment).count

puts "\n📊 Summary:"
puts "   • #{User.count} total users (#{User.where(role: 'artist').count} artists, #{User.where(role: 'client').count} clients)"
puts "   • #{profiles_with_avatars}/#{ArtistProfile.count + ClientProfile.count} profiles have headshots assigned (#{available_headshots} available)"
puts "   • #{ArtistProfile.count} artist profiles"
puts "   • #{ClientProfile.count} client profiles"
puts "   • #{Style.count} styles"
puts "   • #{PortfolioItem.count} portfolio items"
puts "   • #{InspirationBoard.count} inspiration boards with #{InspirationBoardItem.count} saved items"

puts "\n👤 Test Accounts:"
puts "   Admin: <EMAIL> / password123"
puts "   Artist: <EMAIL> / password123 (Raven Blackthorne - Dark Traditional)"
puts "   Artist: <EMAIL> / password123 (Kenzo Storm - Japanese/Irezumi)"
puts "   Artist: <EMAIL> / password123 (Sage Wilde - Minimalist/Geometric)"
puts "   Artist: <EMAIL> / password123 (Luna Vega - Surreal Realism)"
puts "   Artist: <EMAIL> / password123 (Phoenix Cross - Biomechanical)"
puts "   Client: <EMAIL> / password123 (John Doe)"
puts "   Client: <EMAIL> / password123 (Jane Smith)"
puts "   Client: <EMAIL> / password123 (Ashley Brown)"
puts "   Client: <EMAIL> / password123 (Michael Jones)"

puts "\n🔧 Site Settings:"
puts "   • Approval required: #{SiteSetting.current.approval_required}"
puts "   • Site name: #{SiteSetting.current.site_name}"

puts "\n✨ Ready to explore the simplified tattoo marketplace!"