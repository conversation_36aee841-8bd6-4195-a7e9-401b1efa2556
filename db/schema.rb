# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_08_19_212621) do
  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "artist_profiles", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "name", null: false
    t.string "slug", null: false
    t.text "biography"
    t.string "location"
    t.string "contact_email"
    t.string "instagram_url"
    t.string "website_url"
    t.string "booking_link"
    t.boolean "available", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "studio_link"
    t.boolean "waitlist_enabled", default: false, null: false
    t.index ["slug"], name: "index_artist_profiles_on_slug", unique: true
    t.index ["user_id"], name: "index_artist_profiles_on_user_id"
  end

  create_table "artist_specialties", force: :cascade do |t|
    t.integer "artist_profile_id", null: false
    t.integer "style_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["artist_profile_id", "style_id"], name: "index_artist_specialties_on_artist_profile_id_and_style_id", unique: true
    t.index ["artist_profile_id"], name: "index_artist_specialties_on_artist_profile_id"
    t.index ["style_id"], name: "index_artist_specialties_on_style_id"
  end

  create_table "artist_styles", force: :cascade do |t|
    t.integer "artist_profile_id", null: false
    t.integer "style_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["artist_profile_id", "style_id"], name: "index_artist_styles_on_artist_profile_id_and_style_id", unique: true
    t.index ["artist_profile_id"], name: "index_artist_styles_on_artist_profile_id"
    t.index ["style_id"], name: "index_artist_styles_on_style_id"
  end

  create_table "client_profiles", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "name", null: false
    t.string "slug", null: false
    t.string "location"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["slug"], name: "index_client_profiles_on_slug", unique: true
    t.index ["user_id"], name: "index_client_profiles_on_user_id"
  end

  create_table "flash_items", force: :cascade do |t|
    t.integer "artist_profile_id", null: false
    t.text "caption"
    t.integer "position", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["artist_profile_id"], name: "index_flash_items_on_artist_profile_id"
    t.index ["position"], name: "index_flash_items_on_position"
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string "slug", null: false
    t.integer "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.index ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true
    t.index ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type"
    t.index ["sluggable_type", "sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_type_and_sluggable_id"
  end

  create_table "inspiration_board_items", force: :cascade do |t|
    t.integer "inspiration_board_id", null: false
    t.integer "post_id"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "portfolio_item_id"
    t.integer "position"
    t.index ["inspiration_board_id", "position"], name: "idx_on_inspiration_board_id_position_71dea66309"
    t.index ["inspiration_board_id"], name: "index_inspiration_board_items_on_inspiration_board_id"
    t.index ["portfolio_item_id"], name: "index_inspiration_board_items_on_portfolio_item_id"
    t.index ["post_id"], name: "index_inspiration_board_items_on_post_id"
    t.check_constraint "(post_id IS NOT NULL AND portfolio_item_id IS NULL) OR (post_id IS NULL AND portfolio_item_id IS NOT NULL)", name: "one_source_required"
  end

  create_table "inspiration_boards", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "privacy", default: true, null: false
    t.integer "position"
    t.index ["privacy"], name: "index_inspiration_boards_on_privacy"
    t.index ["user_id", "position"], name: "index_inspiration_boards_on_user_id_and_position"
    t.index ["user_id"], name: "index_inspiration_boards_on_user_id"
  end

  create_table "portfolio_item_styles", force: :cascade do |t|
    t.integer "portfolio_item_id", null: false
    t.integer "style_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["portfolio_item_id", "style_id"], name: "index_portfolio_item_styles_on_portfolio_item_id_and_style_id", unique: true
    t.index ["portfolio_item_id"], name: "index_portfolio_item_styles_on_portfolio_item_id"
    t.index ["style_id"], name: "index_portfolio_item_styles_on_style_id"
  end

  create_table "portfolio_items", force: :cascade do |t|
    t.integer "artist_profile_id", null: false
    t.text "caption"
    t.integer "position", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["artist_profile_id", "position"], name: "index_portfolio_items_on_artist_profile_id_and_position"
    t.index ["artist_profile_id"], name: "index_portfolio_items_on_artist_profile_id"
    t.index ["position"], name: "index_portfolio_items_on_position"
  end

  create_table "posts", force: :cascade do |t|
    t.integer "artist_profile_id", null: false
    t.text "caption"
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "in_portfolio", default: false, null: false
    t.boolean "hidden", default: false
    t.integer "post_type", default: 0, null: false
    t.text "body"
    t.index ["artist_profile_id", "published_at"], name: "index_posts_on_artist_profile_id_and_published_at"
    t.index ["artist_profile_id"], name: "index_posts_on_artist_profile_id"
    t.index ["in_portfolio"], name: "index_posts_on_in_portfolio"
    t.index ["post_type"], name: "index_posts_on_post_type"
    t.index ["published_at"], name: "index_posts_on_published_at"
  end

  create_table "sessions", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "simplified_tattoo_marketplaces", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "site_settings", force: :cascade do |t|
    t.boolean "approval_required", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "require_approval", default: true
    t.boolean "allow_public_registration", default: true
    t.boolean "auto_approve_posts", default: true
    t.boolean "enable_comments", default: true
    t.boolean "enable_messaging", default: true
    t.boolean "enable_following", default: true
    t.boolean "enable_inspiration_boards", default: true
    t.boolean "enable_public_profiles", default: true
    t.string "site_name", default: "Tattoo Marketplace"
    t.string "contact_email"
    t.text "site_description"
  end

  create_table "styles", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "users", force: :cascade do |t|
    t.string "email_address", null: false
    t.string "password_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "role", default: 0, null: false
    t.boolean "admin", default: false, null: false
    t.boolean "approved", default: false, null: false
    t.string "username"
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
    t.index ["username"], name: "index_users_on_username", unique: true
  end

  create_table "waitlist_entries", force: :cascade do |t|
    t.integer "artist_profile_id", null: false
    t.string "name", null: false
    t.string "email", null: false
    t.string "phone_number"
    t.text "message"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["artist_profile_id", "created_at"], name: "index_waitlist_entries_on_artist_profile_id_and_created_at"
    t.index ["artist_profile_id", "status"], name: "index_waitlist_entries_on_artist_profile_id_and_status"
    t.index ["artist_profile_id"], name: "index_waitlist_entries_on_artist_profile_id"
    t.index ["email", "artist_profile_id"], name: "index_waitlist_entries_on_email_and_artist_profile_id", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "artist_profiles", "users"
  add_foreign_key "artist_specialties", "artist_profiles"
  add_foreign_key "artist_specialties", "styles"
  add_foreign_key "artist_styles", "artist_profiles"
  add_foreign_key "artist_styles", "styles"
  add_foreign_key "client_profiles", "users"
  add_foreign_key "flash_items", "artist_profiles"
  add_foreign_key "inspiration_board_items", "inspiration_boards"
  add_foreign_key "inspiration_board_items", "posts"
  add_foreign_key "inspiration_boards", "users"
  add_foreign_key "portfolio_item_styles", "portfolio_items"
  add_foreign_key "portfolio_item_styles", "styles"
  add_foreign_key "portfolio_items", "artist_profiles"
  add_foreign_key "posts", "artist_profiles"
  add_foreign_key "sessions", "users"
  add_foreign_key "waitlist_entries", "artist_profiles"
end
